// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/web_key_map_dart.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

/// Maps Web KeyboardEvent keys to the matching LogicalKeyboardKey id.
const Map<String, int> kWebToLogicalKey = <String, int>{
  'AVRInput': 0x00100000d08,
  'AVRPower': 0x00100000d09,
  'Accel': 0x00100000101,
  'Accept': 0x00100000501,
  'Again': 0x00100000502,
  'AllCandidates': 0x00100000701,
  'Alphanumeric': 0x00100000702,
  'AltGraph': 0x00100000103,
  'AppSwitch': 0x00100001001,
  'ArrowDown': 0x00100000301,
  'ArrowLeft': 0x00100000302,
  'ArrowRight': 0x00100000303,
  'ArrowUp': 0x00100000304,
  'Attn': 0x00100000503,
  'AudioBalanceLeft': 0x00100000d01,
  'AudioBalanceRight': 0x00100000d02,
  'AudioBassBoostDown': 0x00100000d03,
  'AudioBassBoostToggle': 0x00100000e02,
  'AudioBassBoostUp': 0x00100000d04,
  'AudioFaderFront': 0x00100000d05,
  'AudioFaderRear': 0x00100000d06,
  'AudioSurroundModeNext': 0x00100000d07,
  'AudioTrebleDown': 0x00100000e04,
  'AudioTrebleUp': 0x00100000e05,
  'AudioVolumeDown': 0x00100000a0f,
  'AudioVolumeMute': 0x00100000a11,
  'AudioVolumeUp': 0x00100000a10,
  'Backspace': 0x00100000008,
  'BrightnessDown': 0x00100000601,
  'BrightnessUp': 0x00100000602,
  'BrowserBack': 0x00100000c01,
  'BrowserFavorites': 0x00100000c02,
  'BrowserForward': 0x00100000c03,
  'BrowserHome': 0x00100000c04,
  'BrowserRefresh': 0x00100000c05,
  'BrowserSearch': 0x00100000c06,
  'BrowserStop': 0x00100000c07,
  'Call': 0x00100001002,
  'Camera': 0x00100000603,
  'CameraFocus': 0x00100001003,
  'Cancel': 0x00100000504,
  'CapsLock': 0x00100000104,
  'ChannelDown': 0x00100000d0a,
  'ChannelUp': 0x00100000d0b,
  'Clear': 0x00100000401,
  'Close': 0x00100000a01,
  'ClosedCaptionToggle': 0x00100000d12,
  'CodeInput': 0x00100000703,
  'ColorF0Red': 0x00100000d0c,
  'ColorF1Green': 0x00100000d0d,
  'ColorF2Yellow': 0x00100000d0e,
  'ColorF3Blue': 0x00100000d0f,
  'ColorF4Grey': 0x00100000d10,
  'ColorF5Brown': 0x00100000d11,
  'Compose': 0x00100000704,
  'ContextMenu': 0x00100000505,
  'Convert': 0x00100000705,
  'Copy': 0x00100000402,
  'CrSel': 0x00100000403,
  'Cut': 0x00100000404,
  'DVR': 0x00100000d4f,
  'Delete': 0x0010000007f,
  'Dimmer': 0x00100000d13,
  'DisplaySwap': 0x00100000d14,
  'Eisu': 0x00100000714,
  'Eject': 0x00100000604,
  'End': 0x00100000305,
  'EndCall': 0x00100001004,
  'Enter': 0x0010000000d,
  'EraseEof': 0x00100000405,
  'Esc': 0x0010000001b,
  'Escape': 0x0010000001b,
  'ExSel': 0x00100000406,
  'Execute': 0x00100000506,
  'Exit': 0x00100000d15,
  'F1': 0x00100000801,
  'F10': 0x0010000080a,
  'F11': 0x0010000080b,
  'F12': 0x0010000080c,
  'F13': 0x0010000080d,
  'F14': 0x0010000080e,
  'F15': 0x0010000080f,
  'F16': 0x00100000810,
  'F17': 0x00100000811,
  'F18': 0x00100000812,
  'F19': 0x00100000813,
  'F2': 0x00100000802,
  'F20': 0x00100000814,
  'F21': 0x00100000815,
  'F22': 0x00100000816,
  'F23': 0x00100000817,
  'F24': 0x00100000818,
  'F3': 0x00100000803,
  'F4': 0x00100000804,
  'F5': 0x00100000805,
  'F6': 0x00100000806,
  'F7': 0x00100000807,
  'F8': 0x00100000808,
  'F9': 0x00100000809,
  'FavoriteClear0': 0x00100000d16,
  'FavoriteClear1': 0x00100000d17,
  'FavoriteClear2': 0x00100000d18,
  'FavoriteClear3': 0x00100000d19,
  'FavoriteRecall0': 0x00100000d1a,
  'FavoriteRecall1': 0x00100000d1b,
  'FavoriteRecall2': 0x00100000d1c,
  'FavoriteRecall3': 0x00100000d1d,
  'FavoriteStore0': 0x00100000d1e,
  'FavoriteStore1': 0x00100000d1f,
  'FavoriteStore2': 0x00100000d20,
  'FavoriteStore3': 0x00100000d21,
  'FinalMode': 0x00100000706,
  'Find': 0x00100000507,
  'Fn': 0x00100000106,
  'FnLock': 0x00100000107,
  'GoBack': 0x00100001005,
  'GoHome': 0x00100001006,
  'GroupFirst': 0x00100000707,
  'GroupLast': 0x00100000708,
  'GroupNext': 0x00100000709,
  'GroupPrevious': 0x0010000070a,
  'Guide': 0x00100000d22,
  'GuideNextDay': 0x00100000d23,
  'GuidePreviousDay': 0x00100000d24,
  'HangulMode': 0x00100000711,
  'HanjaMode': 0x00100000712,
  'Hankaku': 0x00100000715,
  'HeadsetHook': 0x00100001007,
  'Help': 0x00100000508,
  'Hibernate': 0x00100000609,
  'Hiragana': 0x00100000716,
  'HiraganaKatakana': 0x00100000717,
  'Home': 0x00100000306,
  'Hyper': 0x00100000108,
  'Info': 0x00100000d25,
  'Insert': 0x00100000407,
  'InstantReplay': 0x00100000d26,
  'JunjaMode': 0x00100000713,
  'KanaMode': 0x00100000718,
  'KanjiMode': 0x00100000719,
  'Katakana': 0x0010000071a,
  'Key11': 0x00100001201,
  'Key12': 0x00100001202,
  'LastNumberRedial': 0x00100001008,
  'LaunchApplication1': 0x00100000b06,
  'LaunchApplication2': 0x00100000b01,
  'LaunchAssistant': 0x00100000b0e,
  'LaunchCalendar': 0x00100000b02,
  'LaunchContacts': 0x00100000b0c,
  'LaunchControlPanel': 0x00100000b0f,
  'LaunchMail': 0x00100000b03,
  'LaunchMediaPlayer': 0x00100000b04,
  'LaunchMusicPlayer': 0x00100000b05,
  'LaunchPhone': 0x00100000b0d,
  'LaunchScreenSaver': 0x00100000b07,
  'LaunchSpreadsheet': 0x00100000b08,
  'LaunchWebBrowser': 0x00100000b09,
  'LaunchWebCam': 0x00100000b0a,
  'LaunchWordProcessor': 0x00100000b0b,
  'Link': 0x00100000d27,
  'ListProgram': 0x00100000d28,
  'LiveContent': 0x00100000d29,
  'Lock': 0x00100000d2a,
  'LogOff': 0x00100000605,
  'MailForward': 0x00100000a02,
  'MailReply': 0x00100000a03,
  'MailSend': 0x00100000a04,
  'MannerMode': 0x0010000100a,
  'MediaApps': 0x00100000d2b,
  'MediaAudioTrack': 0x00100000d50,
  'MediaClose': 0x00100000d5b,
  'MediaFastForward': 0x00100000d2c,
  'MediaLast': 0x00100000d2d,
  'MediaPause': 0x00100000d2e,
  'MediaPlay': 0x00100000d2f,
  'MediaPlayPause': 0x00100000a05,
  'MediaRecord': 0x00100000d30,
  'MediaRewind': 0x00100000d31,
  'MediaSkip': 0x00100000d32,
  'MediaSkipBackward': 0x00100000d51,
  'MediaSkipForward': 0x00100000d52,
  'MediaStepBackward': 0x00100000d53,
  'MediaStepForward': 0x00100000d54,
  'MediaStop': 0x00100000a07,
  'MediaTopMenu': 0x00100000d55,
  'MediaTrackNext': 0x00100000a08,
  'MediaTrackPrevious': 0x00100000a09,
  'MicrophoneToggle': 0x00100000e06,
  'MicrophoneVolumeDown': 0x00100000e07,
  'MicrophoneVolumeMute': 0x00100000e09,
  'MicrophoneVolumeUp': 0x00100000e08,
  'ModeChange': 0x0010000070b,
  'NavigateIn': 0x00100000d56,
  'NavigateNext': 0x00100000d57,
  'NavigateOut': 0x00100000d58,
  'NavigatePrevious': 0x00100000d59,
  'New': 0x00100000a0a,
  'NextCandidate': 0x0010000070c,
  'NextFavoriteChannel': 0x00100000d33,
  'NextUserProfile': 0x00100000d34,
  'NonConvert': 0x0010000070d,
  'Notification': 0x00100001009,
  'NumLock': 0x0010000010a,
  'OnDemand': 0x00100000d35,
  'Open': 0x00100000a0b,
  'PageDown': 0x00100000307,
  'PageUp': 0x00100000308,
  'Pairing': 0x00100000d5a,
  'Paste': 0x00100000408,
  'Pause': 0x00100000509,
  'PinPDown': 0x00100000d36,
  'PinPMove': 0x00100000d37,
  'PinPToggle': 0x00100000d38,
  'PinPUp': 0x00100000d39,
  'Play': 0x0010000050a,
  'PlaySpeedDown': 0x00100000d3a,
  'PlaySpeedReset': 0x00100000d3b,
  'PlaySpeedUp': 0x00100000d3c,
  'Power': 0x00100000606,
  'PowerOff': 0x00100000607,
  'PreviousCandidate': 0x0010000070e,
  'Print': 0x00100000a0c,
  'PrintScreen': 0x00100000608,
  'Process': 0x0010000070f,
  'Props': 0x0010000050b,
  'RandomToggle': 0x00100000d3d,
  'RcLowBattery': 0x00100000d3e,
  'RecordSpeedNext': 0x00100000d3f,
  'Redo': 0x00100000409,
  'RfBypass': 0x00100000d40,
  'Romaji': 0x0010000071b,
  'STBInput': 0x00100000d45,
  'STBPower': 0x00100000d46,
  'Save': 0x00100000a0d,
  'ScanChannelsToggle': 0x00100000d41,
  'ScreenModeNext': 0x00100000d42,
  'ScrollLock': 0x0010000010c,
  'Select': 0x0010000050c,
  'Settings': 0x00100000d43,
  'ShiftLevel5': 0x00100000111,
  'SingleCandidate': 0x00100000710,
  'Soft1': 0x00100000901,
  'Soft2': 0x00100000902,
  'Soft3': 0x00100000903,
  'Soft4': 0x00100000904,
  'Soft5': 0x00100000905,
  'Soft6': 0x00100000906,
  'Soft7': 0x00100000907,
  'Soft8': 0x00100000908,
  'SpeechCorrectionList': 0x00100000f01,
  'SpeechInputToggle': 0x00100000f02,
  'SpellCheck': 0x00100000a0e,
  'SplitScreenToggle': 0x00100000d44,
  'Standby': 0x0010000060a,
  'Subtitle': 0x00100000d47,
  'Super': 0x0010000010e,
  'Symbol': 0x0010000010f,
  'SymbolLock': 0x00100000110,
  'TV': 0x00100000d49,
  'TV3DMode': 0x00100001101,
  'TVAntennaCable': 0x00100001102,
  'TVAudioDescription': 0x00100001103,
  'TVAudioDescriptionMixDown': 0x00100001104,
  'TVAudioDescriptionMixUp': 0x00100001105,
  'TVContentsMenu': 0x00100001106,
  'TVDataService': 0x00100001107,
  'TVInput': 0x00100000d4a,
  'TVInputComponent1': 0x00100001108,
  'TVInputComponent2': 0x00100001109,
  'TVInputComposite1': 0x0010000110a,
  'TVInputComposite2': 0x0010000110b,
  'TVInputHDMI1': 0x0010000110c,
  'TVInputHDMI2': 0x0010000110d,
  'TVInputHDMI3': 0x0010000110e,
  'TVInputHDMI4': 0x0010000110f,
  'TVInputVGA1': 0x00100001110,
  'TVMediaContext': 0x00100001111,
  'TVNetwork': 0x00100001112,
  'TVNumberEntry': 0x00100001113,
  'TVPower': 0x00100000d4b,
  'TVRadioService': 0x00100001114,
  'TVSatellite': 0x00100001115,
  'TVSatelliteBS': 0x00100001116,
  'TVSatelliteCS': 0x00100001117,
  'TVSatelliteToggle': 0x00100001118,
  'TVTerrestrialAnalog': 0x00100001119,
  'TVTerrestrialDigital': 0x0010000111a,
  'TVTimer': 0x0010000111b,
  'Tab': 0x00100000009,
  'Teletext': 0x00100000d48,
  'Undo': 0x0010000040a,
  'Unidentified': 0x00100000001,
  'VideoModeNext': 0x00100000d4c,
  'VoiceDial': 0x0010000100b,
  'WakeUp': 0x0010000060b,
  'Wink': 0x00100000d4d,
  'Zenkaku': 0x0010000071c,
  'ZenkakuHankaku': 0x0010000071d,
  'ZoomIn': 0x0010000050d,
  'ZoomOut': 0x0010000050e,
  'ZoomToggle': 0x00100000d4e,
};

/// Maps Web KeyboardEvent codes to the matching PhysicalKeyboardKey USB HID code.
const Map<String, int> kWebToPhysicalKey = <String, int>{
  'Abort': 0x0007009b, // abort
  'Again': 0x00070079, // again
  'AltLeft': 0x000700e2, // altLeft
  'AltRight': 0x000700e6, // altRight
  'ArrowDown': 0x00070051, // arrowDown
  'ArrowLeft': 0x00070050, // arrowLeft
  'ArrowRight': 0x0007004f, // arrowRight
  'ArrowUp': 0x00070052, // arrowUp
  'AudioVolumeDown': 0x00070081, // audioVolumeDown
  'AudioVolumeMute': 0x0007007f, // audioVolumeMute
  'AudioVolumeUp': 0x00070080, // audioVolumeUp
  'Backquote': 0x00070035, // backquote
  'Backslash': 0x00070031, // backslash
  'Backspace': 0x0007002a, // backspace
  'BracketLeft': 0x0007002f, // bracketLeft
  'BracketRight': 0x00070030, // bracketRight
  'BrightnessDown': 0x000c0070, // brightnessDown
  'BrightnessUp': 0x000c006f, // brightnessUp
  'BrowserBack': 0x000c0224, // browserBack
  'BrowserFavorites': 0x000c022a, // browserFavorites
  'BrowserForward': 0x000c0225, // browserForward
  'BrowserHome': 0x000c0223, // browserHome
  'BrowserRefresh': 0x000c0227, // browserRefresh
  'BrowserSearch': 0x000c0221, // browserSearch
  'BrowserStop': 0x000c0226, // browserStop
  'CapsLock': 0x00070039, // capsLock
  'Comma': 0x00070036, // comma
  'ContextMenu': 0x00070065, // contextMenu
  'ControlLeft': 0x000700e0, // controlLeft
  'ControlRight': 0x000700e4, // controlRight
  'Convert': 0x0007008a, // convert
  'Copy': 0x0007007c, // copy
  'Cut': 0x0007007b, // cut
  'Delete': 0x0007004c, // delete
  'Digit0': 0x00070027, // digit0
  'Digit1': 0x0007001e, // digit1
  'Digit2': 0x0007001f, // digit2
  'Digit3': 0x00070020, // digit3
  'Digit4': 0x00070021, // digit4
  'Digit5': 0x00070022, // digit5
  'Digit6': 0x00070023, // digit6
  'Digit7': 0x00070024, // digit7
  'Digit8': 0x00070025, // digit8
  'Digit9': 0x00070026, // digit9
  'DisplayToggleIntExt': 0x000100b5, // displayToggleIntExt
  'Eject': 0x000c00b8, // eject
  'End': 0x0007004d, // end
  'Enter': 0x00070028, // enter
  'Equal': 0x0007002e, // equal
  'Esc': 0x00070029, // escape
  'Escape': 0x00070029, // escape
  'F1': 0x0007003a, // f1
  'F10': 0x00070043, // f10
  'F11': 0x00070044, // f11
  'F12': 0x00070045, // f12
  'F13': 0x00070068, // f13
  'F14': 0x00070069, // f14
  'F15': 0x0007006a, // f15
  'F16': 0x0007006b, // f16
  'F17': 0x0007006c, // f17
  'F18': 0x0007006d, // f18
  'F19': 0x0007006e, // f19
  'F2': 0x0007003b, // f2
  'F20': 0x0007006f, // f20
  'F21': 0x00070070, // f21
  'F22': 0x00070071, // f22
  'F23': 0x00070072, // f23
  'F24': 0x00070073, // f24
  'F3': 0x0007003c, // f3
  'F4': 0x0007003d, // f4
  'F5': 0x0007003e, // f5
  'F6': 0x0007003f, // f6
  'F7': 0x00070040, // f7
  'F8': 0x00070041, // f8
  'F9': 0x00070042, // f9
  'Find': 0x0007007e, // find
  'Fn': 0x00000012, // fn
  'FnLock': 0x00000013, // fnLock
  'GameButton1': 0x0005ff01, // gameButton1
  'GameButton10': 0x0005ff0a, // gameButton10
  'GameButton11': 0x0005ff0b, // gameButton11
  'GameButton12': 0x0005ff0c, // gameButton12
  'GameButton13': 0x0005ff0d, // gameButton13
  'GameButton14': 0x0005ff0e, // gameButton14
  'GameButton15': 0x0005ff0f, // gameButton15
  'GameButton16': 0x0005ff10, // gameButton16
  'GameButton2': 0x0005ff02, // gameButton2
  'GameButton3': 0x0005ff03, // gameButton3
  'GameButton4': 0x0005ff04, // gameButton4
  'GameButton5': 0x0005ff05, // gameButton5
  'GameButton6': 0x0005ff06, // gameButton6
  'GameButton7': 0x0005ff07, // gameButton7
  'GameButton8': 0x0005ff08, // gameButton8
  'GameButton9': 0x0005ff09, // gameButton9
  'GameButtonA': 0x0005ff11, // gameButtonA
  'GameButtonB': 0x0005ff12, // gameButtonB
  'GameButtonC': 0x0005ff13, // gameButtonC
  'GameButtonLeft1': 0x0005ff14, // gameButtonLeft1
  'GameButtonLeft2': 0x0005ff15, // gameButtonLeft2
  'GameButtonMode': 0x0005ff16, // gameButtonMode
  'GameButtonRight1': 0x0005ff17, // gameButtonRight1
  'GameButtonRight2': 0x0005ff18, // gameButtonRight2
  'GameButtonSelect': 0x0005ff19, // gameButtonSelect
  'GameButtonStart': 0x0005ff1a, // gameButtonStart
  'GameButtonThumbLeft': 0x0005ff1b, // gameButtonThumbLeft
  'GameButtonThumbRight': 0x0005ff1c, // gameButtonThumbRight
  'GameButtonX': 0x0005ff1d, // gameButtonX
  'GameButtonY': 0x0005ff1e, // gameButtonY
  'GameButtonZ': 0x0005ff1f, // gameButtonZ
  'Help': 0x00070075, // help
  'Home': 0x0007004a, // home
  'Hyper': 0x00000010, // hyper
  'Insert': 0x00070049, // insert
  'IntlBackslash': 0x00070064, // intlBackslash
  'IntlRo': 0x00070087, // intlRo
  'IntlYen': 0x00070089, // intlYen
  'KanaMode': 0x00070088, // kanaMode
  'KeyA': 0x00070004, // keyA
  'KeyB': 0x00070005, // keyB
  'KeyC': 0x00070006, // keyC
  'KeyD': 0x00070007, // keyD
  'KeyE': 0x00070008, // keyE
  'KeyF': 0x00070009, // keyF
  'KeyG': 0x0007000a, // keyG
  'KeyH': 0x0007000b, // keyH
  'KeyI': 0x0007000c, // keyI
  'KeyJ': 0x0007000d, // keyJ
  'KeyK': 0x0007000e, // keyK
  'KeyL': 0x0007000f, // keyL
  'KeyM': 0x00070010, // keyM
  'KeyN': 0x00070011, // keyN
  'KeyO': 0x00070012, // keyO
  'KeyP': 0x00070013, // keyP
  'KeyQ': 0x00070014, // keyQ
  'KeyR': 0x00070015, // keyR
  'KeyS': 0x00070016, // keyS
  'KeyT': 0x00070017, // keyT
  'KeyU': 0x00070018, // keyU
  'KeyV': 0x00070019, // keyV
  'KeyW': 0x0007001a, // keyW
  'KeyX': 0x0007001b, // keyX
  'KeyY': 0x0007001c, // keyY
  'KeyZ': 0x0007001d, // keyZ
  'KeyboardLayoutSelect': 0x000c029d, // keyboardLayoutSelect
  'Lang1': 0x00070090, // lang1
  'Lang2': 0x00070091, // lang2
  'Lang3': 0x00070092, // lang3
  'Lang4': 0x00070093, // lang4
  'Lang5': 0x00070094, // lang5
  'LaunchApp1': 0x000c0194, // launchApp1
  'LaunchApp2': 0x000c0192, // launchApp2
  'LaunchAssistant': 0x000c01cb, // launchAssistant
  'LaunchControlPanel': 0x000c019f, // launchControlPanel
  'LaunchMail': 0x000c018a, // launchMail
  'LaunchScreenSaver': 0x000c01b1, // launchScreenSaver
  'MailForward': 0x000c028b, // mailForward
  'MailReply': 0x000c0289, // mailReply
  'MailSend': 0x000c028c, // mailSend
  'MediaFastForward': 0x000c00b3, // mediaFastForward
  'MediaPause': 0x000c00b1, // mediaPause
  'MediaPlay': 0x000c00b0, // mediaPlay
  'MediaPlayPause': 0x000c00cd, // mediaPlayPause
  'MediaRecord': 0x000c00b2, // mediaRecord
  'MediaRewind': 0x000c00b4, // mediaRewind
  'MediaSelect': 0x000c0183, // mediaSelect
  'MediaStop': 0x000c00b7, // mediaStop
  'MediaTrackNext': 0x000c00b5, // mediaTrackNext
  'MediaTrackPrevious': 0x000c00b6, // mediaTrackPrevious
  'MetaLeft': 0x000700e3, // metaLeft
  'MetaRight': 0x000700e7, // metaRight
  'MicrophoneMuteToggle': 0x00000018, // microphoneMuteToggle
  'Minus': 0x0007002d, // minus
  'NonConvert': 0x0007008b, // nonConvert
  'NumLock': 0x00070053, // numLock
  'Numpad0': 0x00070062, // numpad0
  'Numpad1': 0x00070059, // numpad1
  'Numpad2': 0x0007005a, // numpad2
  'Numpad3': 0x0007005b, // numpad3
  'Numpad4': 0x0007005c, // numpad4
  'Numpad5': 0x0007005d, // numpad5
  'Numpad6': 0x0007005e, // numpad6
  'Numpad7': 0x0007005f, // numpad7
  'Numpad8': 0x00070060, // numpad8
  'Numpad9': 0x00070061, // numpad9
  'NumpadAdd': 0x00070057, // numpadAdd
  'NumpadBackspace': 0x000700bb, // numpadBackspace
  'NumpadClear': 0x000700d8, // numpadClear
  'NumpadClearEntry': 0x000700d9, // numpadClearEntry
  'NumpadComma': 0x00070085, // numpadComma
  'NumpadDecimal': 0x00070063, // numpadDecimal
  'NumpadDivide': 0x00070054, // numpadDivide
  'NumpadEnter': 0x00070058, // numpadEnter
  'NumpadEqual': 0x00070067, // numpadEqual
  'NumpadMemoryAdd': 0x000700d3, // numpadMemoryAdd
  'NumpadMemoryClear': 0x000700d2, // numpadMemoryClear
  'NumpadMemoryRecall': 0x000700d1, // numpadMemoryRecall
  'NumpadMemoryStore': 0x000700d0, // numpadMemoryStore
  'NumpadMemorySubtract': 0x000700d4, // numpadMemorySubtract
  'NumpadMultiply': 0x00070055, // numpadMultiply
  'NumpadParenLeft': 0x000700b6, // numpadParenLeft
  'NumpadParenRight': 0x000700b7, // numpadParenRight
  'NumpadSubtract': 0x00070056, // numpadSubtract
  'Open': 0x00070074, // open
  'PageDown': 0x0007004e, // pageDown
  'PageUp': 0x0007004b, // pageUp
  'Paste': 0x0007007d, // paste
  'Pause': 0x00070048, // pause
  'Period': 0x00070037, // period
  'Power': 0x00070066, // power
  'PrintScreen': 0x00070046, // printScreen
  'PrivacyScreenToggle': 0x00000017, // privacyScreenToggle
  'Props': 0x000700a3, // props
  'Quote': 0x00070034, // quote
  'Resume': 0x00000015, // resume
  'ScrollLock': 0x00070047, // scrollLock
  'Select': 0x00070077, // select
  'SelectTask': 0x000c01a2, // selectTask
  'Semicolon': 0x00070033, // semicolon
  'ShiftLeft': 0x000700e1, // shiftLeft
  'ShiftRight': 0x000700e5, // shiftRight
  'ShowAllWindows': 0x000c029f, // showAllWindows
  'Slash': 0x00070038, // slash
  'Sleep': 0x00010082, // sleep
  'Space': 0x0007002c, // space
  'Super': 0x00000011, // superKey
  'Suspend': 0x00000014, // suspend
  'Tab': 0x0007002b, // tab
  'Turbo': 0x00000016, // turbo
  'Undo': 0x0007007a, // undo
  'WakeUp': 0x00010083, // wakeUp
  'ZoomToggle': 0x000c0232, // zoomToggle
};

/// Maps Web KeyboardEvent keys to Flutter logical IDs that depend on locations.
///
/// `KeyboardEvent.location` is defined as:
///
///  * 0: Standard
///  * 1: Left
///  * 2: Right
///  * 3: Numpad
const Map<String, List<int?>> kWebLogicalLocationMap = <String, List<int?>>{
  '*': <int?>[0x0000000002a, null, null, 0x0020000022a], // asterisk, null, null, numpadMultiply
  '+': <int?>[0x0000000002b, null, null, 0x0020000022b], // add, null, null, numpadAdd
  '-': <int?>[0x0000000002d, null, null, 0x0020000022d], // minus, null, null, numpadSubtract
  '.': <int?>[0x0000000002e, null, null, 0x0020000022e], // period, null, null, numpadDecimal
  '/': <int?>[0x0000000002f, null, null, 0x0020000022f], // slash, null, null, numpadDivide
  '0': <int?>[0x00000000030, null, null, 0x00200000230], // digit0, null, null, numpad0
  '1': <int?>[0x00000000031, null, null, 0x00200000231], // digit1, null, null, numpad1
  '2': <int?>[0x00000000032, null, null, 0x00200000232], // digit2, null, null, numpad2
  '3': <int?>[0x00000000033, null, null, 0x00200000233], // digit3, null, null, numpad3
  '4': <int?>[0x00000000034, null, null, 0x00200000234], // digit4, null, null, numpad4
  '5': <int?>[0x00000000035, null, null, 0x00200000235], // digit5, null, null, numpad5
  '6': <int?>[0x00000000036, null, null, 0x00200000236], // digit6, null, null, numpad6
  '7': <int?>[0x00000000037, null, null, 0x00200000237], // digit7, null, null, numpad7
  '8': <int?>[0x00000000038, null, null, 0x00200000238], // digit8, null, null, numpad8
  '9': <int?>[0x00000000039, null, null, 0x00200000239], // digit9, null, null, numpad9
  'Alt': <int?>[
    0x00200000104,
    0x00200000104,
    0x00200000105,
    null,
  ], // altLeft, altLeft, altRight, null
  'AltGraph': <int?>[0x00100000103, null, 0x00100000103, null], // altGraph, null, altGraph, null
  'ArrowDown': <int?>[0x00100000301, null, null, 0x00200000232], // arrowDown, null, null, numpad2
  'ArrowLeft': <int?>[0x00100000302, null, null, 0x00200000234], // arrowLeft, null, null, numpad4
  'ArrowRight': <int?>[0x00100000303, null, null, 0x00200000236], // arrowRight, null, null, numpad6
  'ArrowUp': <int?>[0x00100000304, null, null, 0x00200000238], // arrowUp, null, null, numpad8
  'Clear': <int?>[0x00100000401, null, null, 0x00200000235], // clear, null, null, numpad5
  'Control': <int?>[
    0x00200000100,
    0x00200000100,
    0x00200000101,
    null,
  ], // controlLeft, controlLeft, controlRight, null
  'Delete': <int?>[0x0010000007f, null, null, 0x0020000022e], // delete, null, null, numpadDecimal
  'End': <int?>[0x00100000305, null, null, 0x00200000231], // end, null, null, numpad1
  'Enter': <int?>[0x0010000000d, null, null, 0x0020000020d], // enter, null, null, numpadEnter
  'Home': <int?>[0x00100000306, null, null, 0x00200000237], // home, null, null, numpad7
  'Insert': <int?>[0x00100000407, null, null, 0x00200000230], // insert, null, null, numpad0
  'Meta': <int?>[
    0x00200000106,
    0x00200000106,
    0x00200000107,
    null,
  ], // metaLeft, metaLeft, metaRight, null
  'PageDown': <int?>[0x00100000307, null, null, 0x00200000233], // pageDown, null, null, numpad3
  'PageUp': <int?>[0x00100000308, null, null, 0x00200000239], // pageUp, null, null, numpad9
  'Shift': <int?>[
    0x00200000102,
    0x00200000102,
    0x00200000103,
    null,
  ], // shiftLeft, shiftLeft, shiftRight, null
};
