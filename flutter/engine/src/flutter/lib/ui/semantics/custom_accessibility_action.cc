// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "flutter/lib/ui/semantics/custom_accessibility_action.h"

namespace flutter {

CustomAccessibilityAction::CustomAccessibilityAction() = default;

CustomAccessibilityAction::~CustomAccessibilityAction() = default;

}  // namespace flutter
