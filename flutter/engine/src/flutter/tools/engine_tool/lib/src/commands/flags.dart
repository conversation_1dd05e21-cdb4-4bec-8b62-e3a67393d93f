// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: public_member_api_docs

// The purpose of this list of flags in a file separate from the command
// definitions is to ensure that flags are named consistently across
// subcommands. For example, unless there's a compelling reason to have both,
// we'd avoid having one subcommand define an --all flag while another defines
// an --everything flag.

// Keep this list alphabetized.
const allFlag = 'all';
const builderFlag = 'builder';
const dryRunFlag = 'dry-run';
const quietFlag = 'quiet';
const runTestsFlag = 'run-tests';
const verboseFlag = 'verbose';
const testOnlyFlag = 'testonly';
