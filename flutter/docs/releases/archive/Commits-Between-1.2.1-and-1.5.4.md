## Changes in `flutter/flutter` from `8661d8` to `b593f5`
| Author | Commit |
|--------|--------|
|<PERSON><PERSON><PERSON>rat<PERSON> <<EMAIL>>|Add track-widget-creation flag to attach command ([#31262](https://github.com/flutter/flutter/issues/31262))  * Add track-widget-creation flag to attach command  * use the flag|
|<PERSON> <<EMAIL>>|Text field scroll physics ([#31088](https://github.com/flutter/flutter/issues/31088))  TextField and CupertinoTextField can now specify scrollPhysics. Cupertino defaults to bouncing now.|
|<PERSON><PERSON> <<EMAIL>>|Revert "[Material] Update slider and slider theme with new sizes, shapes, and color mappings ([#30390](https://github.com/flutter/flutter/issues/30390))" ([#31339](https://github.com/flutter/flutter/issues/31339))  This reverts commit b1039f0f1b761b0e4ec1825eed269b10af6104d4.  (This is a temporary revert while some of our customers deal with the breaking changes involved.)|
|Ben Konyi <<EMAIL>>|Fixed flutter_attach_test not respecting authentication codes ([#31321](https://github.com/flutter/flutter/issues/31321))|
|Ben Konyi <<EMAIL>>|Fixed failing tests caused by introduction of authentication codes ([#31315](https://github.com/flutter/flutter/issues/31315))|
|Christopher Fujino <<EMAIL>>|Add flutter attach documentation ([#31279](https://github.com/flutter/flutter/issues/31279))  * add attach.md document|
|Ben Konyi <<EMAIL>>|Updated flutter_driver to support auth codes ([#31310](https://github.com/flutter/flutter/issues/31310))|
|Ben Konyi <<EMAIL>>|Added support for authentication codes for the VM service. ([#30857](https://github.com/flutter/flutter/issues/30857))  * Added support for authentication codes for the VM service.  Previously, a valid web socket connection would use the following URI:  `ws://127.0.0.1/ws`  Now, by default, the VM service requires a connection to be made with a URI similar to the following:  `ws://127.0.0.1:8181/Ug_U0QVsqFs=/ws`  where `Ug_U0QVsqFs` is an authentication code generated and shared by the service.  This behavior can be disabled with the `--disable-service-auth-codes` flag.|
|Devon Carew <<EMAIL>>|add daemon.log to the daemon spec ([#31273](https://github.com/flutter/flutter/issues/31273))  * add daemon.log to the daemon spec  * mention use in flutter run --machine|
|stuartmorgan <<EMAIL>>|Add desktop workflows to doctor ([#31283](https://github.com/flutter/flutter/issues/31283))  The 'doctor' check to determine if any devices can be listed for the device should consider desktop workflows as well.  This has no effect unless the environment variable to enable desktop device listing is set.|
|Jonah Williams <<EMAIL>>|Add flutter run support for linux and windows ([#31229](https://github.com/flutter/flutter/issues/31229))|
|Jonah Williams <<EMAIL>>|initial work on coverage generating script for tool ([#29494](https://github.com/flutter/flutter/issues/29494))|
|Jonah Williams <<EMAIL>>|add --force flag to precache ([#31278](https://github.com/flutter/flutter/issues/31278))|
|chunhtai <<EMAIL>>|fix issue 12999: Make assets available during tests ([#31207](https://github.com/flutter/flutter/issues/31207))|
|Jonah Williams <<EMAIL>>|pass track widget creation flag through to build script ([#31277](https://github.com/flutter/flutter/issues/31277))|
|Anthony <<EMAIL>>|[Material] Update slider and slider theme with new sizes, shapes, and color mappings ([#30390](https://github.com/flutter/flutter/issues/30390))  This PR makes a number of changes to the visual appearance of material sliders:  Sizes/Shapes ** enabled thumb radius from 6 to 10 ** disabled thumb radius from 4 to 10 with no gap ** default track shape is a rounded rect rather than a rect ** Colors ** all of the colors now use the new color scheme ** overlay opacity has been reduce from 16% to 12% ** value indicator text color now respects the indicator it is on by using onPrimary ** disabledThumb color no respects the surface it is on by using onSurface The slider theme is also now constructed consistently with other theme objects within the ThemeData. By default, all values are null, and have default values that are resolved in the slider itself, rather than in the slider theme.|
|Efthymis Sarmpanis <<EMAIL>>|Adds Tap Header Feature to ExpansionPanelList ([#29390](https://github.com/flutter/flutter/issues/29390))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 06fea14e5800..4b9966f5cb41 (3 commits) ([#31271](https://github.com/flutter/flutter/issues/31271))   https://github.com/flutter/engine/compare/06fea14e5800...4b9966f5cb41  git log 06fea14e5800c35a4fc4dd10cc31b997f1be81e9..4b9966f5cb412a73fa50462b3aee9082f436a62a --no-merges --oneline 4b9966f5c Add an adjustment to the line width check in LineBreaker::addWordBreak ([flutter/engine#8623](https://github.com/flutter/engine/issues/8623)) 60bb866ce Roll src/third_party/skia a94670bd08cd..2c2240f66805 (2 commits) ([flutter/engine#8632](https://github.com/flutter/engine/issues/8632)) a144f17ae Tight Paragraph Width ([flutter/engine#8530](https://github.com/flutter/engine/issues/8530))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Devon Carew <<EMAIL>>|remove the unused hintMessage and hintId fields from the reload results ([#31267](https://github.com/flutter/flutter/issues/31267))|
|engine-flutter-autoroll <<EMAIL>>|06fea14e5 Glitchiness with Tab Characters ([flutter/engine#8591](https://github.com/flutter/engine/issues/8591)) ([#31265](https://github.com/flutter/flutter/issues/31265))   https://github.com/flutter/engine/compare/b6787096a2d3...06fea14e5800  git log b6787096a2d3d0365f03f4cfdb70b932e4e5bae3..06fea14e5800c35a4fc4dd10cc31b997f1be81e9 --no-merges --oneline 06fea14e5 Glitchiness with Tab Characters ([flutter/engine#8591](https://github.com/flutter/engine/issues/8591))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|b6787096a Roll src/third_party/skia 847d55be4e62..a94670bd08cd (1 commits) ([flutter/engine#8631](https://github.com/flutter/engine/issues/8631)) ([#31260](https://github.com/flutter/flutter/issues/31260))   https://github.com/flutter/engine/compare/3e6dc02ad62e...b6787096a2d3  git log 3e6dc02ad62ef9fa02660f5fb0bb114414e282d4..b6787096a2d3d0365f03f4cfdb70b932e4e5bae3 --no-merges --oneline b6787096a Roll src/third_party/skia 847d55be4e62..a94670bd08cd (1 commits) ([flutter/engine#8631](https://github.com/flutter/engine/issues/8631))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|3e6dc02ad Roll src/third_party/skia ced2b79d00f9..847d55be4e62 (3 commits) ([flutter/engine#8630](https://github.com/flutter/engine/issues/8630)) ([#31251](https://github.com/flutter/flutter/issues/31251))   https://github.com/flutter/engine/compare/54f7c51339ae...3e6dc02ad62e  git log 54f7c51339ae7d86a983b5850b14dafe4e2bd90a..3e6dc02ad62ef9fa02660f5fb0bb114414e282d4 --no-merges --oneline 3e6dc02ad Roll src/third_party/skia ced2b79d00f9..847d55be4e62 (3 commits) ([flutter/engine#8630](https://github.com/flutter/engine/issues/8630))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|54f7c5133 Roll src/third_party/skia 14c90c1ca218..ced2b79d00f9 (1 commits) ([flutter/engine#8629](https://github.com/flutter/engine/issues/8629)) ([#31240](https://github.com/flutter/flutter/issues/31240))   https://github.com/flutter/engine/compare/70068706d0ee...54f7c51339ae  git log 70068706d0ee3bc9847632c4d0e467fba49271f8..54f7c51339ae7d86a983b5850b14dafe4e2bd90a --no-merges --oneline 54f7c5133 Roll src/third_party/skia 14c90c1ca218..ced2b79d00f9 (1 commits) ([flutter/engine#8629](https://github.com/flutter/engine/issues/8629))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Add run capability for macOS target ([#31218](https://github.com/flutter/flutter/issues/31218))|
|engine-flutter-autoroll <<EMAIL>>|70068706d Allow building without python2 ([flutter/engine#8605](https://github.com/flutter/engine/issues/8605)) ([#31239](https://github.com/flutter/flutter/issues/31239))   https://github.com/flutter/engine/compare/8b5a50c0df76...70068706d0ee  git log 8b5a50c0df768743dd9858cceb92d1d1de44c35e..70068706d0ee3bc9847632c4d0e467fba49271f8 --no-merges --oneline 70068706d Allow building without python2 ([flutter/engine#8605](https://github.com/flutter/engine/issues/8605))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 876c4c339064..8b5a50c0df76 (4 commits) ([#31237](https://github.com/flutter/flutter/issues/31237))   https://github.com/flutter/engine/compare/876c4c339064...8b5a50c0df76  git log 876c4c339064ed321f37fba61422194d84cbfdb7..8b5a50c0df768743dd9858cceb92d1d1de44c35e --no-merges --oneline 8b5a50c0d Test saving compilation traces. ([flutter/engine#8618](https://github.com/flutter/engine/issues/8618)) 7d3caf895 Avoid leaking the VM in runtime_unittests and update failing tests. ([flutter/engine#8626](https://github.com/flutter/engine/issues/8626)) 91b71070f Revert &[#34](https://github.com/flutter/flutter/issues/34);Add a unit test for PhysicalShapeLayer ([#8616](https://github.com/flutter/flutter/issues/8616))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8627](https://github.com/flutter/engine/issues/8627)) 8b667b017 Add a unit test for PhysicalShapeLayer ([flutter/engine#8616](https://github.com/flutter/engine/issues/8616))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 76794745b676..876c4c339064 (4 commits) ([#31231](https://github.com/flutter/flutter/issues/31231))   https://github.com/flutter/engine/compare/76794745b676...876c4c339064  git log 76794745b6764246cffafb237182f7fafeac41f8..876c4c339064ed321f37fba61422194d84cbfdb7 --no-merges --oneline 876c4c339 Assert that all VM launches in the process have the same opinion on whether the VM should be leaked in the process. ([flutter/engine#8622](https://github.com/flutter/engine/issues/8622)) 72c84c287 Revert &[#34](https://github.com/flutter/flutter/issues/34);Roll src/third_party/dart 7b9ab9f353..714a082a5d (7 commits)&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8624](https://github.com/flutter/engine/issues/8624)) f017fe74a Avoid manually shutting down engine managed isolates. ([flutter/engine#8621](https://github.com/flutter/engine/issues/8621)) 80e934e32 Roll src/third_party/dart 7b9ab9f353..714a082a5d (7 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 523a9a9adde9..76794745b676 (6 commits) ([#31230](https://github.com/flutter/flutter/issues/31230))   https://github.com/flutter/engine/compare/523a9a9adde9...76794745b676  git log 523a9a9adde96d22c801eac59f9f6db3d2a79a30..76794745b6764246cffafb237182f7fafeac41f8 --no-merges --oneline 76794745b Roll src/third_party/skia 6f9047027be3..14c90c1ca218 (5 commits) ([flutter/engine#8619](https://github.com/flutter/engine/issues/8619)) d8aa6bca1 Fix typo in comment ([flutter/engine#8617](https://github.com/flutter/engine/issues/8617)) 8ff631f3d Rename flow namespace to flutter ([flutter/engine#8615](https://github.com/flutter/engine/issues/8615)) 0534b6ae5 Roll src/third_party/dart bc99f1ca28..7b9ab9f353 (5 commits) b767e4d3c Roll src/third_party/skia 5155e09d1466..6f9047027be3 (5 commits) ([flutter/engine#8613](https://github.com/flutter/engine/issues/8613)) c5d1ba916 Remove call to SkFont::setLinearMetrics ([flutter/engine#8612](https://github.com/flutter/engine/issues/8612))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Allow disabling all fingerprint caches via environment variable ([#31171](https://github.com/flutter/flutter/issues/31171))|
|Jonah Williams <<EMAIL>>|Disable macOS integration tests ([#31216](https://github.com/flutter/flutter/issues/31216))|
|Gary Qian <<EMAIL>>|Use full height of the glyph for caret height on Android v2 ([#31210](https://github.com/flutter/flutter/issues/31210))  Will cause golden and Scuba changes. Caret will become taller and shift upwards by 2 pixels.|
|xster <<EMAIL>>|Commit a navigator.pop as soon as the back swipe is lifted ([#30422](https://github.com/flutter/flutter/issues/30422))|
|xster <<EMAIL>>|Add some more cupertino icons ([#30946](https://github.com/flutter/flutter/issues/30946))|
|xster <<EMAIL>>|Clarify various CupertinoTabView docs ([#31109](https://github.com/flutter/flutter/issues/31109))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine fdd427241b77..523a9a9adde9 (10 commits) ([#31213](https://github.com/flutter/flutter/issues/31213))   https://github.com/flutter/engine/compare/fdd427241b77...523a9a9adde9  git log fdd427241b7790a1ad86794725b311245ed3b470..523a9a9adde96d22c801eac59f9f6db3d2a79a30 --no-merges --oneline 523a9a9ad Add FLEPluginRegistry for macOS ([flutter/engine#8611](https://github.com/flutter/engine/issues/8611)) dd9dcaf9a [fuchsia] Fix SceneUpdateContext for new PaintContext field ([flutter/engine#8608](https://github.com/flutter/engine/issues/8608)) 0e27e0fc3 Pipe Z bounds from ViewportMetrics to Flow ([flutter/engine#8583](https://github.com/flutter/engine/issues/8583)) f62ab83ba Roll src/third_party/dart f0da03ca36..bc99f1ca28 (1 commits) 7b38aef03 Roll src/third_party/skia 33906ad1e26f..5155e09d1466 (4 commits) ([flutter/engine#8609](https://github.com/flutter/engine/issues/8609)) b0dedbd60 Roll src/third_party/dart d9c40462c2..f0da03ca36 (1 commits) f88ba6e43 Roll src/third_party/skia e88d4382e1cf..33906ad1e26f (1 commits) ([flutter/engine#8604](https://github.com/flutter/engine/issues/8604)) 53c0905b1 Roll src/third_party/skia 652b007a4cbb..e88d4382e1cf (4 commits) ([flutter/engine#8603](https://github.com/flutter/engine/issues/8603)) 4266f8583 Add desktop shell unittests to test script ([flutter/engine#8600](https://github.com/flutter/engine/issues/8600)) 8156268f0 Roll src/third_party/dart 8e73b3c006..d9c40462c2 (4 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Add desktop projects and build commands (experimental) ([#31205](https://github.com/flutter/flutter/issues/31205))|
|Todd Volkert <<EMAIL>>|Add breadcrumb to docs ([#31178](https://github.com/flutter/flutter/issues/31178))|
|Todd Volkert <<EMAIL>>|Rename Border.uniform() -> Border.fromSide() ([#30792](https://github.com/flutter/flutter/issues/30792))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 388124f948e6..fdd427241b77 (3 commits) ([#31179](https://github.com/flutter/flutter/issues/31179))   https://github.com/flutter/engine/compare/388124f948e6...fdd427241b77  git log ****************************************..fdd427241b7790a1ad86794725b311245ed3b470 --no-merges --oneline fdd427241 Roll src/third_party/skia 70ed05e53ad2..652b007a4cbb (1 commits) ([flutter/engine#8601](https://github.com/flutter/engine/issues/8601)) 3b016106c Check that TransformLayer has a finite matrix ([flutter/engine#8585](https://github.com/flutter/engine/issues/8585)) 4805d72a4 Implement StandardMethodCodec for C&[#43](https://github.com/flutter/flutter/issues/43);&[#43](https://github.com/flutter/flutter/issues/43); shells ([flutter/engine#8598](https://github.com/flutter/engine/issues/8598))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|388124f94 Roll src/third_party/skia db13d3874846..70ed05e53ad2 (6 commits) ([flutter/engine#8599](https://github.com/flutter/engine/issues/8599)) ([#31170](https://github.com/flutter/flutter/issues/31170))   https://github.com/flutter/engine/compare/b819b62bed3f...388124f948e6  git log b819b62bed3ffedf729ac1daddcc8bf7885e9982..**************************************** --no-merges --oneline 388124f94 Roll src/third_party/skia db13d3874846..70ed05e53ad2 (6 commits) ([flutter/engine#8599](https://github.com/flutter/engine/issues/8599))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|b819b62be Variant type for C++ client wrapper ([flutter/engine#8592](https://github.com/flutter/engine/issues/8592)) ([#31168](https://github.com/flutter/flutter/issues/31168))   https://github.com/flutter/engine/compare/db36d28ed074...b819b62bed3f  git log db36d28ed074b8b4f018ce6f7cb93b4e8ffcf456..b819b62bed3ffedf729ac1daddcc8bf7885e9982 --no-merges --oneline b819b62be Variant type for C&[#43](https://github.com/flutter/flutter/issues/43);&[#43](https://github.com/flutter/flutter/issues/43); client wrapper ([flutter/engine#8592](https://github.com/flutter/engine/issues/8592))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|db36d28ed Roll src/third_party/dart a8f3a5dae6..8e73b3c006 (92 commits) ([#31163](https://github.com/flutter/flutter/issues/31163))   https://github.com/flutter/engine/compare/c0ee647ca422...db36d28ed074  git log c0ee647ca4226fcb38a5af178a97ce8ba9129184..db36d28ed074b8b4f018ce6f7cb93b4e8ffcf456 --no-merges --oneline db36d28ed Roll src/third_party/dart a8f3a5dae6..8e73b3c006 (92 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Gary Qian <<EMAIL>>|Revert "Use full height of the glyph for caret height on Android" ([#31159](https://github.com/flutter/flutter/issues/31159))  * Revert "Use full height of the glyph for caret height on Android ([#30991](https://github.com/flutter/flutter/issues/30991))"  This reverts commit 96e1fc9c34a2c3a11e0dd98ceccb6fbea7ce3460.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 7af3f2b9e7c5..c0ee647ca422 (2 commits) ([#31160](https://github.com/flutter/flutter/issues/31160))   https://github.com/flutter/engine/compare/7af3f2b9e7c5...c0ee647ca422  git log 7af3f2b9e7c5f911ef4b1cff37aa5088fad6dbf5..c0ee647ca4226fcb38a5af178a97ce8ba9129184 --no-merges --oneline c0ee647ca Roll src/third_party/skia 68aeec07de4a..db13d3874846 (16 commits) ([flutter/engine#8595](https://github.com/flutter/engine/issues/8595)) 28a46d51a Composite Embedded Views with the correct GrContext  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 7d1da567434a..7af3f2b9e7c5 (2 commits) ([#31153](https://github.com/flutter/flutter/issues/31153))   https://github.com/flutter/engine/compare/7d1da567434a...7af3f2b9e7c5  git log 7d1da567434a820853ed6fae1f6ed65b9de5ba4f..7af3f2b9e7c5f911ef4b1cff37aa5088fad6dbf5 --no-merges --oneline 7af3f2b9e Roll buildroot to ce7b5c786a12927c9e0b4543af267d48c52e0b3a ([flutter/engine#8593](https://github.com/flutter/engine/issues/8593)) d2eeceb94 Roll src/third_party/skia 6c431d52020c..68aeec07de4a (7 commits) ([flutter/engine#8590](https://github.com/flutter/engine/issues/8590))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Sam Rawlins <<EMAIL>>|Bump dartdoc to 0.28.3+2 ([#31148](https://github.com/flutter/flutter/issues/31148))  This version of dartdoc properly escapes fenced code block "info strings."|
|Jonah Williams <<EMAIL>>|Fuchsia step 1: add SDK version file and artifact download ([#31073](https://github.com/flutter/flutter/issues/31073))|
|Gary Qian <<EMAIL>>|Use full height of the glyph for caret height on Android ([#30991](https://github.com/flutter/flutter/issues/30991))|
|Dima Rostopira <<EMAIL>>|Add sorting to flutter version command ([#31064](https://github.com/flutter/flutter/issues/31064))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 9970fb550071..7d1da567434a (2 commits) ([#31129](https://github.com/flutter/flutter/issues/31129))   https://github.com/flutter/engine/compare/9970fb550071...7d1da567434a  git log 9970fb5500710471c031cb86f899bfc3f4a6bafd..7d1da567434a820853ed6fae1f6ed65b9de5ba4f --no-merges --oneline 7d1da5674 Roll src/third_party/skia 5bee533ef3a3..6c431d52020c (1 commits) ([flutter/engine#8589](https://github.com/flutter/engine/issues/8589)) d8a2a55a6 Roll src/third_party/skia 9d10206b7add..5bee533ef3a3 (4 commits) ([flutter/engine#8587](https://github.com/flutter/engine/issues/8587))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Greg Spencer <<EMAIL>>|Update keycodes, fix a comment. ([#30938](https://github.com/flutter/flutter/issues/30938))  Updates the keycodes by regenerating them using the script, and fixed a comment in the template to conform to style.|
|engine-flutter-autoroll <<EMAIL>>|9970fb550 Roll src/third_party/skia d51d00745b1d..9d10206b7add (1 commits) ([flutter/engine#8586](https://github.com/flutter/engine/issues/8586)) ([#31106](https://github.com/flutter/flutter/issues/31106))   https://github.com/flutter/engine/compare/0575a9b46e99...9970fb550071  git log 0575a9b46e994170ce1290f0025623138411dd13..9970fb5500710471c031cb86f899bfc3f4a6bafd --no-merges --oneline 9970fb550 Roll src/third_party/skia d51d00745b1d..9d10206b7add (1 commits) ([flutter/engine#8586](https://github.com/flutter/engine/issues/8586))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Alexander Aprelev <<EMAIL>>|Download and handle product version of flutter patched sdk ([#31063](https://github.com/flutter/flutter/issues/31063))  * Support release/debug flavors of flutter_patched_sdk  * Use [anyNamed] instead of [any] for mocking named arguments  * Fix use of local engine in release mode|
|Tom Robiquet <<EMAIL>>|Added opacity to cupertino switch when disabled ([#29451](https://github.com/flutter/flutter/issues/29451))|
|David Worsham <<EMAIL>>|[scenic] Remove dead view_manager ref ([#31005](https://github.com/flutter/flutter/issues/31005))|
|engine-flutter-autoroll <<EMAIL>>|0575a9b46 Roll src/third_party/skia bf15b6676843..d51d00745b1d (6 commits) ([flutter/engine#8584](https://github.com/flutter/engine/issues/8584)) ([#31098](https://github.com/flutter/flutter/issues/31098))   https://github.com/flutter/engine/compare/324b8403e8a6...0575a9b46e99  git log 324b8403e8a67db7376f85ed29e0a5792174cca0..0575a9b46e994170ce1290f0025623138411dd13 --no-merges --oneline 0575a9b46 Roll src/third_party/skia bf15b6676843..d51d00745b1d (6 commits) ([flutter/engine#8584](https://github.com/flutter/engine/issues/8584))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Add null checks to coverage collection ([#31092](https://github.com/flutter/flutter/issues/31092))|
|xster <<EMAIL>>|Make CupertinoNavigationBarBackButton correctly return an assert error ([#30815](https://github.com/flutter/flutter/issues/30815))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine c3824f06933a..324b8403e8a6 (13 commits) ([#31082](https://github.com/flutter/flutter/issues/31082))   https://github.com/flutter/engine/compare/c3824f06933a...324b8403e8a6  git log c3824f06933af54b54d1094594c0159828dd1a45..324b8403e8a67db7376f85ed29e0a5792174cca0 --no-merges --oneline 324b8403e Remove the flutter_aot GN argument. ([flutter/engine#8581](https://github.com/flutter/engine/issues/8581)) 571ce945b Roll src/third_party/skia 1875353110d1..bf15b6676843 (8 commits) ([flutter/engine#8582](https://github.com/flutter/engine/issues/8582)) b7d484e4f Roll src/third_party/skia 1fe0b86f17f3..1875353110d1 (5 commits) ([flutter/engine#8580](https://github.com/flutter/engine/issues/8580)) 73f455a3c Roll src/third_party/skia e1c5ea6779f4..1fe0b86f17f3 (1 commits) ([flutter/engine#8579](https://github.com/flutter/engine/issues/8579)) 42d06b370 Roll src/third_party/skia 3611ee1bb157..e1c5ea6779f4 (3 commits) ([flutter/engine#8578](https://github.com/flutter/engine/issues/8578)) eb575e24a Roll src/third_party/skia c9f55de2ed39..3611ee1bb157 (1 commits) ([flutter/engine#8577](https://github.com/flutter/engine/issues/8577)) 40473d39a Roll src/third_party/skia b5e57e9a3d0f..c9f55de2ed39 (1 commits) ([flutter/engine#8576](https://github.com/flutter/engine/issues/8576)) dc952bcd4 Roll src/third_party/skia 5c6b565bdfb9..b5e57e9a3d0f (1 commits) ([flutter/engine#8575](https://github.com/flutter/engine/issues/8575)) 14a1db2b6 Roll src/third_party/skia 12cf258193dc..5c6b565bdfb9 (1 commits) ([flutter/engine#8573](https://github.com/flutter/engine/issues/8573)) dcd0209bd Roll src/third_party/skia 33233a09fef8..12cf258193dc (1 commits) ([flutter/engine#8572](https://github.com/flutter/engine/issues/8572)) e0b9bc18f Roll src/third_party/skia 69f54f8f0f22..33233a09fef8 (1 commits) ([flutter/engine#8571](https://github.com/flutter/engine/issues/8571)) 9f9d5d6da Roll src/third_party/skia 990bfc785891..69f54f8f0f22 (1 commits) ([flutter/engine#8570](https://github.com/flutter/engine/issues/8570)) 7c7df0d58 Add null check in FLETextInputPlugin ([flutter/engine#8538](https://github.com/flutter/engine/issues/8538))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Justin McCandless <<EMAIL>>|2d transforms UX improvements ([#30932](https://github.com/flutter/flutter/issues/30932))  Small usability fixes for pan and zoom demo. Title and instruction dialog.|
|liyuqian <<EMAIL>>|Allow profile widget builds in profile mode ([#30990](https://github.com/flutter/flutter/issues/30990))  ## Description  Previously, such function is only available in the debug mode. But the performance information is very noisy in debug mode with JIT. I feel that such function is as important and useful as the performance overlay and the `--trace-skia` option for the GPU thread.  So we should give it the same ability to run in both profile and debug mode.  I've tested it using flutter_gallery in the profile mode. There's no observable difference in the performance overlay between toggling widget build profiling.  ## Related Issues  https://github.com/flutter/flutter/issues/30984|
|Dan Field <<EMAIL>>|Check that ErrorWidget.builder is not modified after test ([#30898](https://github.com/flutter/flutter/issues/30898))|
|Jonah Williams <<EMAIL>>|make flutterProject option of CoverageCollector optional ([#31074](https://github.com/flutter/flutter/issues/31074))|
|liyuqian <<EMAIL>>|Fix the warning test by checking stderr ([#30997](https://github.com/flutter/flutter/issues/30997))  Previously, I used the Android emulator for testing and everything seemed to work fine with stdout (if I remember correctly). But our devicelab uses real Android devices and the warnings are routed to stderr. Hence change stdout to stderr in the test.|
|Christopher Fujino <<EMAIL>>|New flag to `flutter drive` to skip installing fresh app on device ([#30818](https://github.com/flutter/flutter/issues/30818))  * add a --build/--no-build flag to flutter drive command|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 9aa7c9a48e93..c3824f06933a (13 commits) ([#31002](https://github.com/flutter/flutter/issues/31002))   https://github.com/flutter/engine/compare/9aa7c9a48e93...c3824f06933a  git log 9aa7c9a48e9342a450c3078e15c8d7923a338ede..c3824f06933af54b54d1094594c0159828dd1a45 --no-merges --oneline c3824f069 Roll src/third_party/skia e62bf561638c..990bfc785891 (2 commits) ([flutter/engine#8569](https://github.com/flutter/engine/issues/8569)) 49a72e1e4 Roll src/third_party/skia 8be917af4313..e62bf561638c (5 commits) ([flutter/engine#8568](https://github.com/flutter/engine/issues/8568)) 7292d62ef Revert Versions API ([flutter/engine#7828](https://github.com/flutter/engine/issues/7828)) 9336671a8 Revert &[#34](https://github.com/flutter/flutter/issues/34);Roll src/third_party/dart a8f3a5dae6..c2eb9a9860 (8 commits)&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8567](https://github.com/flutter/engine/issues/8567)) a54354349 Roll src/third_party/dart a8f3a5dae6..c2eb9a9860 (8 commits) 23b0e02ec Android Embedding PR29: Improve FlutterFragment construction API &[#43](https://github.com/flutter/flutter/issues/43); engine config API. ([flutter/engine#8540](https://github.com/flutter/engine/issues/8540)) e6c822db9 Roll src/third_party/skia 25071cc52b4b..8be917af4313 (6 commits) ([flutter/engine#8564](https://github.com/flutter/engine/issues/8564)) 1bb2c0cf0 Remove unused import in FlutterActivityDelegate ([flutter/engine#8563](https://github.com/flutter/engine/issues/8563)) ad04340e5 Add missing &lt;memory&gt; include to text_input_model.h ([flutter/engine#8562](https://github.com/flutter/engine/issues/8562)) fcd717e28 Update README.md to point to flutter.dev ([flutter/engine#8557](https://github.com/flutter/engine/issues/8557)) 501892a92 Roll src/third_party/skia 35f1c154c5e5..25071cc52b4b (7 commits) ([flutter/engine#8560](https://github.com/flutter/engine/issues/8560)) db99c86ab Roll src/third_party/skia 41476708db86..35f1c154c5e5 (1 commits) ([flutter/engine#8559](https://github.com/flutter/engine/issues/8559)) a88cd8032 Roll src/third_party/skia f74fff660084..41476708db86 (1 commits) ([flutter/engine#8558](https://github.com/flutter/engine/issues/8558))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Add rrect contains microbenchmark ([#30985](https://github.com/flutter/flutter/issues/30985))|
|Jonah Williams <<EMAIL>>|Fix bugs in contrast guideline and improve heuristic ([#31000](https://github.com/flutter/flutter/issues/31000))|
|Jonah Williams <<EMAIL>>|Make coverage, like, really fast ([#30811](https://github.com/flutter/flutter/issues/30811))|
|Jonah Williams <<EMAIL>>|Revert "revert last 2 engine rolls ([#30995](https://github.com/flutter/flutter/issues/30995))" ([#30998](https://github.com/flutter/flutter/issues/30998))|
|Jonah Williams <<EMAIL>>|revert last 2 engine rolls ([#30995](https://github.com/flutter/flutter/issues/30995))|
|engine-flutter-autoroll <<EMAIL>>|9aa7c9a48 Export extern constants in embedder.h ([flutter/engine#8550](https://github.com/flutter/engine/issues/8550)) ([#30951](https://github.com/flutter/flutter/issues/30951))   https://github.com/flutter/engine/compare/f804c293f3ba...9aa7c9a48e93  git log f804c293f3baaec9ccec1e251293f6a3509223b8..9aa7c9a48e9342a450c3078e15c8d7923a338ede --no-merges --oneline 9aa7c9a48 Export extern constants in embedder.h ([flutter/engine#8550](https://github.com/flutter/engine/issues/8550))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 72986c39ea36..f804c293f3ba (23 commits) ([#30948](https://github.com/flutter/flutter/issues/30948))   https://github.com/flutter/engine/compare/72986c39ea36...f804c293f3ba  git log 72986c39ea3669ef26d4d7d7cd0c44f513fcd117..f804c293f3baaec9ccec1e251293f6a3509223b8 --no-merges --oneline f804c293f Roll src/third_party/skia 3b60397fd35d..f74fff660084 (3 commits) ([flutter/engine#8556](https://github.com/flutter/engine/issues/8556)) 1b448e555 Roll Dart 15b11b018364ce032eae50d78fc8a52b541e2bce...a8f3a5dae6203d1064726a5953cf06a7d484249c ([flutter/engine#8555](https://github.com/flutter/engine/issues/8555)) 99259aef5 Roll src/third_party/skia 36477b49c2ef..3b60397fd35d (6 commits) ([flutter/engine#8554](https://github.com/flutter/engine/issues/8554)) f37220d40 Roll src/third_party/skia c33e6dcc700b..36477b49c2ef (8 commits) ([flutter/engine#8552](https://github.com/flutter/engine/issues/8552)) d84d204c7 Android Embedding PR30: Make FlutterView focusable so that the keyboard can interact with it. ([flutter/engine#8551](https://github.com/flutter/engine/issues/8551)) a344015e9 [fuchsia] Add flutter:: to scene_host.cc ([flutter/engine#8549](https://github.com/flutter/engine/issues/8549)) 711d8431d Roll src/third_party/skia 76e626d9bb55..c33e6dcc700b (4 commits) ([flutter/engine#8547](https://github.com/flutter/engine/issues/8547)) 82e6d6899 Eliminate unused write to local ([flutter/engine#8541](https://github.com/flutter/engine/issues/8541)) f53e477a6 Correct nullability for FlutterStandardReader ([flutter/engine#8537](https://github.com/flutter/engine/issues/8537)) c00364a6c [font_collection] Add missing semicolon ([flutter/engine#8546](https://github.com/flutter/engine/issues/8546)) ae4df6fd1 Revert &[#34](https://github.com/flutter/flutter/issues/34);Change Rect internal representation from Float32List to Float64List ([#8524](https://github.com/flutter/flutter/issues/8524))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8545](https://github.com/flutter/engine/issues/8545)) ffdddb07e Roll src/third_party/skia 6d60534e95d8..76e626d9bb55 (1 commits) ([flutter/engine#8544](https://github.com/flutter/engine/issues/8544)) 5ac728eb6 Roll src/third_party/skia 4d657d5e894a..6d60534e95d8 (3 commits) ([flutter/engine#8543](https://github.com/flutter/engine/issues/8543)) ee462ff68 Roll src/third_party/skia 99d792276740..4d657d5e894a (1 commits) ([flutter/engine#8542](https://github.com/flutter/engine/issues/8542)) 18816c50f Roll src/third_party/skia 42280f8961fa..99d792276740 (8 commits) ([flutter/engine#8539](https://github.com/flutter/engine/issues/8539)) 892591da8 Android Embedding PR28: Report app is active to Flutter in FlutterFragment.onResume() instead of onPostResume() forwarded from Activity. ([flutter/engine#8536](https://github.com/flutter/engine/issues/8536)) 330c6c168 Use code cache dir for engine cache on API &gt;= 21 ([#14704](https://github.com/flutter/flutter/issues/14704)). ([flutter/engine#8534](https://github.com/flutter/engine/issues/8534)) c10ae2be2 Add an option to build the GLFW shell on macOS ([flutter/engine#8531](https://github.com/flutter/engine/issues/8531)) 29aa5fca0 Roll src/third_party/skia b5c685991faa..42280f8961fa (32 commits) ([flutter/engine#8535](https://github.com/flutter/engine/issues/8535)) 0a2869e5c Added support for authentication codes for the VM service ([flutter/engine#8527](https://github.com/flutter/engine/issues/8527)) 36d2135ad Redo a fix for cull rect calculation on TransformLayers with a perspective transform ([flutter/engine#8528](https://github.com/flutter/engine/issues/8528)) 0b36d3e2f Change Rect internal representation from Float32List to Float64List ([flutter/engine#8524](https://github.com/flutter/engine/issues/8524)) b1ae0ccb7 Roll src/third_party/skia 10bf7020aa15..b5c685991faa (3 commits) ([flutter/engine#8526](https://github.com/flutter/engine/issues/8526))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Emily Fortuna <<EMAIL>>|Allow mouse hover to only respond to some mouse events but not all. ([#30886](https://github.com/flutter/flutter/issues/30886))  * Allow mouse hover to only respond to some mouse events but not all.|
|chunhtai <<EMAIL>>|Fix issue 23527: Exception: RenderViewport exceeded its maximum number of layout cycles ([#30809](https://github.com/flutter/flutter/issues/30809))|
|Greg Spencer <<EMAIL>>|Keep hover annotation layers in sync with the mouse detector. ([#30829](https://github.com/flutter/flutter/issues/30829))  Adds a paint after detaching/attaching hover annotations to keep the annotation layers in sync with the annotations attached to the mouse detector.  Fixes [#30744](https://github.com/flutter/flutter/issues/30744)|
|Siva <<EMAIL>>|Use identical instead of '==' in a constant expression. ([#30921](https://github.com/flutter/flutter/issues/30921))  * Use identical instead of '==' in a constant expression.  * Fix analyzer warnings.  * Fix analyzer warnings.|
|Jonah Williams <<EMAIL>>|Add toggle for debugProfileWidgetBuilds ([#30867](https://github.com/flutter/flutter/issues/30867))|
|Jonah Williams <<EMAIL>>|Revert "Manual engine roll with disabled service authentication codes ([#30919](https://github.com/flutter/flutter/issues/30919))" ([#30930](https://github.com/flutter/flutter/issues/30930))  This reverts commit 19d6e7b61390311f0b285662ba5e977bae7b0382.|
|Ben Konyi <<EMAIL>>|Manual engine roll with disabled service authentication codes ([#30919](https://github.com/flutter/flutter/issues/30919))|
|Justin McCandless <<EMAIL>>|Baseline Aligned Row ([#30746](https://github.com/flutter/flutter/issues/30746))  Text in a baseline aligned row now works, and the height of the row is properly calculated.|
|MH Johnson <<EMAIL>>|[Material] Fix showDialog crasher caused by old contexts ([#30754](https://github.com/flutter/flutter/issues/30754))  * Fix bug, add regression test.  * remove unnecessary space|
|Dan Field <<EMAIL>>|Let `sliver.dart` `_createErrorWidget` work with other Widgets ([#30880](https://github.com/flutter/flutter/issues/30880))  Credit to @ymback|
|xster <<EMAIL>>|Add more dialog doc cross-reference ([#30887](https://github.com/flutter/flutter/issues/30887))|
|Jonah Williams <<EMAIL>>|Allow downloading of desktop embedding artifacts ([#30648](https://github.com/flutter/flutter/issues/30648))|
|LongCatIsLooong <<EMAIL>>|CupertinoDatePicker initialDateTime accounts for minuteInterval  ([#30862](https://github.com/flutter/flutter/issues/30862))  initialDateTime should account for minuteInterval|
|LongCatIsLooong <<EMAIL>>|add golden tests for CupertinoDatePicker ([#30828](https://github.com/flutter/flutter/issues/30828))  Add golden tests for CupertinoDatePicker|
|liyuqian <<EMAIL>>|Simplify toImage future handling ([#30876](https://github.com/flutter/flutter/issues/30876))  Thanks to cbracken@'s suggestion on another Google-internal change.|
|champeauxr <<EMAIL>>|Fixed Table flex column layout error [#30437](https://github.com/flutter/flutter/issues/30437) ([#30470](https://github.com/flutter/flutter/issues/30470))  RenderTable._computeColumnWidths() had a logic error that caused flex columns to be collapsed to their minimum widths in certain situations dependent on the layout width constraint and the number of flex columns.|
|xster <<EMAIL>>|Fix iTunes Transporter quirk ([#30883](https://github.com/flutter/flutter/issues/30883))|
|Greg Spencer <<EMAIL>>|Bump Android build tools to 28.0.3 in Dockerfile ([#30832](https://github.com/flutter/flutter/issues/30832))  Bumps the Android build tools to 28.0.3 instead of 28.0.0 in the Dockerfile, since that is what flutter doctor wants.|
|xster <<EMAIL>>|Update the upload key which seems to have trouble for some reason ([#30871](https://github.com/flutter/flutter/issues/30871))|
|Dan Field <<EMAIL>>|Check for invalid elevations ([#30215](https://github.com/flutter/flutter/issues/30215))  * Check for invalid elevation usage in the layer tree|
|Sam Rawlins <<EMAIL>>|Fix additional @mustCallSuper indirect overrides and mixins ([#30667](https://github.com/flutter/flutter/issues/30667))|
|Todd Volkert <<EMAIL>>|Revert "Add Form.onSaved ([#30643](https://github.com/flutter/flutter/issues/30643))" ([#30793](https://github.com/flutter/flutter/issues/30793))  This reverts commit 949023b27686fb80a0047157490d6c4c61995cef.|
|Tong Mu <<EMAIL>>|Revert "Remove pressure customization from some pointer events ([#30414](https://github.com/flutter/flutter/issues/30414))" ([#30873](https://github.com/flutter/flutter/issues/30873))  This reverts commit a821136997b600f0cd39cbcd3d993c54a15f4686.|
|Tong Mu <<EMAIL>>|Remove pressure customization from some pointer events ([#30414](https://github.com/flutter/flutter/issues/30414))  * Remove pressure from PointerHoverEvent ctor|
|Jonah Williams <<EMAIL>>|warn on uncomitted changes ([#30235](https://github.com/flutter/flutter/issues/30235))|
|Jonah Williams <<EMAIL>>|Add semanticsLabel parameter to TextSpan ([#30837](https://github.com/flutter/flutter/issues/30837))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 905c571db50d..72986c39ea36 (2 commits) ([#30861](https://github.com/flutter/flutter/issues/30861))   https://github.com/flutter/engine/compare/905c571db50d...72986c39ea36  git log 905c571db50d5eb3b10eaa4d3c43c99321032223..72986c39ea3669ef26d4d7d7cd0c44f513fcd117 --no-merges --oneline 72986c39e Move constant definitions out embedder.h ([flutter/engine#8498](https://github.com/flutter/engine/issues/8498)) e356dbca2 Merge flutter/synchronization contents into fml. ([flutter/engine#8525](https://github.com/flutter/engine/issues/8525))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Justin McCandless <<EMAIL>>|Fix cursor outside of input width ([#30525](https://github.com/flutter/flutter/issues/30525))  * Disallow cursor from appearing beyond the width of the input.  * Test that verifies the cursor can't exceed the width of the input  * Use constant from editable.dart to explain 1 pixel difference in test  * Fix failing test that tested the case of overflowing spaces|
|xster <<EMAIL>>|Cupertino localization step 7: modularize material specific things out of gen_localizations.dart ([#29822](https://github.com/flutter/flutter/issues/29822))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine eec74e5c9231..905c571db50d (9 commits) ([#30833](https://github.com/flutter/flutter/issues/30833))   https://github.com/flutter/engine/compare/eec74e5c9231...905c571db50d  git log eec74e5c92313f01a28dfbeb78d96e81231edab7..905c571db50d5eb3b10eaa4d3c43c99321032223 --no-merges --oneline 905c571db Add windows host_debug_unopt build ([flutter/engine#8515](https://github.com/flutter/engine/issues/8515)) 39e1d00f3 Remove redundant specification of the |flutter| namespace in the engine. ([flutter/engine#8523](https://github.com/flutter/engine/issues/8523)) 56052c70a Rename the shell namespace to flutter. ([flutter/engine#8520](https://github.com/flutter/engine/issues/8520)) ca1d163d4 Support message loops whose tasks are executed concurrently. ([flutter/engine#8419](https://github.com/flutter/engine/issues/8419)) 8ae84ec3d Roll src/third_party/skia de8362c6f212..10bf7020aa15 (5 commits) ([flutter/engine#8522](https://github.com/flutter/engine/issues/8522)) 2dbd981d3 Roll src/third_party/dart 06ed3f598c..1490a90bc1 (8 commits) 527913268 Android Embedding PR27: Fix SurfaceView flicker in Fragment transactions ([flutter/engine#8504](https://github.com/flutter/engine/issues/8504)) 86ab45cd5 Remove the unused EnableBlink flag. ([flutter/engine#8518](https://github.com/flutter/engine/issues/8518)) d5880a609 Roll src/third_party/skia b5d8c634f95b..de8362c6f212 (14 commits) ([flutter/engine#8519](https://github.com/flutter/engine/issues/8519))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Fix StatefulWidget and StatelessWidget Sample Documentation ([#30814](https://github.com/flutter/flutter/issues/30814))|
|liyuqian <<EMAIL>>|Print warning if flutter drive is run in debug ([#30747](https://github.com/flutter/flutter/issues/30747))  ## Description  Print actionable warnings if `flutter drive` (where most of our performance benchmarks come from) is run in debug mode and it tries to gather benchmarks using `traceAction`.  ## Related Issues  https://github.com/flutter/flutter/issues/30625  ## Tests  I added the following tests: * drive_perf_debug_warning devicelab test|
|xster <<EMAIL>>|Cupertino localization step 11: add more translation clarifications in the instructions ([#30527](https://github.com/flutter/flutter/issues/30527))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 6a0460c33ff7..eec74e5c9231 (6 commits) ([#30813](https://github.com/flutter/flutter/issues/30813))   https://github.com/flutter/engine/compare/6a0460c33ff7...eec74e5c9231  git log 6a0460c33ff7e4f18b809285301337698de9ab38..eec74e5c92313f01a28dfbeb78d96e81231edab7 --no-merges --oneline eec74e5c9 Rename the blink namespace to flutter. ([flutter/engine#8517](https://github.com/flutter/engine/issues/8517)) fff80a0d9 Roll src/third_party/dart 06ed3f598c..06ed3f598c (0 commits) 000f8e31a Remove dart_utf_tag from DEPS 9bfa96dee Add trace events for creating minikin fonts ([flutter/engine#8477](https://github.com/flutter/engine/issues/8477)) 5381e57ea Roll src/third_party/skia 9a7c7be15969..b5d8c634f95b (11 commits) ([flutter/engine#8513](https://github.com/flutter/engine/issues/8513)) df707c6f1 switch to newer APIs for shaders and filters ([flutter/engine#8511](https://github.com/flutter/engine/issues/8511))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Update ExpansionPanelList Samples with Scaffold template ([#30805](https://github.com/flutter/flutter/issues/30805))|
|Shi-Hao Hong <<EMAIL>>|Update ListTile sample snippets to use Material Scaffold Template ([#30800](https://github.com/flutter/flutter/issues/30800))|
|ShaLi Shaltiel <<EMAIL>>|Updated package template .gitignore file ([#27034](https://github.com/flutter/flutter/issues/27034))|
|Shi-Hao Hong <<EMAIL>>|Embedded images and variations to ListTile sample code ([#30537](https://github.com/flutter/flutter/issues/30537))  Also includes counter-examples for list items that differ from ListTile.|
|liyuqian <<EMAIL>>|Mark cubic_bezier_perf__timeline_summary nonflaky ([#30767](https://github.com/flutter/flutter/issues/30767))|
|Michael Thomsen <<EMAIL>>|Forward missing pub commands ([#30115](https://github.com/flutter/flutter/issues/30115))|
|engine-flutter-autoroll <<EMAIL>>|6a0460c33 Roll src/third_party/skia 097e77cd3ba0..9a7c7be15969 (1 commits) ([flutter/engine#8512](https://github.com/flutter/engine/issues/8512)) ([#30785](https://github.com/flutter/flutter/issues/30785))   https://github.com/flutter/engine/compare/2335dd2f8acf...6a0460c33ff7  git log 2335dd2f8acfbbe9af5db626fd9c71031492fa8f..6a0460c33ff7e4f18b809285301337698de9ab38 --no-merges --oneline 6a0460c33 Roll src/third_party/skia 097e77cd3ba0..9a7c7be15969 (1 commits) ([flutter/engine#8512](https://github.com/flutter/engine/issues/8512))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|fix cast NPE in invokeListMethod and invokeMapMathod ([#30760](https://github.com/flutter/flutter/issues/30760))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 8a465596533d..2335dd2f8acf (2 commits) ([#30780](https://github.com/flutter/flutter/issues/30780))   https://github.com/flutter/engine/compare/8a465596533d...2335dd2f8acf  git log 8a465596533d75eb128faab5cd70b60c5e989935..2335dd2f8acfbbe9af5db626fd9c71031492fa8f --no-merges --oneline 2335dd2f8 Roll src/third_party/skia 696e8dda3ca3..097e77cd3ba0 (1 commits) ([flutter/engine#8510](https://github.com/flutter/engine/issues/8510)) 4e839444f Roll src/third_party/skia 0ea414a08208..696e8dda3ca3 (3 commits) ([flutter/engine#8509](https://github.com/flutter/engine/issues/8509))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine ff1bcdc00941..8a465596533d (21 commits) ([#30768](https://github.com/flutter/flutter/issues/30768))   https://github.com/flutter/engine/compare/ff1bcdc00941...8a465596533d  git log ff1bcdc00941188ba27123a4857b443e3c9392bb..8a465596533d75eb128faab5cd70b60c5e989935 --no-merges --oneline 8a4655965 Route FlutterEventTracer events to Fuchsia tracing for Fuchsia ([flutter/engine#8499](https://github.com/flutter/engine/issues/8499)) 7ce64be6c [scenic] Remove unused mozart.internal ([flutter/engine#8496](https://github.com/flutter/engine/issues/8496)) edb050f7a Roll src/third_party/skia 2f08a4043b26..0ea414a08208 (2 commits) ([flutter/engine#8506](https://github.com/flutter/engine/issues/8506)) 623dca053 Roll src/third_party/dart b23817105c..cc63d6e647 (8 commits) c7b138d82 [scenic][SCN-1054] remove dangling uses of SetTranslationRH ([flutter/engine#8503](https://github.com/flutter/engine/issues/8503)) 53620352a Roll src/third_party/dart aacc0b0709..b23817105c (8 commits) 046b3cd28 Get rid of the macro for accessing the current test name. ([flutter/engine#8500](https://github.com/flutter/engine/issues/8500)) 533ca016e Roll src/third_party/skia 6b797fec83f0..2f08a4043b26 (8 commits) ([flutter/engine#8501](https://github.com/flutter/engine/issues/8501)) 612abee90 Wire up support for Dart fixtures in shell_unittests. ([flutter/engine#8497](https://github.com/flutter/engine/issues/8497)) dbcfc504c Android Embedding PR26: Offer an async version of FlutterMain&[#39](https://github.com/flutter/flutter/issues/39);s ensure initialization complete. ([flutter/engine#8465](https://github.com/flutter/engine/issues/8465)) a930ca893 Roll src/third_party/dart 8e4dcf48c4..aacc0b0709 (4 commits) acfc83179 Roll src/third_party/skia 602df41025ea..6b797fec83f0 (19 commits) ([flutter/engine#8494](https://github.com/flutter/engine/issues/8494)) 3558fa65a [Docs] Correcting link to contributing guide. ([flutter/engine#8472](https://github.com/flutter/engine/issues/8472)) 0bf708ee1 Refactor ios play input sound logic. ([flutter/engine#7783](https://github.com/flutter/engine/issues/7783)) 78bd318c9 Add Locale.toLanguageTag() ([flutter/engine#8421](https://github.com/flutter/engine/issues/8421)) efb3b003f Roll src/third_party/dart 8e0a669e74..8e4dcf48c4 (8 commits) 075b4899d Support ContextWrapper when FlutterView is instantiated within a Fragment. ([flutter/engine#7776](https://github.com/flutter/engine/issues/7776)) 85ca8c485 Roll src/third_party/skia 053b2939938c..602df41025ea (21 commits) ([flutter/engine#8492](https://github.com/flutter/engine/issues/8492)) a6b8e70ad Remove unused variable ([flutter/engine#8490](https://github.com/flutter/engine/issues/8490)) 377dc6400 Roll src/third_party/dart 86b5304ccd..8e0a669e74 (6 commits) 7ee3a31c5 Roll src/third_party/dart 907c514c89..86b5304ccd (27 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|register gradle wrapper as universal artifact ([#30755](https://github.com/flutter/flutter/issues/30755))|
|Tim Sneath <<EMAIL>>|Update README.md ([#30594](https://github.com/flutter/flutter/issues/30594))  * Rewrite README with text from Flutter announcement|
|xster <<EMAIL>>|Cupertino localization step 6: add a GlobalCupertinoLocalizations base class with date time formatting ([#29767](https://github.com/flutter/flutter/issues/29767))|
|Jonah Williams <<EMAIL>>|Reland: Ensure that flutter run/drive/test/update_packages only downloads required artifacts  ([#30254](https://github.com/flutter/flutter/issues/30254))|
|Jonah Williams <<EMAIL>>|Allow disabling experimental commands, devices on stable branch ([#30153](https://github.com/flutter/flutter/issues/30153))|
|Lau Ching Jun <<EMAIL>>|Mark ios-deploy version 2.0.0 as bad ([#30578](https://github.com/flutter/flutter/issues/30578))  Mark ios-deploy version 2.0.0 as bad.  ios-deploy before version 1.9.4 declares itself as v2.0.0 https://github.com/ios-control/ios-deploy/commits/main/src/ios-deploy/version.h|
|rami-a <<EMAIL>>|Prevent vertical scroll in shrine by ensuring card size fits the screen ([#30455](https://github.com/flutter/flutter/issues/30455))|
|Todd Volkert <<EMAIL>>|Add `const Border.uniform()` ([#30640](https://github.com/flutter/flutter/issues/30640))  `Border.all()` is a factory constructor and thus not const constructible. This change adds a `const Border.uniform()` constructor and makes `Border.all()` delegate to it. This allows callers to more likely be able to make their widget tree const constructible.|
|Todd Volkert <<EMAIL>>|Add Form.onSaved ([#30643](https://github.com/flutter/flutter/issues/30643))  When submitting data to a server, callers need a callback that will get invoked after all the individual form fields are saved. If they have a button that submits the form, they could just do this logic in the click handler for the button (save the form, then submit to the server), but if they have more ways than one to submit the form (i.e. hitting enter while in a text form field), then it becomes more convoluted and calls for a unified callback that will get notified when the form is submitted.|
|Todd Volkert <<EMAIL>>|Make FormField._validate() return void ([#30644](https://github.com/flutter/flutter/issues/30644))  None of its callers were using the return value, so no need to invoke the `isValid` getter unnecessarily.|
|Todd Volkert <<EMAIL>>|Add docs to FormFieldValidator ([#30645](https://github.com/flutter/flutter/issues/30645))  To describe what its return value represents|
|engine-flutter-autoroll <<EMAIL>>|ff1bcdc00 Add Views V2 support for Fuchsia ([flutter/engine#8115](https://github.com/flutter/engine/issues/8115)) ([#30633](https://github.com/flutter/flutter/issues/30633))   https://github.com/flutter/engine/compare/0c393d64e4d1...ff1bcdc00941  git log 0c393d64e4d1c4cd408a591eac72ba05cdc2c0da..ff1bcdc00941188ba27123a4857b443e3c9392bb --no-merges --oneline ff1bcdc00 Add Views V2 support for Fuchsia ([flutter/engine#8115](https://github.com/flutter/engine/issues/8115))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Add sample for ValueListenableBuilder ([#30626](https://github.com/flutter/flutter/issues/30626))  * Add sample for ValueListenableBuilder|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 6bc33b5e1442..0c393d64e4d1 (12 commits) ([#30623](https://github.com/flutter/flutter/issues/30623))   https://github.com/flutter/engine/compare/6bc33b5e1442...0c393d64e4d1  git log 6bc33b5e144254ceab274298dd61d9d9ed0dcc8f..0c393d64e4d1c4cd408a591eac72ba05cdc2c0da --no-merges --oneline 0c393d64e Roll dart back to 907c514c8937cf76e ([flutter/engine#8473](https://github.com/flutter/engine/issues/8473)) fea42a259 Roll src/third_party/dart 9e1adc54a7..389ccc9000 (10 commits) d6cf2bda1 Roll src/third_party/dart da4ccd0855..9e1adc54a7 (3 commits) 0c2a2c1c4 Initialize OpacityLayer&[#39](https://github.com/flutter/flutter/issues/39);s matrix to identity ([flutter/engine#8467](https://github.com/flutter/engine/issues/8467)) 7fd4caf03 Roll src/third_party/dart 1ad11facec..da4ccd0855 (6 commits) d3fbaea9a Android Embedding PR25: Prevent black rectangle when launching FlutterActivity ([flutter/engine#8460](https://github.com/flutter/engine/issues/8460)) 99da038da Document the leak_vm flag. ([flutter/engine#8462](https://github.com/flutter/engine/issues/8462)) ce7c21ce9 Log the correct function on error in the embedder. ([flutter/engine#8461](https://github.com/flutter/engine/issues/8461)) e10d46421 Improve path metrics tests and docs ([flutter/engine#7851](https://github.com/flutter/engine/issues/7851)) 424045c3f Enable shutting down all root isolates in a VM. ([flutter/engine#8457](https://github.com/flutter/engine/issues/8457)) 816e3dc0f Roll src/third_party/dart 907c514c89..1ad11facec (7 commits) 45dc353ee Roll src/third_party/skia 0e35ce27e0e9..053b2939938c (10 commits) ([flutter/engine#8458](https://github.com/flutter/engine/issues/8458))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|xster <<EMAIL>>|Fix gallery deploy on iOS ([#22810](https://github.com/flutter/flutter/issues/22810))|
|Hans Muller <<EMAIL>>|Fixed a typo in the Expanded API doc ([#30563](https://github.com/flutter/flutter/issues/30563))|
|Taym Haddadi <<EMAIL>>|Add confirmDismiss example to flutter_gallery ([#30497](https://github.com/flutter/flutter/issues/30497))|
|LongCatIsLooong <<EMAIL>>|Provide a default IconTheme in CupertinoTheme ([#30521](https://github.com/flutter/flutter/issues/30521))  -* Inserted an `IconTheme` widget under `CupertinoTheme` to provide a default icon color when using `CupertinoTheme`. * Changed `CupertinoTheme` to a `StatelessWidget`, to match the implementation of `Theme` * Changed the nesting order of `Theme`'s sub widgets, to let `Theme.iconTheme` take precedence, so that `Theme`'s behavior is kept as is.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 2fe4439178f3..6bc33b5e1442 (3 commits) ([#30576](https://github.com/flutter/flutter/issues/30576))   https://github.com/flutter/engine/compare/2fe4439178f3...6bc33b5e1442  git log 2fe4439178f3e09ddaa4d016688691a1adc65ed6..6bc33b5e144254ceab274298dd61d9d9ed0dcc8f --no-merges --oneline 6bc33b5e1 Android Embedding PR24: Allow FlutterActivity to provide an engine, also adjust FlutterFragment timing to avoid Activity launch lag. ([flutter/engine#8448](https://github.com/flutter/engine/issues/8448)) 08a133508 More detailed comments for engine build windows VM ([flutter/engine#8456](https://github.com/flutter/engine/issues/8456)) 1854b8571 Roll src/third_party/dart a2709992a2..907c514c89 (1 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Michael Goderbauer <<EMAIL>>|Bump dartdocs to 0.28.3+1 ([#30570](https://github.com/flutter/flutter/issues/30570))|
|engine-flutter-autoroll <<EMAIL>>|2fe443917 Roll src/third_party/skia 558637996569..0e35ce27e0e9 (11 commits) ([flutter/engine#8454](https://github.com/flutter/engine/issues/8454)) ([#30567](https://github.com/flutter/flutter/issues/30567))   https://github.com/flutter/engine/compare/7e79b1f03db0...2fe4439178f3  git log 7e79b1f03db04a70509a207d5a0ce2de498f9aba..2fe4439178f3e09ddaa4d016688691a1adc65ed6 --no-merges --oneline 2fe443917 Roll src/third_party/skia 558637996569..0e35ce27e0e9 (11 commits) ([flutter/engine#8454](https://github.com/flutter/engine/issues/8454))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Tim Sneath <<EMAIL>>|Replace flutter.io with flutter.dev ([#30562](https://github.com/flutter/flutter/issues/30562))|
|chunhtai <<EMAIL>>|Fix issue 21640: Assertion Error : '_listenerAttached': is not true ([#30513](https://github.com/flutter/flutter/issues/30513))|
|Alexandre Ardhuin <<EMAIL>>|shorter nullable list duplications ([#30305](https://github.com/flutter/flutter/issues/30305))|
|Tong Mu <<EMAIL>>|Correctly synthesise event buttons ([#30535](https://github.com/flutter/flutter/issues/30535))  * Correctly synthesise buttons, and add tests|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 3c3b26b55c16..7e79b1f03db0 (4 commits) ([#30559](https://github.com/flutter/flutter/issues/30559))   https://github.com/flutter/engine/compare/3c3b26b55c16...7e79b1f03db0  git log 3c3b26b55c1682cbf58a712d102c66dca53f93e4..7e79b1f03db04a70509a207d5a0ce2de498f9aba --no-merges --oneline 7e79b1f03 Roll src/third_party/dart 896f1c4bb9..a2709992a2 (25 commits) 9c4c043ae Roll src/third_party/skia 604d5a3fca01..558637996569 (1 commits) ([flutter/engine#8452](https://github.com/flutter/engine/issues/8452)) 754453df3 Roll src/third_party/skia 1f58a8e457ba..604d5a3fca01 (1 commits) ([flutter/engine#8451](https://github.com/flutter/engine/issues/8451)) d6d45da07 Roll src/third_party/skia b0ababfe5ad2..1f58a8e457ba (3 commits) ([flutter/engine#8450](https://github.com/flutter/engine/issues/8450))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Justin McCandless <<EMAIL>>|Trackpad mode crash fix ([#30475](https://github.com/flutter/flutter/issues/30475))  Fixes a crash that happened when attempting to select text in trackpad mode on iOS.|
|engine-flutter-autoroll <<EMAIL>>|3c3b26b55 Roll src/third_party/skia 51874e3e3712..b0ababfe5ad2 (1 commits) ([flutter/engine#8449](https://github.com/flutter/engine/issues/8449)) ([#30540](https://github.com/flutter/flutter/issues/30540))   https://github.com/flutter/engine/compare/697e541a8d94...3c3b26b55c16  git log 697e541a8d9475b504a2bd6f335b4f004775adf3..3c3b26b55c1682cbf58a712d102c66dca53f93e4 --no-merges --oneline 3c3b26b55 Roll src/third_party/skia 51874e3e3712..b0ababfe5ad2 (1 commits) ([flutter/engine#8449](https://github.com/flutter/engine/issues/8449))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Wai Hon Law <<EMAIL>>|Update repair command for Arch Linux ([#30428](https://github.com/flutter/flutter/issues/30428))  The current repair command for Arch Linux is no longer valid because `lib32-libstdc++5` had been removed from multilib.  Actually, `lib32-gcc-libs` from core just work. Also see https://github.com/flutter/flutter/issues/25035|
|engine-flutter-autoroll <<EMAIL>>|697e541a8 Add scripts that prepares our windows VM image ([flutter/engine#8446](https://github.com/flutter/engine/issues/8446)) ([#30538](https://github.com/flutter/flutter/issues/30538))   https://github.com/flutter/engine/compare/8b74cba20bb8...697e541a8d94  git log 8b74cba20bb878d1e75747200f75dfaff5925bda..697e541a8d9475b504a2bd6f335b4f004775adf3 --no-merges --oneline 697e541a8 Add scripts that prepares our windows VM image ([flutter/engine#8446](https://github.com/flutter/engine/issues/8446))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 219bf5938752..8b74cba20bb8 (4 commits) ([#30536](https://github.com/flutter/flutter/issues/30536))   https://github.com/flutter/engine/compare/219bf5938752...8b74cba20bb8  git log 219bf5938752e0030b491565af1910c137b83dd8..8b74cba20bb878d1e75747200f75dfaff5925bda --no-merges --oneline 8b74cba20 Make sure FlutterViewController flushs all pending touches when no longer active ([flutter/engine#8400](https://github.com/flutter/engine/issues/8400)) 06cab8171 Roll src/third_party/skia e4628b10722f..51874e3e3712 (2 commits) ([flutter/engine#8445](https://github.com/flutter/engine/issues/8445)) 5460926c0 Roll src/third_party/dart 9814cb1b9c..896f1c4bb9 (4 commits) 694fc5677 Build windows engine on GCE ([flutter/engine#8442](https://github.com/flutter/engine/issues/8442))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|xster <<EMAIL>>|Let docker image install fastlane too for Linux ([#30530](https://github.com/flutter/flutter/issues/30530))|
|Hans Muller <<EMAIL>>|Correct MaterialButton disabledColor ([#30531](https://github.com/flutter/flutter/issues/30531))|
|xster <<EMAIL>>|Cupertino localization step 5: add french arb as translated example ([#29708](https://github.com/flutter/flutter/issues/29708))|
|Tong Mu <<EMAIL>>|Touching the screen adds `0x01` to buttons ([#30457](https://github.com/flutter/flutter/issues/30457))  * Add constants `kPrimaryButton`, `kTouchContact` and `kStylusContact` * PointerDownEvent and PointerMoveEvent will always set the 0x01 bit on buttons|
|Morgan <<EMAIL>>|Fix MaterialApp's _navigatorObserver when only builder used ([#29413](https://github.com/flutter/flutter/issues/29413))  Fix MaterialApp's _navigatorObserver so it doesn't cause WidgetApp's assertion to fail when using custom navigator, fixes [#18904](https://github.com/flutter/flutter/issues/18904)|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0f70d5d4f1e3..219bf5938752 (2 commits) ([#30520](https://github.com/flutter/flutter/issues/30520))   https://github.com/flutter/engine/compare/0f70d5d4f1e3...219bf5938752  git log 0f70d5d4f1e3ed377781bc6e73e3666c73afc7f8..219bf5938752e0030b491565af1910c137b83dd8 --no-merges --oneline 219bf5938 Roll src/third_party/skia 1383a38e1d92..e4628b10722f (12 commits) ([flutter/engine#8443](https://github.com/flutter/engine/issues/8443)) a40adfdf4 Add window title/icon support to GLFW shell ([flutter/engine#8435](https://github.com/flutter/engine/issues/8435))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Kate Lovett <<EMAIL>>|Embedding the diagram for BottomNavigationBar. ([#30468](https://github.com/flutter/flutter/issues/30468))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5944867cabdb..0f70d5d4f1e3 (2 commits) ([#30512](https://github.com/flutter/flutter/issues/30512))   https://github.com/flutter/engine/compare/5944867cabdb...0f70d5d4f1e3  git log 5944867cabdb934ef9fea0dccb865cb1acef51b5..0f70d5d4f1e3ed377781bc6e73e3666c73afc7f8 --no-merges --oneline 0f70d5d4f Roll src/third_party/dart 600b2831ef..9814cb1b9c (7 commits) 4b1ec56bc Roll src/third_party/skia f346df396a23..1383a38e1d92 (8 commits) ([flutter/engine#8440](https://github.com/flutter/engine/issues/8440))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Justin McCandless <<EMAIL>>|Pan and zoom gallery demo ([#25164](https://github.com/flutter/flutter/issues/25164))  Adds the "2D Transformations" demo to the gallery, which shows how to do things such as navigate around a map a la Google Maps, or show a full screen zoomable photo.  The idea is to abstract this code into a first class widget soon.|
|Tong Mu <<EMAIL>>|Fix edge cases of PointerEventConverter ([#29998](https://github.com/flutter/flutter/issues/29998))  * Fix: ui.PointerChange.remove might contain position change, but we used to expand it into a Cancel and Remove, neither of which allows position change. A Hover event is added, and a test is updated accordingly. * Fixed the issue where a PointerMoveEvent and a PointerCancelEvent do not receive the correct pressure. * Refactor the calculation of delta into deltaTo.|
|engine-flutter-autoroll <<EMAIL>>|5944867ca update to use SkTileMode ([flutter/engine#8439](https://github.com/flutter/engine/issues/8439)) ([#30506](https://github.com/flutter/flutter/issues/30506))   https://github.com/flutter/engine/compare/ad9d2417e9aa...5944867cabdb  git log ad9d2417e9aa182700f0d1423de758d2a000598d..5944867cabdb934ef9fea0dccb865cb1acef51b5 --no-merges --oneline 5944867ca update to use SkTileMode ([flutter/engine#8439](https://github.com/flutter/engine/issues/8439))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 309d078b597d..ad9d2417e9aa (15 commits) ([#30502](https://github.com/flutter/flutter/issues/30502))   https://github.com/flutter/engine/compare/309d078b597d...ad9d2417e9aa  git log 309d078b597d3a38fe9bcef8d8bae7b56487e8eb..ad9d2417e9aa182700f0d1423de758d2a000598d --no-merges --oneline ad9d2417e Roll src/third_party/skia dde12a25bb5e..f346df396a23 (1 commits) ([flutter/engine#8438](https://github.com/flutter/engine/issues/8438)) 0ed237c19 Roll src/third_party/skia b6d04556c34d..dde12a25bb5e (4 commits) ([flutter/engine#8437](https://github.com/flutter/engine/issues/8437)) 445ab42ef Roll src/third_party/dart 11a3dd7332..600b2831ef (9 commits) 49d0dc6d6 Roll src/third_party/skia 5c5de2146309..b6d04556c34d (1 commits) ([flutter/engine#8434](https://github.com/flutter/engine/issues/8434)) 4ff18fbda Roll src/third_party/dart f6768b6fb3..11a3dd7332 (56 commits) 7b831986f Roll src/third_party/skia 2273c900ea28..5c5de2146309 (5 commits) ([flutter/engine#8432](https://github.com/flutter/engine/issues/8432)) 800ea0a40 Revert &[#34](https://github.com/flutter/flutter/issues/34);Enable shutting down all root isolates in a VM. ([#8402](https://github.com/flutter/flutter/issues/8402))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8431](https://github.com/flutter/engine/issues/8431)) b59c4436c Enable shutting down all root isolates in a VM. ([flutter/engine#8402](https://github.com/flutter/engine/issues/8402)) 9acdf1837 Make AccessibilityViewEmbedder final ([flutter/engine#8429](https://github.com/flutter/engine/issues/8429)) 804d54ddf Eliminate unused displayBounds parameter ([flutter/engine#8427](https://github.com/flutter/engine/issues/8427)) d4c815f57 Roll buildroot ([flutter/engine#8425](https://github.com/flutter/engine/issues/8425)) 20190d707 Roll src/third_party/skia fbe240669394..2273c900ea28 (13 commits) ([flutter/engine#8428](https://github.com/flutter/engine/issues/8428)) 7e38b0aa2 Revert &[#34](https://github.com/flutter/flutter/issues/34);Revert &[#34](https://github.com/flutter/flutter/issues/34);Separate the data required to bootstrap the VM into its own class. ([#8397](https://github.com/flutter/flutter/issues/8397))&[#34](https://github.com/flutter/flutter/issues/34); ([#8406](https://github.com/flutter/flutter/issues/8406))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8414](https://github.com/flutter/engine/issues/8414)) d8bb9d793 Roll src/third_party/skia 176da0bb8a10..fbe240669394 (9 commits) ([flutter/engine#8424](https://github.com/flutter/engine/issues/8424)) 64fc58329 Remove use of DART_CHECK_VALID. ([flutter/engine#8417](https://github.com/flutter/engine/issues/8417))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Add ExpansionPanelList and ExpansionPanelList.radio Examples ([#30343](https://github.com/flutter/flutter/issues/30343))|
|Michelle Dudley <<EMAIL>>|Fix Shrine overscroll glow indicator ([#27217](https://github.com/flutter/flutter/issues/27217))|
|Nikita <<EMAIL>>|Implement compute for async function ([#16265](https://github.com/flutter/flutter/issues/16265)) ([#30275](https://github.com/flutter/flutter/issues/30275))|
|Kate Lovett <<EMAIL>>|Embedding new raised button diagram. ([#30398](https://github.com/flutter/flutter/issues/30398))|
|Kate Lovett <<EMAIL>>|Updating sample code for BottomNavigationBar class for diagram. ([#30453](https://github.com/flutter/flutter/issues/30453))|
|Michael Goderbauer <<EMAIL>>|Random trivial fixes in the animation packages ([#30276](https://github.com/flutter/flutter/issues/30276))|
|Michael Goderbauer <<EMAIL>>|Be more explicit when ValueNotifier notifies ([#30461](https://github.com/flutter/flutter/issues/30461))|
|engine-flutter-autoroll <<EMAIL>>|309d078b5 Roll src/third_party/skia e25b4472cdd9..176da0bb8a10 (10 commits) ([flutter/engine#8422](https://github.com/flutter/engine/issues/8422)) ([#30441](https://github.com/flutter/flutter/issues/30441))   https://github.com/flutter/engine/compare/cc115500b9cb...309d078b597d  git log cc115500b9cb2b27d59627f202e6cd6bb87f49fc..309d078b597d3a38fe9bcef8d8bae7b56487e8eb --no-merges --oneline 309d078b5 Roll src/third_party/skia e25b4472cdd9..176da0bb8a10 (10 commits) ([flutter/engine#8422](https://github.com/flutter/engine/issues/8422))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Zachary Anderson <<EMAIL>>|[fuchsia] Fix isolate filter ([#30458](https://github.com/flutter/flutter/issues/30458))|
|Hans Muller <<EMAIL>>|Fix gallery API doc URL launcher ([#30460](https://github.com/flutter/flutter/issues/30460))|
|liyuqian <<EMAIL>>|Revert "Error message for setting shaderWarmUp too late ([#30145](https://github.com/flutter/flutter/issues/30145))" ([#30463](https://github.com/flutter/flutter/issues/30463))  This reverts commit ffbb335e35ae7416be98b72779baae6c9bd25b3b.  Reason for revert: flutter driver tests may have bugs in getting first frame. Revert this until we figure out what happened.|
|Michael Goderbauer <<EMAIL>>|Moar Videos ([#30452](https://github.com/flutter/flutter/issues/30452))|
|Michael Goderbauer <<EMAIL>>|Add 29 Widget of the Week videos ([#30415](https://github.com/flutter/flutter/issues/30415))|
|Christopher Fujino <<EMAIL>>|make shellcheck (linter) recommended changes to bin/flutter ([#30456](https://github.com/flutter/flutter/issues/30456))|
|Michael Goderbauer <<EMAIL>>|Bump dartdocs to 0.28.3 ([#30451](https://github.com/flutter/flutter/issues/30451))|
|Vyacheslav Egorov <<EMAIL>>|Introduce --report-timings flag for flutter build aot command. ([#30032](https://github.com/flutter/flutter/issues/30032))  This flag makes flutter build aot report timings for substeps (e.g. frontend compilation and gen_snapshot) in a machine readable form.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 4a8c7dceeb4b..cc115500b9cb (6 commits) ([#30421](https://github.com/flutter/flutter/issues/30421))   https://github.com/flutter/engine/compare/4a8c7dceeb4b...cc115500b9cb  git log 4a8c7dceeb4b7c710e0b22090cc3bc26f458a3a9..cc115500b9cb2b27d59627f202e6cd6bb87f49fc --no-merges --oneline cc115500b Roll src/third_party/skia c9eeab3d85b9..e25b4472cdd9 (1 commits) ([flutter/engine#8420](https://github.com/flutter/engine/issues/8420)) 20e45996d Roll src/third_party/skia 71c0e9422038..c9eeab3d85b9 (4 commits) ([flutter/engine#8418](https://github.com/flutter/engine/issues/8418)) a650790e4 Add scroll wheel support to desktop GLFW shell ([flutter/engine#8416](https://github.com/flutter/engine/issues/8416)) 77d8e12e9 Roll src/third_party/dart 14fd3b7d6a..f6768b6fb3 (5 commits) 23fceaacd Added new Android embedding packages to javadoc generation. ([flutter/engine#8411](https://github.com/flutter/engine/issues/8411)) 2f1786010 Pass environment defines to compile flutter platform step. ([flutter/engine#8412](https://github.com/flutter/engine/issues/8412))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|xster <<EMAIL>>|Cupertino localization step 4: let generated date localization combine material and cupertino locales ([#29650](https://github.com/flutter/flutter/issues/29650))|
|Dan Field <<EMAIL>>|Draggable Scrollable sheet ([#30058](https://github.com/flutter/flutter/issues/30058))  * Draggable Scrollable sheet|
|engine-flutter-autoroll <<EMAIL>>|Roll engine f7b4903d7c99..4a8c7dceeb4b (5 commits) ([#30399](https://github.com/flutter/flutter/issues/30399))   https://github.com/flutter/engine/compare/f7b4903d7c99...4a8c7dceeb4b  git log f7b4903d7c9913447af3aaebc4308d1a1e2a4dde..4a8c7dceeb4b7c710e0b22090cc3bc26f458a3a9 --no-merges --oneline 4a8c7dcee Roll src/third_party/skia f2d9b865d736..71c0e9422038 (13 commits) ([flutter/engine#8413](https://github.com/flutter/engine/issues/8413)) efbecd7e8 [txt] Add back FontCollection::SetDefaultFontManager ([flutter/engine#8410](https://github.com/flutter/engine/issues/8410)) 5f7711449 Roll src/third_party/dart 75b2f9f919..14fd3b7d6a (23 commits) f27c5730a [fuchsia] Exclude glfw from the Fuchsia host build ([flutter/engine#8407](https://github.com/flutter/engine/issues/8407)) 853ac1bdf Roll src/third_party/skia 70d8f4f3acbb..f2d9b865d736 (11 commits) ([flutter/engine#8408](https://github.com/flutter/engine/issues/8408))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Amir Hardon <<EMAIL>>|Make sure _handleAppFrame is only registered once per frame ([#30346](https://github.com/flutter/flutter/issues/30346))  There were 2 possible scenarios in which _handleAppFrame is added more than once as a frame callback. When this happens it is possible that the second invocation will try to access _nextFrame.image when _nextFrame is null and crash. The 2 scenarios are:  Scenario 1  A GIF frame is decoded and a Flutter frame is executed before it's time to show the next GIF frame. The timer that's waiting for enough time to elapse is invoked, and schedules a callback for the next Flutter frame(here). Before the next Flutter frame is executed, MultiFrameImageStreamCompleter#removeListener is called followed by ``MultiFrameImageStreamCompleter#addListenerthat is invoking_decodeNextFrameAndSchedule` which is adding `_handleAppFrame` again as a next frame callback. Scenario 2 removeListener and addListener are called multiple times in succession, every call to addListener can result in another registration of _handleAppFrame to the next Flutter frame callbacks list.  This patch fixes the issue by guarding against a second registration of _handleAppFrame.|
|xster <<EMAIL>>|Add needed singular resources for cupertino_en.arb ([#30160](https://github.com/flutter/flutter/issues/30160))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 6675f71dfb22..f7b4903d7c99 (2 commits) ([#30393](https://github.com/flutter/flutter/issues/30393))   https://github.com/flutter/engine/compare/6675f71dfb22...f7b4903d7c99  git log 6675f71dfb22a0b4fdab6332e37843f78c33c9e6..f7b4903d7c9913447af3aaebc4308d1a1e2a4dde --no-merges --oneline f7b4903d7 Revert &[#34](https://github.com/flutter/flutter/issues/34);Separate the data required to bootstrap the VM into its own class. ([#8397](https://github.com/flutter/flutter/issues/8397))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8406](https://github.com/flutter/engine/issues/8406)) f835789dd Roll src/third_party/skia d962070feb84..70d8f4f3acbb (1 commits) ([flutter/engine#8405](https://github.com/flutter/engine/issues/8405))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jon Tippens <<EMAIL>>|Fix minor typo ([#30353](https://github.com/flutter/flutter/issues/30353))|
|Kate Lovett <<EMAIL>>|Updating sample code for raised button to match forthcoming diagram. ([#30348](https://github.com/flutter/flutter/issues/30348))|
|engine-flutter-autoroll <<EMAIL>>|6675f71df Roll src/third_party/skia 1acf79a3e5b8..d962070feb84 (4 commits) ([flutter/engine#8404](https://github.com/flutter/engine/issues/8404)) ([#30365](https://github.com/flutter/flutter/issues/30365))   https://github.com/flutter/engine/compare/b572ade3550b...6675f71dfb22  git log b572ade3550b3f04ccb8c18d5bc4d5804f479c03..6675f71dfb22a0b4fdab6332e37843f78c33c9e6 --no-merges --oneline 6675f71df Roll src/third_party/skia 1acf79a3e5b8..d962070feb84 (4 commits) ([flutter/engine#8404](https://github.com/flutter/engine/issues/8404))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|b572ade35 Roll src/third_party/skia 3f6a2ac63670..1acf79a3e5b8 (1 commits) ([flutter/engine#8403](https://github.com/flutter/engine/issues/8403)) ([#30355](https://github.com/flutter/flutter/issues/30355))   https://github.com/flutter/engine/compare/3796d989183d...b572ade3550b  git log 3796d989183d588b17a110d8aaf74906c4e1c8f0..b572ade3550b3f04ccb8c18d5bc4d5804f479c03 --no-merges --oneline b572ade35 Roll src/third_party/skia 3f6a2ac63670..1acf79a3e5b8 (1 commits) ([flutter/engine#8403](https://github.com/flutter/engine/issues/8403))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Alexandre Ardhuin <<EMAIL>>|no need .toList() before .join() ([#30304](https://github.com/flutter/flutter/issues/30304))|
|engine-flutter-autoroll <<EMAIL>>|3796d9891 [ui] Add null check in FontWeight.lerp ([flutter/engine#8274](https://github.com/flutter/engine/issues/8274)) ([#30352](https://github.com/flutter/flutter/issues/30352))   https://github.com/flutter/engine/compare/79a988f5da46...3796d989183d  git log 79a988f5da4665980c6cc6916403e76b53f1e3e0..3796d989183d588b17a110d8aaf74906c4e1c8f0 --no-merges --oneline 3796d9891 [ui] Add null check in FontWeight.lerp ([flutter/engine#8274](https://github.com/flutter/engine/issues/8274))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine c991647404f8..79a988f5da46 (2 commits) ([#30350](https://github.com/flutter/flutter/issues/30350))   https://github.com/flutter/engine/compare/c991647404f8...79a988f5da46  git log c991647404f8117dab2ca4ed49aa3ba48a634240..79a988f5da4665980c6cc6916403e76b53f1e3e0 --no-merges --oneline 79a988f5d Roll src/third_party/skia b27667a15a15..3f6a2ac63670 (1 commits) ([flutter/engine#8401](https://github.com/flutter/engine/issues/8401)) cfe8675ff Delay platform view removal to submitFrame. ([flutter/engine#8380](https://github.com/flutter/engine/issues/8380))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 6c1b0b83c2b3..c991647404f8 (2 commits) ([#30345](https://github.com/flutter/flutter/issues/30345))   https://github.com/flutter/engine/compare/6c1b0b83c2b3...c991647404f8  git log 6c1b0b83c2b3259cf7b23c2f6d6fc5cfdb924b61..c991647404f8117dab2ca4ed49aa3ba48a634240 --no-merges --oneline c99164740 Separate the data required to bootstrap the VM into its own class. ([flutter/engine#8397](https://github.com/flutter/engine/issues/8397)) 8c9aca440 Roll src/third_party/skia de206c75c2c8..b27667a15a15 (14 commits) ([flutter/engine#8399](https://github.com/flutter/engine/issues/8399))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Michael Goderbauer <<EMAIL>>|Make heroes fly on pushReplacement ([#30228](https://github.com/flutter/flutter/issues/30228))|
|Shi-Hao Hong <<EMAIL>>|Added assert to prevent complete ListTile trailing/leading horizontal expansion ([#30212](https://github.com/flutter/flutter/issues/30212))|
|LongCatIsLooong <<EMAIL>>|update sample code analyzer regexp & test case ([#30201](https://github.com/flutter/flutter/issues/30201))  updates the regex the analyzer uses, so it should be able to recognize expressions such as const Foo a = Foo(); as "other code" rather than a constructor call.|
|engine-flutter-autoroll <<EMAIL>>|6c1b0b83c Roll src/third_party/dart 1e82544b07..75b2f9f919 (26 commits) ([#30337](https://github.com/flutter/flutter/issues/30337))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine a850016454f6..40eb74c50b06 (2 commits) ([#30335](https://github.com/flutter/flutter/issues/30335))   https://github.com/flutter/engine/compare/a850016454f6...40eb74c50b06  git log a850016454f6d33da64d03dbd1ea71fd0fac299f..40eb74c50b0630d0a5fbf8586febe6581b893b66 --no-merges --oneline 40eb74c50 Make the resource context primary on iOS ([flutter/engine#8387](https://github.com/flutter/engine/issues/8387)) d8d9855df Roll src/third_party/skia 73cc4e8d5fcf..de206c75c2c8 (2 commits) ([flutter/engine#8396](https://github.com/flutter/engine/issues/8396))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Tong Mu <<EMAIL>>|Simplify logic of TapGestureRecognizer ([#30227](https://github.com/flutter/flutter/issues/30227))  Refactors the logic of TapGestureRecognizer, making the calling dependency unidirectional between resolve(accept) and checkUp.|
|Hans Muller <<EMAIL>>|Fix OutlineInputBorder crash ([#30123](https://github.com/flutter/flutter/issues/30123))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine f3ec767458f1..a850016454f6 (4 commits) ([#30312](https://github.com/flutter/flutter/issues/30312))   https://github.com/flutter/engine/compare/f3ec767458f1...a850016454f6  git log f3ec767458f12bb3099248fdc57d6c0d1051f042..a850016454f6d33da64d03dbd1ea71fd0fac299f --no-merges --oneline a85001645 Roll src/third_party/skia bb482ab871a2..73cc4e8d5fcf (1 commits) ([flutter/engine#8395](https://github.com/flutter/engine/issues/8395)) aba61e8fb Roll src/third_party/skia 5ee611b26f23..bb482ab871a2 (4 commits) ([flutter/engine#8394](https://github.com/flutter/engine/issues/8394)) 82071e916 Don&[#39](https://github.com/flutter/flutter/issues/39);t access a11y APIs with reflection starting Android P. ([flutter/engine#8393](https://github.com/flutter/engine/issues/8393)) d9f16111a Roll src/third_party/skia acb4829c1be4..5ee611b26f23 (1 commits) ([flutter/engine#8392](https://github.com/flutter/engine/issues/8392))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Zachary Anderson <<EMAIL>>|[fuchsia_tester] Plumb through the location of icudtl ([#30218](https://github.com/flutter/flutter/issues/30218))|
|engine-flutter-autoroll <<EMAIL>>|f3ec76745 Roll src/third_party/skia 83edfbe736fd..acb4829c1be4 (1 commits) ([flutter/engine#8391](https://github.com/flutter/engine/issues/8391)) ([#30266](https://github.com/flutter/flutter/issues/30266))   https://github.com/flutter/engine/compare/be6313d87545...f3ec767458f1  git log be6313d87545874a6108edb2a311f30d9a027472..f3ec767458f12bb3099248fdc57d6c0d1051f042 --no-merges --oneline f3ec76745 Roll src/third_party/skia 83edfbe736fd..acb4829c1be4 (1 commits) ([flutter/engine#8391](https://github.com/flutter/engine/issues/8391))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|be6313d87 Roll src/third_party/skia 2fdbeae32785..83edfbe736fd (1 commits) ([flutter/engine#8390](https://github.com/flutter/engine/issues/8390)) ([#30263](https://github.com/flutter/flutter/issues/30263))   https://github.com/flutter/engine/compare/ae024d755518...be6313d87545  git log ae024d755518019f3618931fbc47d3f27e84fad5..be6313d87545874a6108edb2a311f30d9a027472 --no-merges --oneline be6313d87 Roll src/third_party/skia 2fdbeae32785..83edfbe736fd (1 commits) ([flutter/engine#8390](https://github.com/flutter/engine/issues/8390))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|ae024d755 Roll src/third_party/skia a6dd1ebd42cf..2fdbeae32785 (1 commits) ([flutter/engine#8389](https://github.com/flutter/engine/issues/8389)) ([#30257](https://github.com/flutter/flutter/issues/30257))   https://github.com/flutter/engine/compare/28f8453629f9...ae024d755518  git log 28f8453629f951d41437aa16bb09957643f1e850..ae024d755518019f3618931fbc47d3f27e84fad5 --no-merges --oneline ae024d755 Roll src/third_party/skia a6dd1ebd42cf..2fdbeae32785 (1 commits) ([flutter/engine#8389](https://github.com/flutter/engine/issues/8389))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 8dabd60a30d0..28f8453629f9 (3 commits) ([#30255](https://github.com/flutter/flutter/issues/30255))   https://github.com/flutter/engine/compare/8dabd60a30d0...28f8453629f9  git log 8dabd60a30d048bf90b4bc0b29914ea00bda182c..28f8453629f951d41437aa16bb09957643f1e850 --no-merges --oneline 28f845362 Roll src/third_party/skia c9822a1818ba..a6dd1ebd42cf (1 commits) ([flutter/engine#8388](https://github.com/flutter/engine/issues/8388)) 2e1f491ba Roll src/third_party/skia 265fd3de3527..c9822a1818ba (1 commits) ([flutter/engine#8386](https://github.com/flutter/engine/issues/8386)) b466aba58 Roll src/third_party/skia 59b733715141..265fd3de3527 (1 commits) ([flutter/engine#8384](https://github.com/flutter/engine/issues/8384))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Added helpful Material assert message  ([#30219](https://github.com/flutter/flutter/issues/30219))  Clarified an assert for when a Material widget's backgroundColor is null when its type isn't MaterialType.transparency|
|engine-flutter-autoroll <<EMAIL>>|Roll engine b75472c106d0..8dabd60a30d0 (2 commits) ([#30236](https://github.com/flutter/flutter/issues/30236))   https://github.com/flutter/engine/compare/b75472c106d0...8dabd60a30d0  git log b75472c106d0cd0aab45de6bc16e0d6aa6d418cf..8dabd60a30d048bf90b4bc0b29914ea00bda182c --no-merges --oneline 8dabd60a3 Remove unused DartVM::IsKernelMapping ([flutter/engine#8381](https://github.com/flutter/engine/issues/8381)) 63b9d257c Roll src/third_party/dart f29e100f42..1e82544b07 (41 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine ca7623eb39d7..b75472c106d0 (5 commits) ([#30231](https://github.com/flutter/flutter/issues/30231))   https://github.com/flutter/engine/compare/ca7623eb39d7...b75472c106d0  git log ca7623eb39d74a8cbdd095fcc7db398267b6928f..b75472c106d0cd0aab45de6bc16e0d6aa6d418cf --no-merges --oneline b75472c10 Add missing import to functional for Windows. ([flutter/engine#8382](https://github.com/flutter/engine/issues/8382)) 9d7167516 Ensure OpacityLayer to have a single child ([flutter/engine#8362](https://github.com/flutter/engine/issues/8362)) 131cc625a Allow native entrypoint registration for runtime unittests. ([flutter/engine#8379](https://github.com/flutter/engine/issues/8379)) 5e124ffc1 Fix typos ([flutter/engine#8339](https://github.com/flutter/engine/issues/8339)) 972afdc92 Allow running runtime_unittests in AOT mode. ([flutter/engine#8375](https://github.com/flutter/engine/issues/8375))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Revert "Ensure that flutter run/drive/test/update_packages only downloads required artifacts ([#30075](https://github.com/flutter/flutter/issues/30075))" ([#30232](https://github.com/flutter/flutter/issues/30232))|
|Jonah Williams <<EMAIL>>|Ensure that flutter run/drive/test/update_packages only downloads required artifacts ([#30075](https://github.com/flutter/flutter/issues/30075))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 86f4b741b863..ca7623eb39d7 (5 commits) ([#30229](https://github.com/flutter/flutter/issues/30229))   https://github.com/flutter/engine/compare/86f4b741b863...ca7623eb39d7  git log 86f4b741b863aef5586f03f4d250f1d789fde2be..ca7623eb39d74a8cbdd095fcc7db398267b6928f --no-merges --oneline ca7623eb3 Handle null values in TextInputConfiguration.actionLabel JSON ([flutter/engine#8377](https://github.com/flutter/engine/issues/8377)) d0239bd83 Roll src/third_party/skia 73c6c9d26870..59b733715141 (3 commits) ([flutter/engine#8378](https://github.com/flutter/engine/issues/8378)) 71d59cde9 Check for hover motion events in AndroidTouchProcessor ([flutter/engine#8376](https://github.com/flutter/engine/issues/8376)) fdcbb40fe [flutter_tester] Accept --icu-data-file-path ([flutter/engine#8374](https://github.com/flutter/engine/issues/8374)) e633b26c5 Introduce unit tests and refactor web dart:ui into &[#34](https://github.com/flutter/flutter/issues/34);package&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8346](https://github.com/flutter/engine/issues/8346))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|add coverage benchmark ([#30222](https://github.com/flutter/flutter/issues/30222))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine a1a2129bc469..86f4b741b863 (5 commits) ([#30223](https://github.com/flutter/flutter/issues/30223))   https://github.com/flutter/engine/compare/a1a2129bc469...86f4b741b863  git log a1a2129bc4696e66510be984fd7908eae51eb98f..86f4b741b863aef5586f03f4d250f1d789fde2be --no-merges --oneline 86f4b741b Move libdart selection into its own target in //flutter/runtime. ([flutter/engine#8373](https://github.com/flutter/engine/issues/8373)) f39cf3774 Add a GN format presubmit. ([flutter/engine#8371](https://github.com/flutter/engine/issues/8371)) d6e2d8527 Roll src/third_party/skia 7eeb74fdfd84..73c6c9d26870 (8 commits) ([flutter/engine#8372](https://github.com/flutter/engine/issues/8372)) 8412c199a Enable Linux shell build ([flutter/engine#8233](https://github.com/flutter/engine/issues/8233)) 133901199 libtxt: track the start and end x positions of glyph blobs for more accurate rendering of text decorations ([flutter/engine#8368](https://github.com/flutter/engine/issues/8368))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|make sure flutter test asks for cache upgrades ([#30216](https://github.com/flutter/flutter/issues/30216))|
|xster <<EMAIL>>|Cupertino localization step 3: in-place move some material tools around to make room for cupertino ([#29644](https://github.com/flutter/flutter/issues/29644))|
|xster <<EMAIL>>|Make sure everything in the Cupertino page transition can be linear when back swiping ([#28629](https://github.com/flutter/flutter/issues/28629))|
|dacaiguoguo <<EMAIL>>|Set custom flutter_assets by add FLTAssetsPath to AppFrameworkInfo.plist ([#29499](https://github.com/flutter/flutter/issues/29499))  * Get FLTAssetsPath from AppFrameworkInfo.plist|
|Jonah Williams <<EMAIL>>|add missing test case and handle wildcard removal ([#30205](https://github.com/flutter/flutter/issues/30205))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 82765aa77db9..a1a2129bc469 (13 commits) ([#30209](https://github.com/flutter/flutter/issues/30209))   https://github.com/flutter/engine/compare/82765aa77db9...a1a2129bc469  git log 82765aa77db9621dfbc50801ee2709aa0a00e04d..a1a2129bc4696e66510be984fd7908eae51eb98f --no-merges --oneline a1a2129bc GN Format all files in the engine. ([flutter/engine#8369](https://github.com/flutter/engine/issues/8369)) 82c910414 Remove old Fuchsia external mem,sem extensions 6d1a6a4f4 Allow per-platform customization of the default Skia font manager ([flutter/engine#8358](https://github.com/flutter/engine/issues/8358)) 3de50b8d2 Roll src/third_party/skia 84a53268af92..7eeb74fdfd84 (3 commits) ([flutter/engine#8367](https://github.com/flutter/engine/issues/8367)) 65837ab32 Roll src/third_party/skia a2cec203eea8..84a53268af92 (1 commits) ([flutter/engine#8366](https://github.com/flutter/engine/issues/8366)) ada0a8e2b Roll src/third_party/skia 57b7a792bb6f..a2cec203eea8 (1 commits) ([flutter/engine#8365](https://github.com/flutter/engine/issues/8365)) 9729f1ae3 Roll src/third_party/skia bf4229cc10a1..57b7a792bb6f (5 commits) ([flutter/engine#8364](https://github.com/flutter/engine/issues/8364)) 23e0c3758 Roll src/third_party/skia 01fe26360660..bf4229cc10a1 (6 commits) ([flutter/engine#8363](https://github.com/flutter/engine/issues/8363)) fd0b44045 Roll src/third_party/skia cc8c7bee7925..01fe26360660 (10 commits) ([flutter/engine#8361](https://github.com/flutter/engine/issues/8361)) 96c975163 Rename threshold to access_threshold ([flutter/engine#8354](https://github.com/flutter/engine/issues/8354)) 09a67fe60 update buildroot dep for https://github.com/flutter/buildroot/commit/e2ca4571fa39be20ab1bf67c65d4700612756af2 ([flutter/engine#8359](https://github.com/flutter/engine/issues/8359)) 602b61213 Reduce z-fighting on Scenic ([flutter/engine#8318](https://github.com/flutter/engine/issues/8318)) b53e436f9 Roll src/third_party/dart a977273262..f29e100f42 (8 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Darren Austin <<EMAIL>>|Made the showMenu() position parameter required  ([#30206](https://github.com/flutter/flutter/issues/30206))  Made the showMenu() position parameter required as it doesn't make sense to show a menu without indicating where it should be shown. Also added a test to verify this.|
|Michael Goderbauer <<EMAIL>>|Part 1: Improve Overlay API ([#28747](https://github.com/flutter/flutter/issues/28747))|
|Jonah Williams <<EMAIL>>|disable jit snapshot until we know what is going on here ([#30204](https://github.com/flutter/flutter/issues/30204))|
|Dan Field <<EMAIL>>|roll engine to 82765aa77db9621dfbc50801ee2709aa0a00e04d ([#30198](https://github.com/flutter/flutter/issues/30198))|
|Shi-Hao Hong <<EMAIL>>|Set Max Height for ListTile trailing and leading widgets ([#29771](https://github.com/flutter/flutter/issues/29771))|
|liyuqian <<EMAIL>>|Error message for setting shaderWarmUp too late ([#30145](https://github.com/flutter/flutter/issues/30145))  Developers may get confused by setting PaintingBinding.shaderWarmUp in the wrong place. The added assert and error message help avoid that.|
|Jonah Williams <<EMAIL>>|Intercept errors thrown by synchronous Completers in image resolution. ([#30139](https://github.com/flutter/flutter/issues/30139))|
|Shi-Hao Hong <<EMAIL>>|Add link to hero animation page ([#30059](https://github.com/flutter/flutter/issues/30059))|
|Darren Austin <<EMAIL>>|Fix a misuse of matchesGoldenFile() in the physical_model_test. ([#30141](https://github.com/flutter/flutter/issues/30141))  Fixed a misuse of matchesGoldenFile() in the physical_model_test so that it correctly reports a failed test on Linux (and skips the other platforms).|
|Ian Hickson <<EMAIL>>|Update PULL_REQUEST_TEMPLATE.md ([#29621](https://github.com/flutter/flutter/issues/29621))  This tries to make it more obvious when someone checks the "tests" box without actually having added tests.|
|Jonah Williams <<EMAIL>>|skip .dart_tool folders when running update-packages ([#30082](https://github.com/flutter/flutter/issues/30082))|
|Jonah Williams <<EMAIL>>|Add more test coverage to image handling ([#30078](https://github.com/flutter/flutter/issues/30078))|
|Christopher Fujino <<EMAIL>>|Move spinner `_defaultSlowWarning` message to a new line ([#30071](https://github.com/flutter/flutter/issues/30071))  * write newline before adding slow restart message to spinner * update existing test|
|Francisco Magdaleno <<EMAIL>>|Adds the keyboard mapping for Linux ([#29993](https://github.com/flutter/flutter/issues/29993))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 37947f961a62..c4d14a00716f (11 commits) ([#30094](https://github.com/flutter/flutter/issues/30094))   https://github.com/flutter/engine/compare/37947f961a62...c4d14a00716f  git log 37947f961a6242d2efc05a55a4319b120764a403..c4d14a00716fa616b69d51bab957aee483bdc9c2 --no-merges --oneline c4d14a007 Roll src/third_party/skia e4c67058ddb1..01a065884b7d (1 commits) ([flutter/engine#8344](https://github.com/flutter/engine/issues/8344)) 5983b7adf Roll src/third_party/dart ffee99d79b..cf32584870 (6 commits) 84c62b4c7 Build Windows shell ([flutter/engine#8331](https://github.com/flutter/engine/issues/8331)) 6415277d0 Roll src/third_party/skia 8d2c19554e4a..e4c67058ddb1 (1 commits) ([flutter/engine#8341](https://github.com/flutter/engine/issues/8341)) a0110107e Roll src/third_party/dart 991c9da720..ffee99d79b (7 commits) 2098398e1 Cleanups to run_tests.sh script ([flutter/engine#8337](https://github.com/flutter/engine/issues/8337)) 5c99138fa Build GLFW from source for Linux shell ([flutter/engine#8327](https://github.com/flutter/engine/issues/8327)) 68002458f Fix Windows build. ([flutter/engine#8336](https://github.com/flutter/engine/issues/8336)) 6d8b83677 Remove use of epoxy from Linux shell ([flutter/engine#8334](https://github.com/flutter/engine/issues/8334)) 4efc32163 Add super call in FLEView reshape ([flutter/engine#8335](https://github.com/flutter/engine/issues/8335)) cb8eb801a Allow embedders to specify their own task runner interfaces. ([flutter/engine#8273](https://github.com/flutter/engine/issues/8273))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Move binarySearch implementation in animated_list to foundation/collection.dart. ([#29860](https://github.com/flutter/flutter/issues/29860))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 76200562779e..37947f961a62 (2 commits) ([#30074](https://github.com/flutter/flutter/issues/30074))   https://github.com/flutter/engine/compare/76200562779e...37947f961a62  git log 76200562779e49d34e62056b66800ac870197092..37947f961a6242d2efc05a55a4319b120764a403 --no-merges --oneline 37947f961 Allow delegation of a11y events from nodes that were not yet traversed ([flutter/engine#8333](https://github.com/flutter/engine/issues/8333)) deeb7a747 Roll src/third_party/skia 99ccc0ca87e6..8d2c19554e4a (11 commits) ([flutter/engine#8332](https://github.com/flutter/engine/issues/8332))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Added Hero zero-frame animation documentation ([#30048](https://github.com/flutter/flutter/issues/30048))|
|Nizarius <<EMAIL>>|CupertinoTextField: added ability to change placeholder color ([#28001](https://github.com/flutter/flutter/issues/28001))|
|Jonah Williams <<EMAIL>>|Update to latest matcher ([#30019](https://github.com/flutter/flutter/issues/30019))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 3a3f707d5a9b..76200562779e (2 commits) ([#30070](https://github.com/flutter/flutter/issues/30070))   https://github.com/flutter/engine/compare/3a3f707d5a9b...76200562779e  git log 3a3f707d5a9b17388b60ff10b9f65b0c6a3c4134..76200562779e49d34e62056b66800ac870197092 --no-merges --oneline 762005627 Android Embedding PR22: Polish - FlutterActivity Intent factories, FlutterFragment control of render modes, FlutterSurfaceView transparent until rendering is ready. ([flutter/engine#8317](https://github.com/flutter/engine/issues/8317)) 6bd697dd4 Fix &[#34](https://github.com/flutter/flutter/issues/34);PointerEvent&[#34](https://github.com/flutter/flutter/issues/34); flow end event ([flutter/engine#8319](https://github.com/flutter/engine/issues/8319))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Make timeout durations configurable ([#30053](https://github.com/flutter/flutter/issues/30053))|
|xster <<EMAIL>>|Cupertino localization step 1.5: fix a resource mismatch in cupertino_en.arb ([#29821](https://github.com/flutter/flutter/issues/29821))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0d83a2ecd1bb..3a3f707d5a9b (25 commits) ([#30063](https://github.com/flutter/flutter/issues/30063))   https://github.com/flutter/engine/compare/0d83a2ecd1bb...3a3f707d5a9b  git log 0d83a2ecd1bb78f8e1f0ed9719ff3751cdfdd064..3a3f707d5a9b17388b60ff10b9f65b0c6a3c4134 --no-merges --oneline 3a3f707d5 Reland &[#34](https://github.com/flutter/flutter/issues/34);Allow specification of std::functions as native entrypoints from Dart code.&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8329](https://github.com/flutter/engine/issues/8329)) d4275d9ee Roll src/third_party/skia 576eb87a2d2d..99ccc0ca87e6 (5 commits) ([flutter/engine#8328](https://github.com/flutter/engine/issues/8328)) 3a415c4f1 Map glfw into third_party, and roll buildroot ([flutter/engine#8308](https://github.com/flutter/engine/issues/8308)) ce9ea5869 [fuchsia] Disable FML_TRACE_COUNTER events to unblock roll ([flutter/engine#8325](https://github.com/flutter/engine/issues/8325)) 48496588c Roll src/third_party/dart 8a92d2a8d9..991c9da720 (2 commits) 20c241ac3 Roll src/third_party/skia c476e5da4fef..576eb87a2d2d (7 commits) ([flutter/engine#8324](https://github.com/flutter/engine/issues/8324)) cadcf1c58 Roll src/third_party/dart f3fd1943fc..8a92d2a8d9 (16 commits) 66141eb8e Roll src/third_party/skia d1c271bd39f0..c476e5da4fef (2 commits) ([flutter/engine#8322](https://github.com/flutter/engine/issues/8322)) e5b31cdd9 Roll src/third_party/skia cec20280b3fb..d1c271bd39f0 (3 commits) ([flutter/engine#8321](https://github.com/flutter/engine/issues/8321)) 1daff5ce1 Roll src/third_party/skia bf341ae88c83..cec20280b3fb (3 commits) ([flutter/engine#8320](https://github.com/flutter/engine/issues/8320)) 87db585ad Roll src/third_party/skia 45d5f702133e..bf341ae88c83 (2 commits) ([flutter/engine#8316](https://github.com/flutter/engine/issues/8316)) fd7d7fa5a Add a11y support for embedded iOS platform view ([flutter/engine#8156](https://github.com/flutter/engine/issues/8156)) f64ee01e1 Roll src/third_party/skia d2ca31218bc4..45d5f702133e (11 commits) ([flutter/engine#8315](https://github.com/flutter/engine/issues/8315)) f521df383 Fixed service isolate not being initialized correctly due to bad name ([flutter/engine#8251](https://github.com/flutter/engine/issues/8251)) 80b825ce8 Roll src/third_party/dart 093c2909fe..f3fd1943fc (13 commits) ([flutter/engine#8310](https://github.com/flutter/engine/issues/8310)) 7e77d5c48 Revert &[#34](https://github.com/flutter/flutter/issues/34);Allow specification of std::functions as native entrypoints from Dart code. ([#8309](https://github.com/flutter/flutter/issues/8309))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8312](https://github.com/flutter/engine/issues/8312)) 400a86a62 Allow specification of std::functions as native entrypoints from Dart code. ([flutter/engine#8309](https://github.com/flutter/engine/issues/8309)) 51f23fedf [vulkan] Add FUCHSIA external sem/mem extensions 78de8dcb4 Enable lambda like native callbacks in tests and add support for custom entrypoints. ([flutter/engine#8299](https://github.com/flutter/engine/issues/8299)) 2812c7db4 Roll src/third_party/skia 62fd6c411622..d2ca31218bc4 (9 commits) ([flutter/engine#8307](https://github.com/flutter/engine/issues/8307)) 95f9134e9 Roll src/third_party/skia d90004516a63..62fd6c411622 (4 commits) ([flutter/engine#8306](https://github.com/flutter/engine/issues/8306)) 358273bd2 Roll src/third_party/skia 33211b2c7a02..d90004516a63 (1 commits) ([flutter/engine#8305](https://github.com/flutter/engine/issues/8305)) 2804057bf Roll src/third_party/skia 80dd599e91d0..33211b2c7a02 (1 commits) ([flutter/engine#8303](https://github.com/flutter/engine/issues/8303)) a673bbf6f Roll src/third_party/skia c5ee499f2c59..80dd599e91d0 (5 commits) ([flutter/engine#8301](https://github.com/flutter/engine/issues/8301)) d88037dc0 Roll src/third_party/dart fa74184b7a..093c2909fe (71 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Pass FLUTTER_TOOL_ARGS to snapshot command. ([#29938](https://github.com/flutter/flutter/issues/29938))|
|Kate Lovett <<EMAIL>>|Embedding new diagrams for API Docs ([#29697](https://github.com/flutter/flutter/issues/29697))  * Embedding new diarams for API docs: Container, ListView, Align.  * Moving the diagrams to be enclosed within the snippet windows.  * Fixed wording for the alt image text.|
|Jonah Williams <<EMAIL>>|ensure packages file is updated when using build_runner ([#29885](https://github.com/flutter/flutter/issues/29885))|
|Chris Yang <<EMAIL>>|Include platformViewId in semantics tree for iOS ([#29304](https://github.com/flutter/flutter/issues/29304))  Include the platformViewId of PlatformViews in the semantics tree. The accessibility bridge in the engine can use this id to steal the semantics nodes from the actual platform view and stick them into Flutter's semantics tree.  It is the iOS PlatformView counter part of https://github.com/flutter/flutter/pull/28953. It reverts the change in https://github.com/flutter/flutter/pull/28953/commits/5b5d6e89ec50f8f51766fb778d0e244f183a5000 and https://github.com/flutter/flutter/pull/28953/commits/03fd797eb8955561c2654b51035175202a25da9d.  https://github.com/flutter/flutter/issues/29302|
|Jonah Williams <<EMAIL>>|Lazy cache 5: The Empire Strikes Back ([#29986](https://github.com/flutter/flutter/issues/29986))|
|xster <<EMAIL>>|Let CupertinoPageScaffold have tap status bar to scroll to top ([#29946](https://github.com/flutter/flutter/issues/29946))|
|liyuqian <<EMAIL>>|Change `-c` to `--enable-asserts` ([#30055](https://github.com/flutter/flutter/issues/30055))  Unblock the engine roll that includes the Dart roll that removes `-c`.|
|LongCatIsLooong <<EMAIL>>|update CupertinoSwitch documentation ([#29987](https://github.com/flutter/flutter/issues/29987))  Documented CupertinoSwitch.value that it can't be null and added an assertion value != null in the constructor.  Related issue: [#12948](https://github.com/flutter/flutter/issues/12948)|
|Justin McCandless <<EMAIL>>|Docs explaining how to avoid the problem of height toggling with error ([#29811](https://github.com/flutter/flutter/issues/29811))  Documentation that explains a common use case seen in Github issues where users were surprised by TextFormField growth to accommodate validation messages.|
|LongCatIsLooong <<EMAIL>>|Remove unwanted gap between navigation bar and safe area's child ([#29943](https://github.com/flutter/flutter/issues/29943))  Remove the additional top padding from CupertinoPageScaffold's MediaQuery when the navigation bar is opaque (as the padding was already consumed by the navigation bar).  Related Issue: [#29136](https://github.com/flutter/flutter/issues/29136)|
|Dwayne Slater <<EMAIL>>|Allow PointerEnterEvent and PointerExitEvents to be created from any PointerEvent ([#28602](https://github.com/flutter/flutter/issues/28602))  Avoids an implicit downcast + crash in MouseTracker, since the last pointer event can be a Move or Down event, in addition to Hover.|
|rami-a <<EMAIL>>|Fix issue with account drawer header arrow rotating when setState is called ([#29980](https://github.com/flutter/flutter/issues/29980))  The UserAccountsDrawerHeader had a bug with the arrow rotating whenever setState was called on its parent. This change makes sure that we check that the state actually changed before allowing the animation to trigger.  Fixes [#25801](https://github.com/flutter/flutter/issues/25801)|
|Jonah Williams <<EMAIL>>|Watch wildcard directories in addition to asset bundle ([#29883](https://github.com/flutter/flutter/issues/29883))|
|Jonah Williams <<EMAIL>>|ensure that fe server is always built with tool ([#29768](https://github.com/flutter/flutter/issues/29768))|
|Dan Field <<EMAIL>>|Avoid overwriting task result for non-leak checkers ([#29989](https://github.com/flutter/flutter/issues/29989))|
|Kate Lovett <<EMAIL>>|Linking Higher & Lower Class Docs ([#29758](https://github.com/flutter/flutter/issues/29758))  * Adding linking between higher and lower level classes in the API Docs. ref:[#22859](https://github.com/flutter/flutter/issues/22859)  * Fixed links between classes.|
|Jonah Williams <<EMAIL>>|Revert "Lazy cache 4 ([#29785](https://github.com/flutter/flutter/issues/29785))" ([#29985](https://github.com/flutter/flutter/issues/29985))|
|Amir Hardon <<EMAIL>>|Limit the semantic nodes ID range to 2^16 ([#29928](https://github.com/flutter/flutter/issues/29928))  This PR limits the framework generated semantic node IDs to be smaller than 2^16, this allows to safely generate semantic node in the engine with IDs >= 2^16 avoiding ID collision (which is done in [flutter/engine#8250](https://github.com/flutter/engine/issues/8250)).|
|Zachary Anderson <<EMAIL>>|[fuchsia] Fix flutter_tool BUILD.gn deps ([#29942](https://github.com/flutter/flutter/issues/29942))|
|Jonah Williams <<EMAIL>>|Lazy cache 4 ([#29785](https://github.com/flutter/flutter/issues/29785))|
|hyjfine <<EMAIL>>|Let CupertinoTextField's clear button also call onChanged ([#29474](https://github.com/flutter/flutter/issues/29474))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine af64f725f6c7..0d83a2ecd1bb (2 commits) ([#29948](https://github.com/flutter/flutter/issues/29948))   https://github.com/flutter/engine/compare/af64f725f6c7...0d83a2ecd1bb  git log af64f725f6c74f5d1287a8be8511691119354088..0d83a2ecd1bb78f8e1f0ed9719ff3751cdfdd064 --no-merges --oneline 0d83a2ecd Roll src/third_party/skia 15dc59e65cfa..c5ee499f2c59 (2 commits) ([flutter/engine#8298](https://github.com/flutter/engine/issues/8298)) 42d062f95 [fuchsia] Add missing trace macros ([flutter/engine#8297](https://github.com/flutter/engine/issues/8297))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Fix cache location, artifacts, and re-enable dart2js test ([#29783](https://github.com/flutter/flutter/issues/29783))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine dd6be2fd8371..af64f725f6c7 (3 commits) ([#29944](https://github.com/flutter/flutter/issues/29944))   https://github.com/flutter/engine/compare/dd6be2fd8371...af64f725f6c7  git log dd6be2fd83710de8392fb554927a5d495684a13e..af64f725f6c74f5d1287a8be8511691119354088 --no-merges --oneline af64f725f Fixing links between higher and lower classes. ([flutter/engine#8295](https://github.com/flutter/engine/issues/8295)) 22ee8ee08 Migrate existing embedder unit tests to use the fixture. ([flutter/engine#8296](https://github.com/flutter/engine/issues/8296)) 345ae7d37 Delegate a11y events and action to/from embedded Android platform views. ([flutter/engine#8250](https://github.com/flutter/engine/issues/8250))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|liyuqian <<EMAIL>>|Remove tranparent paint hack from BackdropFilter ([#29929](https://github.com/flutter/flutter/issues/29929))  We can remove the hack because https://github.com/flutter/engine/pull/8183 has been rolled into the framework.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 3a445cec8a51..dd6be2fd8371 (2 commits) ([#29937](https://github.com/flutter/flutter/issues/29937))   https://github.com/flutter/engine/compare/3a445cec8a51...dd6be2fd8371  git log 3a445cec8a51056e5903c12659b6fc97138e57db..dd6be2fd83710de8392fb554927a5d495684a13e --no-merges --oneline dd6be2fd8 Roll src/third_party/skia 42197c0bc68e..15dc59e65cfa (19 commits) ([flutter/engine#8294](https://github.com/flutter/engine/issues/8294)) d6ead18f4 Make it easy to write embedder unit tests by creating a fixture and config builder. ([flutter/engine#8276](https://github.com/flutter/engine/issues/8276))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine dcc72c7176bc..3a445cec8a51 (4 commits) ([#29924](https://github.com/flutter/flutter/issues/29924))   https://github.com/flutter/engine/compare/dcc72c7176bc...3a445cec8a51  git log dcc72c7176bcaa9fc9fa3798542f764ca6fe4ebc..3a445cec8a51056e5903c12659b6fc97138e57db --no-merges --oneline 3a445cec8 Roll src/third_party/skia cd54466473fe..42197c0bc68e (10 commits) ([flutter/engine#8292](https://github.com/flutter/engine/issues/8292)) fcc63e7d0 Roll src/third_party/skia 2e2fa9e3a06b..cd54466473fe (2 commits) ([flutter/engine#8291](https://github.com/flutter/engine/issues/8291)) 1b1209881 Roll src/third_party/skia c9f746cfb9f4..2e2fa9e3a06b (1 commits) ([flutter/engine#8290](https://github.com/flutter/engine/issues/8290)) b5813917f Roll src/third_party/skia e9a53fe3526a..c9f746cfb9f4 (1 commits) ([flutter/engine#8288](https://github.com/flutter/engine/issues/8288))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Danny Tuppeny <<EMAIL>>|Update Twitter handle @flutterio -> @FlutterDev ([#29908](https://github.com/flutter/flutter/issues/29908))  The @flutterio account now just directs people to @FlutterDev:  https://twitter.com/flutterio|
|Jonah Williams <<EMAIL>>|wrap Timeline calls in assert ([#29861](https://github.com/flutter/flutter/issues/29861))|
|Jonah Williams <<EMAIL>>|Update upgrade to reset off of hotfix branches ([#29786](https://github.com/flutter/flutter/issues/29786))|
|Jonah Williams <<EMAIL>>|dont fail build if codegen fails ([#29818](https://github.com/flutter/flutter/issues/29818))|
|engine-flutter-autoroll <<EMAIL>>|dcc72c717 Roll src/third_party/skia d79c95e8bcf2..e9a53fe3526a (1 commits) ([flutter/engine#8287](https://github.com/flutter/engine/issues/8287)) ([#29874](https://github.com/flutter/flutter/issues/29874))   https://github.com/flutter/engine/compare/d2bba21af27a...dcc72c7176bc  git log d2bba21af27a8d3f30c66aa014f324c786f3fb4e..dcc72c7176bcaa9fc9fa3798542f764ca6fe4ebc --no-merges --oneline dcc72c717 Roll src/third_party/skia d79c95e8bcf2..e9a53fe3526a (1 commits) ([flutter/engine#8287](https://github.com/flutter/engine/issues/8287))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 9f878fcb3ecd..d2bba21af27a (6 commits) ([#29869](https://github.com/flutter/flutter/issues/29869))   https://github.com/flutter/engine/compare/9f878fcb3ecd...d2bba21af27a  git log 9f878fcb3ecdcc2c6ba0c6d958282c176f008183..d2bba21af27a8d3f30c66aa014f324c786f3fb4e --no-merges --oneline d2bba21af Roll src/third_party/skia ab4c138c0ef3..d79c95e8bcf2 (2 commits) ([flutter/engine#8286](https://github.com/flutter/engine/issues/8286)) cee2bd732 Roll src/third_party/skia 97d957d1db65..ab4c138c0ef3 (3 commits) ([flutter/engine#8285](https://github.com/flutter/engine/issues/8285)) ef3cbe8ac Roll src/third_party/skia 4c0b5c4b79dd..97d957d1db65 (1 commits) ([flutter/engine#8284](https://github.com/flutter/engine/issues/8284)) bd4f6d726 Roll src/third_party/skia b1dbbc8509c6..4c0b5c4b79dd (2 commits) ([flutter/engine#8283](https://github.com/flutter/engine/issues/8283)) 8d23b21d1 Roll src/third_party/skia 61bd91237778..b1dbbc8509c6 (2 commits) ([flutter/engine#8282](https://github.com/flutter/engine/issues/8282)) 61838cab6 Roll src/third_party/skia ec1c6fdfdd11..61bd91237778 (1 commits) ([flutter/engine#8280](https://github.com/flutter/engine/issues/8280))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|9f878fcb3 Roll src/third_party/skia e86cc5039026..ec1c6fdfdd11 (1 commits) ([flutter/engine#8279](https://github.com/flutter/engine/issues/8279)) ([#29836](https://github.com/flutter/flutter/issues/29836))   https://github.com/flutter/engine/compare/62c88d690272...9f878fcb3ecd  git log 62c88d690272f0b78047758bb9d8ffa8bbb870cc..9f878fcb3ecdcc2c6ba0c6d958282c176f008183 --no-merges --oneline 9f878fcb3 Roll src/third_party/skia e86cc5039026..ec1c6fdfdd11 (1 commits) ([flutter/engine#8279](https://github.com/flutter/engine/issues/8279))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|62c88d690 Roll src/third_party/skia 33332f1e100c..e86cc5039026 (1 commits) ([flutter/engine#8278](https://github.com/flutter/engine/issues/8278)) ([#29832](https://github.com/flutter/flutter/issues/29832))   https://github.com/flutter/engine/compare/f8e3a40f9ba7...62c88d690272  git log f8e3a40f9ba701120724b5d6e8ff790d50f18083..62c88d690272f0b78047758bb9d8ffa8bbb870cc --no-merges --oneline 62c88d690 Roll src/third_party/skia 33332f1e100c..e86cc5039026 (1 commits) ([flutter/engine#8278](https://github.com/flutter/engine/issues/8278))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5f8ae420c1ac..f8e3a40f9ba7 (2 commits) ([#29830](https://github.com/flutter/flutter/issues/29830))   https://github.com/flutter/engine/compare/5f8ae420c1ac...f8e3a40f9ba7  git log 5f8ae420c1ac61bbbb26e61251d129c879fc788d..f8e3a40f9ba701120724b5d6e8ff790d50f18083 --no-merges --oneline f8e3a40f9 Roll buildroot to 9c7b023ff266ee58b00fe60326fa1db910a087f3 ([flutter/engine#8277](https://github.com/flutter/engine/issues/8277)) c12696cc5 Roll src/third_party/skia f9264bac4e99..33332f1e100c (27 commits) ([flutter/engine#8275](https://github.com/flutter/engine/issues/8275))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|roll engine to 5f8ae420c1ac61bbbb26e61251d129c879fc788d ([#29817](https://github.com/flutter/flutter/issues/29817))|
|Mouad Debbar <<EMAIL>>|Add support for text selection via mouse to Cupertino text fields ([#29769](https://github.com/flutter/flutter/issues/29769))|
|Alexandre Ardhuin <<EMAIL>>|some formatting of map, parameters and spaces ([#29760](https://github.com/flutter/flutter/issues/29760))|
|Dan Field <<EMAIL>>|Detect and cleanup leaky processes ([#29196](https://github.com/flutter/flutter/issues/29196))  * Detect and cleanup leaky processes * Add flaky tests for detecting leaked processes|
|Luka Knezic <<EMAIL>>|Update CupertinoTextField ([#29008](https://github.com/flutter/flutter/issues/29008))|
|Abhijeeth Padarthi <<EMAIL>>|Removes unnecessary "new" in documentation ([#29779](https://github.com/flutter/flutter/issues/29779))|
|Amir Hardon <<EMAIL>>|Revert "Update upgrade to rebase and stash local changes. ([#29192](https://github.com/flutter/flutter/issues/29192))" ([#29780](https://github.com/flutter/flutter/issues/29780))  This reverts commit e38be671a77b6c09dceb74ad2e63cc4b8671c83d.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine d4d4883216fd..b68f7e72aa71 (7 commits) ([#29776](https://github.com/flutter/flutter/issues/29776))   https://github.com/flutter/engine/compare/d4d4883216fd...b68f7e72aa71  git log d4d4883216fd1c1dfc17d41418a960c002cfff57..b68f7e72aa71eb75969bd674d78a5b40ee5ed89f --no-merges --oneline b68f7e72a Allow specifying an alternate Mac host SDK. ([flutter/engine#8249](https://github.com/flutter/engine/issues/8249)) 3df694300 Roll src/third_party/dart cabaa78cc5..ba0147ac07 (3 commits) ([flutter/engine#8248](https://github.com/flutter/engine/issues/8248)) 69c4d82f5 Roll src/third_party/skia d6ab77a2e20f..19fb3971bca5 (17 commits) ([flutter/engine#8245](https://github.com/flutter/engine/issues/8245)) 06635d38a Mirror Android platform views a11y tree in the Flutter a11y tree. ([flutter/engine#8237](https://github.com/flutter/engine/issues/8237)) 28433c513 FlutterJNI no longer asserts it is attached when dispatching platform messages and instead fizzles with a warning if not attached. Not sure what root cause of issue is, but this is necessary to avoid crashes. ([flutter/engine#8246](https://github.com/flutter/engine/issues/8246)) 1addfb596 Roll src/third_party/dart 5e9df35a57..cabaa78cc5 (36 commits) ([flutter/engine#8244](https://github.com/flutter/engine/issues/8244)) 13e3b7101 Roll src/third_party/skia a695da930510..d6ab77a2e20f (11 commits) ([flutter/engine#8243](https://github.com/flutter/engine/issues/8243))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|update fuchsia-attach ([#29764](https://github.com/flutter/flutter/issues/29764))|
|Jonah Williams <<EMAIL>>|Use Dart version in script cache check ([#29721](https://github.com/flutter/flutter/issues/29721))|
|xster <<EMAIL>>|Add heart shapes to CupertinoIcons ([#29630](https://github.com/flutter/flutter/issues/29630))|
|Michael Goderbauer <<EMAIL>>|Fix transition to stock details ([#29638](https://github.com/flutter/flutter/issues/29638))|
|Rubens de Melo <<EMAIL>>|Include brackets on OutlineButton doc ([#29654](https://github.com/flutter/flutter/issues/29654))|
|Jonah Williams <<EMAIL>>|Update upgrade to rebase and stash local changes. ([#29192](https://github.com/flutter/flutter/issues/29192))|
|Jonah Williams <<EMAIL>>|Allowing adding/updating packages during hot reload ([#29747](https://github.com/flutter/flutter/issues/29747))|
|Mouad Debbar <<EMAIL>>|Avoid flickering while dragging to select text ([#29563](https://github.com/flutter/flutter/issues/29563))|
|Jonah Williams <<EMAIL>>|Enable code generation features in tool  ([#29399](https://github.com/flutter/flutter/issues/29399))|
|Jonah Williams <<EMAIL>>|Revert "Enable platform views for Flutter Gallery on iOS. ([#29632](https://github.com/flutter/flutter/issues/29632))" ([#29754](https://github.com/flutter/flutter/issues/29754))|
|Amir Hardon <<EMAIL>>|Enable platform views for Flutter Gallery on iOS. ([#29632](https://github.com/flutter/flutter/issues/29632))  This results in running Flutter Gallery with one less thread (platform and gpu threads are the the same).  This is very likely to cause a regression in the iOS Gallery benchmarks. I'm mainly interested in landing this to see how much the benchmarks regress, and it's likely that we will revert it shortly after landing.|
|Shayne Kelly II <<EMAIL>>|Update DropdownButton underline to be customizable ([#29138](https://github.com/flutter/flutter/issues/29138))|
|Christopher Fujino <<EMAIL>>|Fix more tests for ANSI terminals ([#29699](https://github.com/flutter/flutter/issues/29699))  * have tests that log error messages use a FakePlatform that does not support Ansi color, to fix tests started from color terminals * add the override to the other tests too, in case someone copy & pastes them in the future for a test involving an error message|
|Jonah Williams <<EMAIL>>|fix asset reloading ([#29469](https://github.com/flutter/flutter/issues/29469))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 250b99bd275a..d4d4883216fd (3 commits) ([#29742](https://github.com/flutter/flutter/issues/29742))   https://github.com/flutter/engine/compare/250b99bd275a...d4d4883216fd  git log 250b99bd275ad7622f239a8042f6a6d31b5b95f1..d4d4883216fd1c1dfc17d41418a960c002cfff57 --no-merges --oneline d4d488321 Roll src/third_party/skia 174a3f61fd5d..a695da930510 (2 commits) ([flutter/engine#8242](https://github.com/flutter/engine/issues/8242)) 2d5376c09 Roll src/third_party/skia 2c4ceca62d27..174a3f61fd5d (1 commits) ([flutter/engine#8239](https://github.com/flutter/engine/issues/8239)) b05399901 Roll src/third_party/skia e409441aee0d..2c4ceca62d27 (1 commits) ([flutter/engine#8238](https://github.com/flutter/engine/issues/8238))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|250b99bd2 Roll src/third_party/skia 4652930a7dfc..e409441aee0d (1 commits) ([flutter/engine#8236](https://github.com/flutter/engine/issues/8236)) ([#29719](https://github.com/flutter/flutter/issues/29719))   https://github.com/flutter/engine/compare/fa435385b6b9...250b99bd275a  git log fa435385b6b9f80080997dd38ad66bbe4bb0a152..250b99bd275ad7622f239a8042f6a6d31b5b95f1 --no-merges --oneline 250b99bd2 Roll src/third_party/skia 4652930a7dfc..e409441aee0d (1 commits) ([flutter/engine#8236](https://github.com/flutter/engine/issues/8236))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Alexander Aprelev <<EMAIL>>|Use source list from the compiler to track invalidated files for hot reload. ([#29693](https://github.com/flutter/flutter/issues/29693))  * Use source list from the compiler to track invalidated files.  * Revert accidental change  * Fix first-time-seen-the-file logic  * Fix/simplify invalidate logic now that we can rely on compiler to let us know what is the cut-off point for invalidation.  * Update devfs mock to accommodate for new fields  * Fix deleted files case  * Analyzer found missing final|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 188adf7e5ec5..fa435385b6b9 (2 commits) ([#29715](https://github.com/flutter/flutter/issues/29715))   https://github.com/flutter/engine/compare/188adf7e5ec5...fa435385b6b9  git log 188adf7e5ec521f7859d5a0a1609fdcb688436b2..fa435385b6b9f80080997dd38ad66bbe4bb0a152 --no-merges --oneline fa435385b Use the GPU thread for Android surface on-screen context lifecycle operations ([flutter/engine#8234](https://github.com/flutter/engine/issues/8234)) 10e1addd8 Roll src/third_party/skia 7e2c0614a2fd..4652930a7dfc (5 commits) ([flutter/engine#8232](https://github.com/flutter/engine/issues/8232))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine d8c89e2bfe9b..188adf7e5ec5 (3 commits) ([#29709](https://github.com/flutter/flutter/issues/29709))   https://github.com/flutter/engine/compare/d8c89e2bfe9b...188adf7e5ec5  git log d8c89e2bfe9b0b0d50da41c1349ef1534517c3c8..188adf7e5ec521f7859d5a0a1609fdcb688436b2 --no-merges --oneline 188adf7e5 Removed Activity reference from AccessibilityBridge by using a View for insets instead of the Activity ([#18115](https://github.com/flutter/flutter/issues/18115)) ([flutter/engine#8231](https://github.com/flutter/engine/issues/8231)) d452dd5c3 Initial import of GLFW Linux shell from FDE ([flutter/engine#8159](https://github.com/flutter/engine/issues/8159)) 512004503 Remove jsoncpp from desktop Linux shell setup ([flutter/engine#8230](https://github.com/flutter/engine/issues/8230))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Reland composite physical layers for all platforms ([#29701](https://github.com/flutter/flutter/issues/29701))  * Composite physical layers even if elevation is 0|
|engine-flutter-autoroll <<EMAIL>>|d8c89e2bf Add the Linux desktop setup script to Dockerfile ([flutter/engine#8227](https://github.com/flutter/engine/issues/8227)) ([#29703](https://github.com/flutter/flutter/issues/29703))   https://github.com/flutter/engine/compare/6a8a45fc4f69...d8c89e2bfe9b  git log 6a8a45fc4f698364d03b5902c1818d2acc79db22..d8c89e2bfe9b0b0d50da41c1349ef1534517c3c8 --no-merges --oneline d8c89e2bf Add the Linux desktop setup script to Dockerfile ([flutter/engine#8227](https://github.com/flutter/engine/issues/8227))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Anthony <<EMAIL>>|[Material] Create a FloatingActionButton ThemeData and honor it within the FloatingActionButton ([#28735](https://github.com/flutter/flutter/issues/28735))  Adds a FloatingActionButtonThemeData so that FABs can be themed at the Theme level and independently. The properties that are now on the theme must be defaulted at the build level, in order to respect any contributing theme properties. Because of this, some tests had to be modified to look at properties after they are built. Also, since default behaviors are now tested in the FAB Theme test, some default tests in the FAB test no longer applied and were removed.  The themable properties are: -backgroundColor -foregroundColor -elevation -disabledElevation -highlightElevation -shape|
|Jonah Williams <<EMAIL>>|Download secondary SDK ([#29633](https://github.com/flutter/flutter/issues/29633))|
|Jonah Williams <<EMAIL>>|Add builders and engine hash to fingerprint ([#29434](https://github.com/flutter/flutter/issues/29434))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 96ad0c8926b0..6a8a45fc4f69 (16 commits) ([#29698](https://github.com/flutter/flutter/issues/29698))   https://github.com/flutter/engine/compare/96ad0c8926b0...6a8a45fc4f69  git log 96ad0c8926b0493d4276a39ee9bc6de6fa97bece..6a8a45fc4f698364d03b5902c1818d2acc79db22 --no-merges --oneline 6a8a45fc4 Have the AccessibilityBridge attach/detach itself to the ([flutter/engine#8229](https://github.com/flutter/engine/issues/8229)) 45b19e47c Roll src/third_party/skia ba91f65f2070..7e2c0614a2fd (13 commits) ([flutter/engine#8228](https://github.com/flutter/engine/issues/8228)) 1dbd2046b Moved io.flutter.embedding.engine.android package to io.flutter.embedding.android ([flutter/engine#8221](https://github.com/flutter/engine/issues/8221)) bb354363e Roll src/third_party/dart 70e3e67dd7..5e9df35a57 (45 commits) 3b19a4d30 Removed dart_plugin_tag from DEPS d9b6629b6 Roll src/third_party/skia 4273a150f84d..ba91f65f2070 (6 commits) ([flutter/engine#8225](https://github.com/flutter/engine/issues/8225)) e88573aef Roll src/third_party/skia d7d93001ead2..4273a150f84d (1 commits) ([flutter/engine#8224](https://github.com/flutter/engine/issues/8224)) be9067cc7 Roll src/third_party/skia 37a9294d2eb9..d7d93001ead2 (2 commits) ([flutter/engine#8223](https://github.com/flutter/engine/issues/8223)) ee4abba58 Roll src/third_party/skia 2894d13a0d9b..37a9294d2eb9 (1 commits) ([flutter/engine#8222](https://github.com/flutter/engine/issues/8222)) 3496156bd Roll src/third_party/skia dd0544078d05..2894d13a0d9b (33 commits) ([flutter/engine#8220](https://github.com/flutter/engine/issues/8220)) a1dcb2ea9 [ios] Set contentsScale before we commit CATransaction ([flutter/engine#8218](https://github.com/flutter/engine/issues/8218)) fa1931f6a Send macOS keyboard data to the engine ([flutter/engine#8219](https://github.com/flutter/engine/issues/8219)) 45f69ac47 Plumb a reference of PlatformViewsController and AccessibilityBridge to each other ([flutter/engine#8208](https://github.com/flutter/engine/issues/8208)) 7cbbdb400 libtxt: more accurate tracking of run positioning and width for justified text ([flutter/engine#8214](https://github.com/flutter/engine/issues/8214)) 172810342 Add a build dependencies script for Linux desktop ([flutter/engine#8160](https://github.com/flutter/engine/issues/8160)) d764b69b2 Add docs for helpful commands to fix format ([flutter/engine#8171](https://github.com/flutter/engine/issues/8171))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Alexandre Ardhuin <<EMAIL>>|some spaces formatting ([#29452](https://github.com/flutter/flutter/issues/29452))  * some space formattings  * always use blocks in if-else if a block is used  * format spaces in for and while  * allow multiline if conditions  * fix missing space|
|Marco Scannadinari <<EMAIL>>|Adjust remaining Cupertino route animations to match native ([#28597](https://github.com/flutter/flutter/issues/28597))|
|Fedor Korotkov <<EMAIL>>|Speed up CI via mojave-flutter image ([#29669](https://github.com/flutter/flutter/issues/29669))|
|Michael Goderbauer <<EMAIL>>|Fix links on homepage of API docs ([#29641](https://github.com/flutter/flutter/issues/29641))|
|Lau Ching Jun <<EMAIL>>|Fix typo ([#29625](https://github.com/flutter/flutter/issues/29625))|
|Jonah Williams <<EMAIL>>|Improve flutter test startup time ([#29404](https://github.com/flutter/flutter/issues/29404))|
|Shi-Hao Hong <<EMAIL>>|DropdownButton Icon customizability ([#29572](https://github.com/flutter/flutter/issues/29572))|
|Amir Hardon <<EMAIL>>|Revert last 2 engine rolls. ([#29684](https://github.com/flutter/flutter/issues/29684))  Reverting to the engine version prior to: https://github.com/flutter/flutter/pull/29658|
|engine-flutter-autoroll <<EMAIL>>|3496156bd Roll src/third_party/skia dd0544078d05..2894d13a0d9b (33 commits) ([flutter/engine#8220](https://github.com/flutter/engine/issues/8220)) ([#29659](https://github.com/flutter/flutter/issues/29659))   https://github.com/flutter/engine/compare/a1dcb2ea987e...3496156bd9bf  git log a1dcb2ea987efe0e257832160a1b0583ac219584..3496156bd9bfe61788bf64ce3ca3cf1bc2663f95 --no-merges --oneline 3496156bd Roll src/third_party/skia dd0544078d05..2894d13a0d9b (33 commits) ([flutter/engine#8220](https://github.com/flutter/engine/issues/8220))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 96ad0c8926b0..a1dcb2ea987e (6 commits) ([#29658](https://github.com/flutter/flutter/issues/29658))   https://github.com/flutter/engine/compare/96ad0c8926b0...a1dcb2ea987e  git log 96ad0c8926b0493d4276a39ee9bc6de6fa97bece..a1dcb2ea987efe0e257832160a1b0583ac219584 --no-merges --oneline a1dcb2ea9 [ios] Set contentsScale before we commit CATransaction ([flutter/engine#8218](https://github.com/flutter/engine/issues/8218)) fa1931f6a Send macOS keyboard data to the engine ([flutter/engine#8219](https://github.com/flutter/engine/issues/8219)) 45f69ac47 Plumb a reference of PlatformViewsController and AccessibilityBridge to each other ([flutter/engine#8208](https://github.com/flutter/engine/issues/8208)) 7cbbdb400 libtxt: more accurate tracking of run positioning and width for justified text ([flutter/engine#8214](https://github.com/flutter/engine/issues/8214)) 172810342 Add a build dependencies script for Linux desktop ([flutter/engine#8160](https://github.com/flutter/engine/issues/8160)) d764b69b2 Add docs for helpful commands to fix format ([flutter/engine#8171](https://github.com/flutter/engine/issues/8171))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Ben Konyi <<EMAIL>>|Manual engine roll for 2019-03-19 ([#29627](https://github.com/flutter/flutter/issues/29627))  * Manual engine roll for 2019-03-19 * Applying patch for Dart SDK changes (see PR [#29004](https://github.com/flutter/flutter/issues/29004))|
|Jonah Williams <<EMAIL>>|prevent stream notifications from interfering with reload ([#29467](https://github.com/flutter/flutter/issues/29467))|
|engine-flutter-autoroll <<EMAIL>>|571964e1d Allow exported __const on iOS ([flutter/engine#8217](https://github.com/flutter/engine/issues/8217)) ([#29624](https://github.com/flutter/flutter/issues/29624))   https://github.com/flutter/engine/compare/e28fc3c810ee...571964e1d7b4  git log e28fc3c810ee3c62ad201bc49f4934f0ed20a82c..571964e1d7b49e5424c800b461006207d670736f --no-merges --oneline 571964e1d Allow exported __const on iOS ([flutter/engine#8217](https://github.com/flutter/engine/issues/8217))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Revert "Reland composite physical layers on all platforms ([#29532](https://github.com/flutter/flutter/issues/29532))" ([#29623](https://github.com/flutter/flutter/issues/29623))  This reverts commit 803b15e0db9606f6f4c84925f9c6cb369be72394.|
|Shi-Hao Hong <<EMAIL>>|added friendlier error for invalid AndroidManifest.xml ([#29604](https://github.com/flutter/flutter/issues/29604))|
|Phil Quitslund <<EMAIL>>|make literals const for @immutable constructors ([#29619](https://github.com/flutter/flutter/issues/29619))|
|Hans Muller <<EMAIL>>|Update progress indicator API docs ([#29564](https://github.com/flutter/flutter/issues/29564))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5088735e5f54..e28fc3c810ee (9 commits) ([#29615](https://github.com/flutter/flutter/issues/29615))   https://github.com/flutter/engine/compare/5088735e5f54...e28fc3c810ee  git log 5088735e5f54f7fb34215b6665cb4da1fd8f075d..e28fc3c810ee3c62ad201bc49f4934f0ed20a82c --no-merges --oneline e28fc3c81 Define the dart_platform_sdk GN variable only on host targets ([flutter/engine#8206](https://github.com/flutter/engine/issues/8206)) e73dbf6a6 Roll src/third_party/skia 389b4b267d15..7eba20aef187 (2 commits) ([flutter/engine#8209](https://github.com/flutter/engine/issues/8209)) bc8ebcb96 Export FlutterSemanticsUpdateNotification and improve docs ([flutter/engine#8203](https://github.com/flutter/engine/issues/8203)) b404c69fd [scenic][SCN-1054] Move back off of SetTranslationRH ([flutter/engine#8174](https://github.com/flutter/engine/issues/8174)) 71e2d7d8c Roll src/third_party/skia 4a522b7e916c..389b4b267d15 (32 commits) ([flutter/engine#8205](https://github.com/flutter/engine/issues/8205)) ad5b722a7 Simplify the fallback waiter and add traces for vsync scheduling overhead. ([flutter/engine#8185](https://github.com/flutter/engine/issues/8185)) 6980a8356 [platform_view] iOSP platformView composition optimize. ([flutter/engine#8202](https://github.com/flutter/engine/issues/8202)) 568841923 Disable build_ios due to large queue times. ([flutter/engine#8204](https://github.com/flutter/engine/issues/8204)) 629072277 Merge only gpu and platform threads for platform views, fix deadlock. ([flutter/engine#8045](https://github.com/flutter/engine/issues/8045))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|remove explicit frame schedule ([#29461](https://github.com/flutter/flutter/issues/29461))|
|Priit Lätt <<EMAIL>>|Use async execution for xcodebuild commands ([#29048](https://github.com/flutter/flutter/issues/29048))|
|Brian Wilkerson <<EMAIL>>|Update another SDK constraint ([#29454](https://github.com/flutter/flutter/issues/29454))|
|Dan Field <<EMAIL>>|Remove timeout from add2app test for iOS ([#28746](https://github.com/flutter/flutter/issues/28746))|
|Dan Field <<EMAIL>>|Add semantic label finders ([#29342](https://github.com/flutter/flutter/issues/29342))  * Add semantic label finders|
|liyuqian <<EMAIL>>|Manually roll engine to 5088735e5 ([#29566](https://github.com/flutter/flutter/issues/29566))  Need this manual roll because of golden changes.  flutter/engine@5088735e5 Clip to clip_rect instead of paint bounds ([flutter/engine#8183](https://github.com/flutter/engine/issues/8183)) flutter/engine@8818a6c41 Roll src/third_party/skia 2c043c288169..4a522b7e916c (3 commits) ([flutter/engine#8201](https://github.com/flutter/engine/issues/8201)) flutter/engine@048d70bc0 Support dartdevc, dart2js with shared source files, dartdevc sdk ([flutter/engine#8105](https://github.com/flutter/engine/issues/8105)) flutter/engine@e0782dec7 Roll src/third_party/skia 576b2a5596bc..2c043c288169 (2 commits) ([flutter/engine#8200](https://github.com/flutter/engine/issues/8200))|
|Jonah Williams <<EMAIL>>|make build runner configurable ([#29568](https://github.com/flutter/flutter/issues/29568))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine e75f6de01fee..048d70bc0948 (2 commits) ([#29569](https://github.com/flutter/flutter/issues/29569))   https://github.com/flutter/engine/compare/e75f6de01fee...048d70bc0948  git log e75f6de01fee738eb15f24efb0ec549587009f1a..048d70bc0948de3bad60e2056f186f336cd5aad9 --no-merges --oneline 048d70bc0 Support dartdevc, dart2js with shared source files, dartdevc sdk ([flutter/engine#8105](https://github.com/flutter/engine/issues/8105)) e0782dec7 Roll src/third_party/skia 576b2a5596bc..2c043c288169 (2 commits) ([flutter/engine#8200](https://github.com/flutter/engine/issues/8200))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Filip Hracek <<EMAIL>>|Add sample to forEachTween ([#29195](https://github.com/flutter/flutter/issues/29195))  The current documentation for `AnimatedWidgetBaseState.forEachTween` is exhaustive but a little hard to grok. This dartdoc example provides some clarity.|
|Dan Field <<EMAIL>>|Reland composite physical layers on all platforms ([#29532](https://github.com/flutter/flutter/issues/29532))  Reland composite physical layers and update tests|
|xster <<EMAIL>>|Make real JSON in arb ([#29463](https://github.com/flutter/flutter/issues/29463))|
|Francisco Magdaleno <<EMAIL>>|Adds macOS raw keyboard mapping ([#29231](https://github.com/flutter/flutter/issues/29231))|
|Jonah Williams <<EMAIL>>|Ensure that different formatting of ndk properties file does not crash tool ([#29528](https://github.com/flutter/flutter/issues/29528))|
|Ivan Schütz <<EMAIL>>|Improve Navigator documentation ([#29540](https://github.com/flutter/flutter/issues/29540))|
|Dan Field <<EMAIL>>|Make it easier to ensure semantics in widgetTests ([#29387](https://github.com/flutter/flutter/issues/29387))  * Make it easier to ensure semantics|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5b09204e3037..e75f6de01fee (2 commits) ([#29537](https://github.com/flutter/flutter/issues/29537))   https://github.com/flutter/engine/compare/5b09204e3037...e75f6de01fee  git log 5b09204e3037a8e03fcffc9161cd474b41f4db3b..e75f6de01fee738eb15f24efb0ec549587009f1a --no-merges --oneline e75f6de01 Roll src/third_party/skia 895e1eedb420..576b2a5596bc (1 commits) ([flutter/engine#8198](https://github.com/flutter/engine/issues/8198)) d9fda1c20 Add --no-full-dart-sdk option to GN ([flutter/engine#8196](https://github.com/flutter/engine/issues/8196))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 4d9b9aacd076..5b09204e3037 (3 commits) ([#29522](https://github.com/flutter/flutter/issues/29522))   https://github.com/flutter/engine/compare/4d9b9aacd076...5b09204e3037  git log 4d9b9aacd07602031ab9cee863a74ecf02686ed4..5b09204e3037a8e03fcffc9161cd474b41f4db3b --no-merges --oneline 5b09204e3 Roll src/third_party/skia 72ef2d519a0e..895e1eedb420 (1 commits) ([flutter/engine#8195](https://github.com/flutter/engine/issues/8195)) a47e12d25 Roll src/third_party/skia 3ea586f9f959..72ef2d519a0e (1 commits) ([flutter/engine#8194](https://github.com/flutter/engine/issues/8194)) 5ca92fbe9 Roll src/third_party/skia 143542fa28e9..3ea586f9f959 (1 commits) ([flutter/engine#8193](https://github.com/flutter/engine/issues/8193))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine c6f9fe7e00e2..4d9b9aacd076 (2 commits) ([#29507](https://github.com/flutter/flutter/issues/29507))   https://github.com/flutter/engine/compare/c6f9fe7e00e2...4d9b9aacd076  git log c6f9fe7e00e2648de1a7aa02673b7771a9b92c49..4d9b9aacd07602031ab9cee863a74ecf02686ed4 --no-merges --oneline 4d9b9aacd Roll src/third_party/skia e1f2a572cec0..143542fa28e9 (1 commits) ([flutter/engine#8192](https://github.com/flutter/engine/issues/8192)) c14fde841 Roll src/third_party/skia d95286de1dda..e1f2a572cec0 (1 commits) ([flutter/engine#8191](https://github.com/flutter/engine/issues/8191))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|c6f9fe7e0 Roll src/third_party/skia 90155edf140b..d95286de1dda (2 commits) ([flutter/engine#8190](https://github.com/flutter/engine/issues/8190)) ([#29497](https://github.com/flutter/flutter/issues/29497))   https://github.com/flutter/engine/compare/0f97c920ff90...c6f9fe7e00e2  git log 0f97c920ff90aaa0cbfd960ef61089a124ffafae..c6f9fe7e00e2648de1a7aa02673b7771a9b92c49 --no-merges --oneline c6f9fe7e0 Roll src/third_party/skia 90155edf140b..d95286de1dda (2 commits) ([flutter/engine#8190](https://github.com/flutter/engine/issues/8190))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|0f97c920f Roll src/third_party/skia f3f7059cb736..90155edf140b (1 commits) ([flutter/engine#8189](https://github.com/flutter/engine/issues/8189)) ([#29493](https://github.com/flutter/flutter/issues/29493))   https://github.com/flutter/engine/compare/4a4f1823eb5b...0f97c920ff90  git log 4a4f1823eb5b3a08b703f0190ab309af8b65f721..0f97c920ff90aaa0cbfd960ef61089a124ffafae --no-merges --oneline 0f97c920f Roll src/third_party/skia f3f7059cb736..90155edf140b (1 commits) ([flutter/engine#8189](https://github.com/flutter/engine/issues/8189))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 015c0246aebc..4a4f1823eb5b (4 commits) ([#29491](https://github.com/flutter/flutter/issues/29491))   https://github.com/flutter/engine/compare/015c0246aebc...4a4f1823eb5b  git log 015c0246aebc6fc23f30004e3319b6d1e85a885a..4a4f1823eb5b3a08b703f0190ab309af8b65f721 --no-merges --oneline 4a4f1823e Roll src/third_party/skia 3d42a0521d12..f3f7059cb736 (6 commits) ([flutter/engine#8188](https://github.com/flutter/engine/issues/8188)) 39fea5502 Roll src/third_party/skia f34d0c79dc6c..3d42a0521d12 (1 commits) ([flutter/engine#8187](https://github.com/flutter/engine/issues/8187)) 926e65531 Roll src/third_party/skia d3cfbcae10eb..f34d0c79dc6c (2 commits) ([flutter/engine#8186](https://github.com/flutter/engine/issues/8186)) 575d61f49 Roll src/third_party/skia 49ea3194535c..d3cfbcae10eb (9 commits) ([flutter/engine#8184](https://github.com/flutter/engine/issues/8184))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|liyuqian <<EMAIL>>|Add doc about MediaQuery to Chip ([#29445](https://github.com/flutter/flutter/issues/29445))  Fixes https://github.com/flutter/flutter/issues/21232|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0e0e0a98f511..015c0246aebc (3 commits) ([#29464](https://github.com/flutter/flutter/issues/29464))   https://github.com/flutter/engine/compare/0e0e0a98f511...015c0246aebc  git log 0e0e0a98f5111ebf4032ef25eaf568f19867ecf1..015c0246aebc6fc23f30004e3319b6d1e85a885a --no-merges --oneline 015c0246a Fix log level typo from ERROR to INFO ([flutter/engine#8180](https://github.com/flutter/engine/issues/8180)) c033aa469 Fix include of libzx. ([flutter/engine#8181](https://github.com/flutter/engine/issues/8181)) d92f93730 Add flag to allow only building full sdk on release to speed up bots ([flutter/engine#8080](https://github.com/flutter/engine/issues/8080))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Switch flutter_tools from script to app-jit snapshot. ([#27749](https://github.com/flutter/flutter/issues/27749))|
|engine-flutter-autoroll <<EMAIL>>|0e0e0a98f Roll src/third_party/skia 46f5ee156155..49ea3194535c (8 commits) ([flutter/engine#8179](https://github.com/flutter/engine/issues/8179)) ([#29453](https://github.com/flutter/flutter/issues/29453))   https://github.com/flutter/engine/compare/7da45be4a9af...0e0e0a98f511  git log 7da45be4a9afec572ebc110ad52511f5e2dc63e3..0e0e0a98f5111ebf4032ef25eaf568f19867ecf1 --no-merges --oneline 0e0e0a98f Roll src/third_party/skia 46f5ee156155..49ea3194535c (8 commits) ([flutter/engine#8179](https://github.com/flutter/engine/issues/8179))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|fix for sometimes packages file is an APK ([#29456](https://github.com/flutter/flutter/issues/29456))|
|Jonah Williams <<EMAIL>>|Improve hot reload performance ([#28152](https://github.com/flutter/flutter/issues/28152))|
|engine-flutter-autoroll <<EMAIL>>|7da45be4a Roll src/third_party/skia 69600007e278..46f5ee156155 (8 commits) ([flutter/engine#8178](https://github.com/flutter/engine/issues/8178)) ([#29447](https://github.com/flutter/flutter/issues/29447))   https://github.com/flutter/engine/compare/cad97fe7467c...7da45be4a9af  git log cad97fe7467c60de645d47acffd0e00cd83178ab..7da45be4a9afec572ebc110ad52511f5e2dc63e3 --no-merges --oneline 7da45be4a Roll src/third_party/skia 69600007e278..46f5ee156155 (8 commits) ([flutter/engine#8178](https://github.com/flutter/engine/issues/8178))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|add assert if length of TabController and number of tabs do not match ([#29332](https://github.com/flutter/flutter/issues/29332))|
|Anthony <<EMAIL>>|Fix Flex class docs by replacing 'vertical space' with 'space on its main axis' so that the language fits both column and row. ([#29057](https://github.com/flutter/flutter/issues/29057))|
|Kate Lovett <<EMAIL>>|Sample code for Align class. ref:[#13637](https://github.com/flutter/flutter/issues/13637) ([#29442](https://github.com/flutter/flutter/issues/29442))|
|Mouad Debbar <<EMAIL>>|Fix text selection when user is dragging in the opposite direction ([#29395](https://github.com/flutter/flutter/issues/29395))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine b36068c3778c..cad97fe7467c (2 commits) ([#29441](https://github.com/flutter/flutter/issues/29441))   https://github.com/flutter/engine/compare/b36068c3778c...cad97fe7467c  git log b36068c3778c014ac999ce03412af2690bc713bc..cad97fe7467c60de645d47acffd0e00cd83178ab --no-merges --oneline cad97fe74 Update overlays_gr_context_ correctly ([flutter/engine#8175](https://github.com/flutter/engine/issues/8175)) c0690e6ec Roll src/third_party/skia aefecad7c0d2..69600007e278 (2 commits) ([flutter/engine#8177](https://github.com/flutter/engine/issues/8177))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|liyuqian <<EMAIL>>|Add dump-shader-skp flag to flutter tools ([#29258](https://github.com/flutter/flutter/issues/29258))  This is the accompanying change for https://github.com/flutter/engine/pull/8148 and it needs the engine PR to land first.  For https://github.com/flutter/flutter/issues/813|
|xster <<EMAIL>>|Cupertino localization step 1: add an English arb file ([#29200](https://github.com/flutter/flutter/issues/29200))|
|liyuqian <<EMAIL>>|No shrinking for BackdropFilter's cull rect ([#28174](https://github.com/flutter/flutter/issues/28174))  * No shrinking for BackdropFilter's cull rect  This will be a breaking change. Our old behavior may generate confusions for a sample app like our added golden test: Skia will shrink the cull rect (and thus the filtered area) to the text. The new behavior will fill the BackdropFilter to its parent/ancestor clip. This is more in align with our clip behaviors (no clip by default).  If this breaks your app, wrap the BackdropFilter with a ClipRect.  [wip] The golden images are not uploaded yet. I'll wait for the initial round of review to approve the golden test before uploading them.  * Statically define the callback  * Add TODO to remind the hacking code removal  * Nit fix  * Update goldens|
|Jason Simmons <<EMAIL>>|Use fs.identical to compare paths when finding the engine source path ([#29386](https://github.com/flutter/flutter/issues/29386))  This will handle paths with symlinks that resolve to the same location.|
|Edman P. Anjos <<EMAIL>>|Paint backgroundColor in CircularProgressIndicator ([#28004](https://github.com/flutter/flutter/issues/28004))|
|liyuqian <<EMAIL>>|--verbose-logging to verbose-logging in android ([#29383](https://github.com/flutter/flutter/issues/29383))  Sorry that I missed the typo during the review of https://github.com/flutter/flutter/pull/29321|
|Christian Wenz <<EMAIL>>|Properly escape Android SDK Manager path in error message ([#29134](https://github.com/flutter/flutter/issues/29134))|
|Dan Field <<EMAIL>>|Update README.md ([#29369](https://github.com/flutter/flutter/issues/29369))|
|Mike Leonhard <<EMAIL>>|[cupertino_icons] Add circle and circle_filled, for radio buttons. ([#29407](https://github.com/flutter/flutter/issues/29407))|
|Efthymis Sarmpanis <<EMAIL>>|Throw assertion error when a Hero has a Hero child. ([#28470](https://github.com/flutter/flutter/issues/28470))|
|Brian Wilkerson <<EMAIL>>|Update SDK constraints to reflect the fact that set literals are being used ([#29389](https://github.com/flutter/flutter/issues/29389))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 403337ebb893..b36068c3778c (10 commits) ([#29429](https://github.com/flutter/flutter/issues/29429))   https://github.com/flutter/engine/compare/403337ebb893...b36068c3778c  git log 403337ebb893380101d1fa9cc435ce9b6cfeb22c..b36068c3778c014ac999ce03412af2690bc713bc --no-merges --oneline b36068c37 Roll src/third_party/skia 05201fc7e77e..aefecad7c0d2 (5 commits) ([flutter/engine#8176](https://github.com/flutter/engine/issues/8176)) b32d0ab98 Bugfix: Prevent crash when responding to a platform message after FlutterJNI detaches from native ([#28651](https://github.com/flutter/flutter/issues/28651)). ([flutter/engine#8170](https://github.com/flutter/engine/issues/8170)) d9b2f0946 Roll src/third_party/skia 512e38091c85..05201fc7e77e (10 commits) ([flutter/engine#8173](https://github.com/flutter/engine/issues/8173)) 4b01d795f Add frame and target time metadata to vsync events and connect platform vsync events using flows. ([flutter/engine#8172](https://github.com/flutter/engine/issues/8172)) f7a092262 [fuchsia] Remove deprecated libraries from snapshot ([flutter/engine#8085](https://github.com/flutter/engine/issues/8085)) 09db84fad Android Embedding PR 19: Add accessibility to new FlutterView. ([flutter/engine#8109](https://github.com/flutter/engine/issues/8109)) 246f0e3f4 Add an allocator specific check to ensure that strings passed to the timeline are not heap allocated. ([flutter/engine#8168](https://github.com/flutter/engine/issues/8168)) 7a6bb9998 Roll src/third_party/skia feb720f746dc..512e38091c85 (11 commits) ([flutter/engine#8169](https://github.com/flutter/engine/issues/8169)) 5825bdac9 Roll src/third_party/dart 7d560f8385..7418238239 (61 commits) 66fdeb163 Add dump-shader-skp switch to help ShaderWarmUp ([flutter/engine#8148](https://github.com/flutter/engine/issues/8148))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|Disable widget inspector scroll test ([#29403](https://github.com/flutter/flutter/issues/29403))|
|Dan Field <<EMAIL>>|Roll engine to 403337ebb893380101d1fa9cc435ce9b6cfeb22c ([#29377](https://github.com/flutter/flutter/issues/29377))  * Manual engine roll with goldens|
|Justin McCandless <<EMAIL>>|Text field height attempt 2 ([#29250](https://github.com/flutter/flutter/issues/29250))  Adds the `minLines` and `expands` parameters for controlling text height.  The original PR was reverted, so this one contains a few extra fixes for the tests that were broken.|
|Jonah Williams <<EMAIL>>|Only run codegen at start of flutter_test ([#29171](https://github.com/flutter/flutter/issues/29171))|
|Jonah Williams <<EMAIL>>|Skip Dialog interaction test ([#29385](https://github.com/flutter/flutter/issues/29385))|
|Jonah Williams <<EMAIL>>|Revert "Manual engine roll with goldens ([#29363](https://github.com/flutter/flutter/issues/29363))" ([#29374](https://github.com/flutter/flutter/issues/29374))  This reverts commit c2e08913c063dea04f21b3e2db777146862c2f15.|
|Dan Field <<EMAIL>>|Manual engine roll with goldens ([#29363](https://github.com/flutter/flutter/issues/29363))|
|Shi-Hao Hong <<EMAIL>>|Implement labelPadding configuration in TabBarTheme  ([#29183](https://github.com/flutter/flutter/issues/29183))|
|Hans Muller <<EMAIL>>|Error message when TextSelectionOverlay finds no Overlay ([#29329](https://github.com/flutter/flutter/issues/29329))|
|Dan Field <<EMAIL>>|guard new formatter behind env var ([#29340](https://github.com/flutter/flutter/issues/29340))  * guard new formatter behind env var|
|rami-a <<EMAIL>>|[Material] Fix radio ink ripple to be centered ([#28845](https://github.com/flutter/flutter/issues/28845))|
|Jonah Williams <<EMAIL>>|add option for --verbose-system-logs ([#29321](https://github.com/flutter/flutter/issues/29321))|
|Dan Field <<EMAIL>>|Revert "Always composite PhysicalModels ([#28919](https://github.com/flutter/flutter/issues/28919))" ([#29314](https://github.com/flutter/flutter/issues/29314))  This reverts commit 58fb183298643bb0c57acd0b50b2480e9d1d5779.|
|Gary Qian <<EMAIL>>|Revert "Roll engine 31b289f277c6..b1b388f1c235 (7 commits)", Roll to 8b1a299ed instead. ([#29330](https://github.com/flutter/flutter/issues/29330))  * Revert "Roll engine 31b289f277c6..b1b388f1c235 (7 commits) ([#29323](https://github.com/flutter/flutter/issues/29323))"  This reverts commit 58ac12877d756804ad5f33e9f03c627fbde29b35.  Also rolls: 8b1a299ed [Skia] Rollback Skia to 29d5dec9a0783a033b921dc483fb98d565d684f6 ([flutter/engine#8151](https://github.com/flutter/engine/issues/8151)) 9f50dae33 Look up ICU symbols based on the path to libflutter.so as a fallback ([flutter/engine#8139](https://github.com/flutter/engine/issues/8139)) 59715b71d Disable build_ios task due to lack of credits. ([flutter/engine#8150](https://github.com/flutter/engine/issues/8150))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 31b289f277c6..b1b388f1c235 (7 commits) ([#29323](https://github.com/flutter/flutter/issues/29323))  * 59715b71d Disable build_ios task due to lack of credits. ([flutter/engine#8150](https://github.com/flutter/engine/issues/8150))  * 9f50dae33 Look up ICU symbols based on the path to libflutter.so as a fallback ([flutter/engine#8139](https://github.com/flutter/engine/issues/8139))  * 8b1a299ed [Skia] Rollback Skia to 29d5dec9a0783a033b921dc483fb98d565d684f6 ([flutter/engine#8151](https://github.com/flutter/engine/issues/8151))  * 8be2aca56 Roll src/third_party/dart 1bd36d694d..674fd0e060 (48 commits) [#8152](https://github.com/flutter/flutter/issues/8152)  * 2e427030f Revert "Disable build_ios task due to lack of credits. ([#8150](https://github.com/flutter/flutter/issues/8150))" ([flutter/engine#8153](https://github.com/flutter/engine/issues/8153))  * 2daebebf4 Fix text.dart height docs ([flutter/engine#8079](https://github.com/flutter/engine/issues/8079))  * b1b388f1c Encode scroll motion events in the Android touch processor ([flutter/engine#8149](https://github.com/flutter/engine/issues/8149))|
|Dan Field <<EMAIL>>|Revert "Speculative fix for [#29262](https://github.com/flutter/flutter/issues/29262) ([#29273](https://github.com/flutter/flutter/issues/29273))" ([#29319](https://github.com/flutter/flutter/issues/29319))  This reverts commit 53d45258dc5d30b886ed5f29251d5e7d70bc78e1.|
|Dan Field <<EMAIL>>|Speculative fix for [#29262](https://github.com/flutter/flutter/issues/29262) ([#29273](https://github.com/flutter/flutter/issues/29273))  * Avoid writing to stderr before process has finished streaming|
|Gary Qian <<EMAIL>>|Expose decorationThickness in TextStyle. Roll engine (12 commits) ([#28751](https://github.com/flutter/flutter/issues/28751))  31b289f27 Fix indexing error in dart:ui TextStyle.toString ([flutter/engine#8143](https://github.com/flutter/engine/issues/8143)) fc2e6b61c Typo "fast an inline" to "fast and inline" ([flutter/engine#8142](https://github.com/flutter/engine/issues/8142)) 0f19b2de0 Reland PerformanceOverlayLayer golden test ([flutter/engine#8140](https://github.com/flutter/engine/issues/8140)) 073aadde1 Fix TextStyle decode misalignment ([flutter/engine#8141](https://github.com/flutter/engine/issues/8141)) d87d29054 Roll src/third_party/skia 406b068942f0..2eecc3ea3d71 (11 commits) ([flutter/engine#8138](https://github.com/flutter/engine/issues/8138)) 5cef4a022 Use final state passed to dart before initialization as the initial lifecycleState. ([flutter/engine#8124](https://github.com/flutter/engine/issues/8124)) ffef51be6 Roll src/third_party/skia 665bc64a2dc4..406b068942f0 (8 commits) ([flutter/engine#8137](https://github.com/flutter/engine/issues/8137)) 48efd0fb7 Roll src/third_party/skia 762ddd7e4352..665bc64a2dc4 (2 commits) ([flutter/engine#8129](https://github.com/flutter/engine/issues/8129)) f666adbbd Roll src/third_party/skia 2932a458957d..762ddd7e4352 (3 commits) ([flutter/engine#8128](https://github.com/flutter/engine/issues/8128)) 8b0df6ded Bugfix [#29203](https://github.com/flutter/flutter/issues/29203): NPE in getAccessibilityProvider in old FlutterView. ([flutter/engine#8126](https://github.com/flutter/engine/issues/8126)) 8f7b18345 Roll src/third_party/skia c6d8781c4036..2932a458957d (2 commits) ([flutter/engine#8125](https://github.com/flutter/engine/issues/8125)) 52b67fdd5 Expose decorationThickness to dart:ui ([flutter/engine#8008](https://github.com/flutter/engine/issues/8008))|
|Jonah Williams <<EMAIL>>|Add skip to group in test_compat ([#29236](https://github.com/flutter/flutter/issues/29236))|
|sjindel-google <<EMAIL>>|Fix text selection handles showing outside the visible text region ([#24476](https://github.com/flutter/flutter/issues/24476))  Don't show handles outside the text field's boundary.|
|Michael Goderbauer <<EMAIL>>|Fix DartDoc for UniqueKey ([#29245](https://github.com/flutter/flutter/issues/29245))|
|Shi-Hao Hong <<EMAIL>>|Update README.md|
|Dan Field <<EMAIL>>|Always composite PhysicalModels ([#28919](https://github.com/flutter/flutter/issues/28919))  * Always composite PhysicalModels * Update tests to check layer tree instead of canvas for Physical shadows|
|Hans Muller <<EMAIL>>|Update flutter_localizations translations ([#29247](https://github.com/flutter/flutter/issues/29247))|
|guoskyhero <<EMAIL>>|Document that SearchDelegate.buildResults can be called multiple time… ([#26785](https://github.com/flutter/flutter/issues/26785))|
|Sean D'Souza <<EMAIL>>|Docs edit for Tab Label Color ([#29165](https://github.com/flutter/flutter/issues/29165))|
|liyuqian <<EMAIL>>|No image shader caching in default shader warm-up ([#29179](https://github.com/flutter/flutter/issues/29179))  * No image shader caching in default shader warm-up  It turns out that no matter how small the image is, too much memory will be used. Hence remove the image shader caching and let the client implement itself based on needs.  Fixes https://github.com/flutter/flutter/issues/29172  * Fix|
|Fedor Korotkov <<EMAIL>>|Install JDK and Android SDK only for integration tests ([#29229](https://github.com/flutter/flutter/issues/29229))  * Install JDK and Android SDK only for integration and build tests|
|Hans Muller <<EMAIL>>|Ensure that animated pairs of Tabs TextStyles have matching inherited values ([#29175](https://github.com/flutter/flutter/issues/29175))|
|Michael Goderbauer <<EMAIL>>|Heroes and nested Navigators ([#29069](https://github.com/flutter/flutter/issues/29069))|
|Hans Muller <<EMAIL>>|MaterialButton shape should override ButtonTheme shape ([#29189](https://github.com/flutter/flutter/issues/29189))|
|Kate Lovett <<EMAIL>>|Update to ListView Sample Code in API Docs ([#29072](https://github.com/flutter/flutter/issues/29072))  * Updated ListView Sample code with more examples for different constructors and also to match asset diagrams.  * Fixed MIA semicolons.  * Code cleanup.  * Added context for ListView.builder example.  * Analyzer does not like const and static usages.  * Replaced the const declarations with final. The analyzer does not like the use of const here, at all.  * Fixed parameterized declarations.|
|Michael Goderbauer <<EMAIL>>|Include platformViewId in semantics tree ([#28953](https://github.com/flutter/flutter/issues/28953))|
|Danny Tuppeny <<EMAIL>>|Add a `flutter create --list-samples` command ([#28938](https://github.com/flutter/flutter/issues/28938))  * Add a `flutter create --list-samples` command  * Add some comments/docs  * Tweak text|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 56d8634c51ff..5ccee9537334 (4 commits) ([#29198](https://github.com/flutter/flutter/issues/29198))   https://github.com/flutter/engine/compare/56d8634c51ff...5ccee9537334  git log 56d8634c51ff50534b1853b71205eff861131dcf..5ccee95373348854bc4877cfe240024a36847d2d --no-merges --oneline 5ccee9537 Roll src/third_party/skia e9c1ce89c005..c6d8781c4036 (29 commits) ([flutter/engine#8123](https://github.com/flutter/engine/issues/8123)) 20165b314 Integrated AndroidTouchProcessor within the old FlutterView ([flutter/engine#8095](https://github.com/flutter/engine/issues/8095)) bc901324f Revert &[#34](https://github.com/flutter/flutter/issues/34);Add support for trace counters with variable arguments and instrument the raster cache. ([#8094](https://github.com/flutter/flutter/issues/8094))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8122](https://github.com/flutter/engine/issues/8122)) 2a0d35428 Add support for trace counters with variable arguments and instrument the raster cache. ([flutter/engine#8094](https://github.com/flutter/engine/issues/8094))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine a71609f0695c..56d8634c51ff (2 commits) ([#29185](https://github.com/flutter/flutter/issues/29185))   https://github.com/flutter/engine/compare/a71609f0695c...56d8634c51ff  git log a71609f0695c2977ac96b7a79e67e24767abfda2..56d8634c51ff50534b1853b71205eff861131dcf --no-merges --oneline 56d8634c5 Improve shadow doc in PhysicalShapeLayer ([flutter/engine#8114](https://github.com/flutter/engine/issues/8114)) bd0f9085e Adds a platfromViewId to SemanticsNode ([flutter/engine#8055](https://github.com/flutter/engine/issues/8055))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dwayne Slater <<EMAIL>>|Fix MouseTracker annotation leak ([#28990](https://github.com/flutter/flutter/issues/28990))  * Fix MouseTracker annotation leak  Map's remove method is typed `remove(Object key)`, which can cause bugs.  Modified the existing test to check if the annotation has been removed from MouseTracker.|
|Michael Goderbauer <<EMAIL>>|Deprecate profile() ([#29054](https://github.com/flutter/flutter/issues/29054))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0d2cf5857b2b..a71609f0695c (3 commits) ([#29166](https://github.com/flutter/flutter/issues/29166))   https://github.com/flutter/engine/compare/0d2cf5857b2b...a71609f0695c  git log 0d2cf5857b2be0949435f60c0188f1aed4d93c93..a71609f0695c2977ac96b7a79e67e24767abfda2 --no-merges --oneline a71609f06 Roll src/third_party/skia bd050b90f8c7..e9c1ce89c005 (8 commits) ([flutter/engine#8113](https://github.com/flutter/engine/issues/8113)) 35e3b2b86 Roll src/third_party/skia 92ae09900456..bd050b90f8c7 (7 commits) ([flutter/engine#8111](https://github.com/flutter/engine/issues/8111)) 04fe0078f Roll src/third_party/dart 2fb6cd9f5f..674fd0e060 (58 commits) ([flutter/engine#8110](https://github.com/flutter/engine/issues/8110))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Aawaz Gyawali <<EMAIL>>|Changed flutter.dev to flutter.io on each link. ([#28888](https://github.com/flutter/flutter/issues/28888))|
|stuartmorgan <<EMAIL>>|Add support for scrollwheels ([#22762](https://github.com/flutter/flutter/issues/22762))  Adds support for discrete scroll events, such as those sent by a scroll wheel.  Includes the plumbing to convert, dispatch, and handle these events, as well as Scrollable support for consuming them.|
|Paul Berry <<EMAIL>>|Cause `flutter analyze` to fail if the analysis server experienced an error. ([#29126](https://github.com/flutter/flutter/issues/29126))  Substantially reduces the danger that a bug in the analysis server might prevent errors from being detected by `flutter analyze`.|
|Hiroki Matsue <<EMAIL>>|Fix typo in RefreshIndicator constructor API doc|
|Kate Lovett <<EMAIL>>|Update to Container Sample Code in API Docs ([#29053](https://github.com/flutter/flutter/issues/29053))  * Updating sample code for container to match forthcoming diagrams in assets-for-api-docs.  * Analyzer unhappy with Colors, fixing.  * Resolving push conflict (hopefully)  * Analyzer fix.  * Corrected color to match brand gallery  * Fixing wrap and indentation.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 14d1584a2262..0d2cf5857b2b (2 commits) ([#29132](https://github.com/flutter/flutter/issues/29132))   https://github.com/flutter/engine/compare/14d1584a2262...0d2cf5857b2b  git log 14d1584a22627e9a1eefc0e5f3de78caffcde09e..0d2cf5857b2be0949435f60c0188f1aed4d93c93 --no-merges --oneline 0d2cf5857 Fix the Windows build ([flutter/engine#8106](https://github.com/flutter/engine/issues/8106)) a28b53007 Log non-kSuccess returns from embedder API calls. ([flutter/engine#8096](https://github.com/flutter/engine/issues/8096))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Make sure test reporter prints out stderr, and disables Bigquery for non-contributors ([#29073](https://github.com/flutter/flutter/issues/29073))  * print stderr to stderr, no bq if not contributor  * let test continue of bigquery fails|
|Dan Field <<EMAIL>>|Update .cirrus.yml ([#29129](https://github.com/flutter/flutter/issues/29129))  I inadvertently commented this out in my previous PR around this.|
|Dan Field <<EMAIL>>|Run non-perf sensitive tests on Cirrus ([#27971](https://github.com/flutter/flutter/issues/27971))  * Run non-perf sensisitive tests on Cirrus|
|engine-flutter-autoroll <<EMAIL>>|14d1584a2 Roll src/third_party/skia 29d5dec9a078..92ae09900456 (1 commits) ([flutter/engine#8104](https://github.com/flutter/engine/issues/8104)) ([#29120](https://github.com/flutter/flutter/issues/29120))   https://github.com/flutter/engine/compare/4e54bc93ca9a...14d1584a2262  git log 4e54bc93ca9aaf2156fb06266c9fe509e8599a5f..14d1584a22627e9a1eefc0e5f3de78caffcde09e --no-merges --oneline 14d1584a2 Roll src/third_party/skia 29d5dec9a078..92ae09900456 (1 commits) ([flutter/engine#8104](https://github.com/flutter/engine/issues/8104))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|4e54bc93c Guard initialization of touch exploration listener ([flutter/engine#8103](https://github.com/flutter/engine/issues/8103)) ([#29117](https://github.com/flutter/flutter/issues/29117))   https://github.com/flutter/engine/compare/9e8f8d0889ec...4e54bc93ca9a  git log 9e8f8d0889ec04b54def0bf917321a27006623b0..4e54bc93ca9aaf2156fb06266c9fe509e8599a5f --no-merges --oneline 4e54bc93c Guard initialization of touch exploration listener ([flutter/engine#8103](https://github.com/flutter/engine/issues/8103))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|9e8f8d088 Drop android_sdk_downloader in favor of cipd ([flutter/engine#8087](https://github.com/flutter/engine/issues/8087)) ([#29115](https://github.com/flutter/flutter/issues/29115))   https://github.com/flutter/engine/compare/21342863aa60...9e8f8d0889ec  git log 21342863aa6018af53c09af2a3e7aa432a6cc844..9e8f8d0889ec04b54def0bf917321a27006623b0 --no-merges --oneline 9e8f8d088 Drop android_sdk_downloader in favor of cipd ([flutter/engine#8087](https://github.com/flutter/engine/issues/8087))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|21342863a Do not cache gclient sync ([flutter/engine#8098](https://github.com/flutter/engine/issues/8098)) ([#29111](https://github.com/flutter/flutter/issues/29111))   https://github.com/flutter/engine/compare/dc216bdcf0b3...21342863aa60  git log dc216bdcf0b3fbdf5a6ed2ccffa0e33f82c927c1..21342863aa6018af53c09af2a3e7aa432a6cc844 --no-merges --oneline 21342863a Do not cache gclient sync ([flutter/engine#8098](https://github.com/flutter/engine/issues/8098))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|dc216bdcf Fix typo ([flutter/engine#8102](https://github.com/flutter/engine/issues/8102)) ([#29107](https://github.com/flutter/flutter/issues/29107))   https://github.com/flutter/engine/compare/124fb1a054cd...dc216bdcf0b3  git log 124fb1a054cdb9d4338b4b7602e795699dbc0108..dc216bdcf0b3fbdf5a6ed2ccffa0e33f82c927c1 --no-merges --oneline dc216bdcf Fix typo ([flutter/engine#8102](https://github.com/flutter/engine/issues/8102))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine e77337bb1c74..124fb1a054cd (2 commits) ([#29104](https://github.com/flutter/flutter/issues/29104))   https://github.com/flutter/engine/compare/e77337bb1c74...124fb1a054cd  git log e77337bb1c74bf0595513cc71072b717f8977251..124fb1a054cdb9d4338b4b7602e795699dbc0108 --no-merges --oneline 124fb1a05 Roll src/third_party/skia b45e5f1e810a..29d5dec9a078 (1 commits) ([flutter/engine#8101](https://github.com/flutter/engine/issues/8101)) 76ad08e7d Roll src/third_party/skia a009d582e812..b45e5f1e810a (2 commits) ([flutter/engine#8100](https://github.com/flutter/engine/issues/8100))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|e77337bb1 Use right stream for Java, on mac try to autoselect Java 1.8 ([flutter/engine#8099](https://github.com/flutter/engine/issues/8099)) ([#29094](https://github.com/flutter/flutter/issues/29094))   https://github.com/flutter/engine/compare/a63ff4632728...e77337bb1c74  git log a63ff4632728c6386977dac95699021ee6018449..e77337bb1c74bf0595513cc71072b717f8977251 --no-merges --oneline e77337bb1 Use right stream for Java, on mac try to autoselect Java 1.8 ([flutter/engine#8099](https://github.com/flutter/engine/issues/8099))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 03d5f3c2d1e0..a63ff4632728 (2 commits) ([#29091](https://github.com/flutter/flutter/issues/29091))   https://github.com/flutter/engine/compare/03d5f3c2d1e0...a63ff4632728  git log 03d5f3c2d1e06e3949232d01233224fe18c196fb..a63ff4632728c6386977dac95699021ee6018449 --no-merges --oneline a63ff4632 Android linter prints to the console by default ([flutter/engine#8090](https://github.com/flutter/engine/issues/8090)) 24d990294 Roll src/third_party/skia bea1f94f341e..a009d582e812 (18 commits) ([flutter/engine#8097](https://github.com/flutter/engine/issues/8097))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Todd Volkert <<EMAIL>>|Revert "Fix TextField height issues ([#27205](https://github.com/flutter/flutter/issues/27205))" ([#29093](https://github.com/flutter/flutter/issues/29093))  This reverts commit 9e9f48dabb8423a6285bddfb21e69f0751060459.|
|Alexandre Ardhuin <<EMAIL>>|fix block formatting ([#29051](https://github.com/flutter/flutter/issues/29051))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine c48774cfe09b..03d5f3c2d1e0 (3 commits) ([#29082](https://github.com/flutter/flutter/issues/29082))   https://github.com/flutter/engine/compare/c48774cfe09b...03d5f3c2d1e0  git log c48774cfe09b7f286defb9e457a74c7e7028e11c..03d5f3c2d1e06e3949232d01233224fe18c196fb --no-merges --oneline 03d5f3c2d Clarify arguments to FlutterEngineOnVsync ([flutter/engine#8093](https://github.com/flutter/engine/issues/8093)) 718329ce6 Android Embedding PR 17: Clarify AccessibilityBridge and move logic out of FlutterView. ([flutter/engine#8061](https://github.com/flutter/engine/issues/8061)) edfc0cfaa Android Embedding PR 16: Add touch support to FlutterView. ([flutter/engine#8034](https://github.com/flutter/engine/issues/8034))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|fix windows codegen ([#29062](https://github.com/flutter/flutter/issues/29062))|
|Ian Hickson <<EMAIL>>|Support hotfix version numbers ([#28672](https://github.com/flutter/flutter/issues/28672))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 87edd94e9cc7..c48774cfe09b (3 commits) ([#29079](https://github.com/flutter/flutter/issues/29079))   https://github.com/flutter/engine/compare/87edd94e9cc7...c48774cfe09b  git log 87edd94e9cc751a0de72c0828d41793b9f740062..c48774cfe09b7f286defb9e457a74c7e7028e11c --no-merges --oneline c48774cfe Roll src/third_party/dart 571ea80e11..2fb6cd9f5f (122 commits) ([flutter/engine#8086](https://github.com/flutter/engine/issues/8086)) 3c8ef04e9 Allow embedders to post tasks onto the render thread. ([flutter/engine#8089](https://github.com/flutter/engine/issues/8089)) 1d10e0eb6 Guard against NewAPI failures ([flutter/engine#8048](https://github.com/flutter/engine/issues/8048))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 60314077846c..87edd94e9cc7 (4 commits) ([#29067](https://github.com/flutter/flutter/issues/29067))   https://github.com/flutter/engine/compare/60314077846c...87edd94e9cc7  git log 60314077846c123f45c5aaef911c5ea48343d569..87edd94e9cc751a0de72c0828d41793b9f740062 --no-merges --oneline 87edd94e9 Add read-only persistent cache ([flutter/engine#8049](https://github.com/flutter/engine/issues/8049)) 4c9404950 Move android_sdk_downloader so I can more easily deprecate it ([flutter/engine#8084](https://github.com/flutter/engine/issues/8084)) 7cbd9d8b4 Roll src/third_party/skia 3d1b941f3a7d..bea1f94f341e (7 commits) ([flutter/engine#8083](https://github.com/flutter/engine/issues/8083)) 260669c65 Fix deleting text when the last character is some special characters on IOS ([flutter/engine#7982](https://github.com/flutter/engine/issues/7982))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Terje Lønøy <<EMAIL>>|Add key support to cupertino button ([#28900](https://github.com/flutter/flutter/issues/28900))|
|xster <<EMAIL>>|Fix CupertinoTabView tree re-shape on view inset change ([#29024](https://github.com/flutter/flutter/issues/29024))|
|Dan Field <<EMAIL>>|add heartbeat ([#29056](https://github.com/flutter/flutter/issues/29056))|
|Yuqian Li <<EMAIL>>|Update docs|
|Jonah Williams <<EMAIL>>|Revert "Lazily download artifacts (III) ([#27903](https://github.com/flutter/flutter/issues/27903))" ([#29064](https://github.com/flutter/flutter/issues/29064))  This reverts commit 3fd36200d9b89322d75c1b6bad37b3ad7f665b81.|
|Jonah Williams <<EMAIL>>|Lazily download artifacts (III) ([#27903](https://github.com/flutter/flutter/issues/27903))|
|Jonah Williams <<EMAIL>>|Add integration to all targets ([#29020](https://github.com/flutter/flutter/issues/29020))|
|Justin McCandless <<EMAIL>>|Fix TextField height issues ([#27205](https://github.com/flutter/flutter/issues/27205))  * Create new TextField attribute to control maxLength behaviour  * Create test case for maxLinesIncrementalHeight  * fix maxLinesIncrementalHeight set method  * fix editable_test.dart  * Just introducing some proposed API additions, renaming to expands  * Pass minLines and expands through to child widgets and validate  * minLines can't be null, and expands can't be true when maxLines is 1  * Implement minLines and maxLines height sizing logic  * Simplify minLines validation logic  * expands parameter proof of concept  * min/max mixup bug fix, and tests work with expands false  * Test expands=true cases, and fix textPainter.height being out of date  * Test all behavior matrix cases  * min/max assertion more strict, can't be equal  * Tests work that were missing expands serialization  * Action sheet tests no longer fail due to rounding error  * TextFieldFocus test no longer mysteriously fails  * TODOs for making expands nullable. Will depend on how Expanded wrapping works  * Expanded growth happens when expanded is true and maxLines is null  * Test Expanded wrapper  * No more overflow when wrapped in Expanded  * Docs improvements  * expands can be null  * Simplify error cases to support existing behavior  * Docs examples and other docs cleanup  * Expansion up to perfectly meet the parent size  * Fix analyze null error  * Fix test after move to nullable expands  * minLines defaults to null  * expands is now exclusively for expanding to parent height and not growth between min and max  * _layout rewritten to handle max height. Need to fix prefix tests and reenable expands  * Tests for textfield overflowing parent  * layoutLineBox is documented and private  * expands works in new _layout  * _layout return numbers seem to perfectly match original _layout  * inputWidth comment after trying it out and failing tests  * Fix analyze errors  * WIP prefix/suffix do affect height  * Prefix/suffix and icons affect height, tests pass, but I'm still visually verifying identical to original  * Tall prefix test that verifies pixel perfect layout  * Fix overflowing edge case and test it  * Clean up comments, old code, and todos  * Changing _expands causes relayout. Wasnt able to figure out how to test though...  * Clean up code review comments  * Fix misalignment when tall prefix and border, and clean up related test  * Simple code review cleanup  * Bring back inputWidth to _layout method  * Fix rounding errors showing up in mac tests  * Fix flake by reordering tests. Without this, the dreaded intrinsicwidth flake is reproducible 50% of the time on my machine.  * Fix more rounding error mac tests|
|Greg Spencer <<EMAIL>>|Remove references to obsolete SemanticsSortOrder ([#29012](https://github.com/flutter/flutter/issues/29012))  This removes references to SemanticsSortOrder, which was removed a long time ago.|
|Greg Spencer <<EMAIL>>|Add/rewrite tests for FocusScope. ([#28169](https://github.com/flutter/flutter/issues/28169))  In anticipation of changing a lot of the focus code, I'm adding some tests for the FocusScope.  As a result, I was able to find and fix a bug where there was an incorrect assert.  I also added some more documentation.  Several of the tests enforce what I think is incorrect behavior related to passing focus off when the widget tree gets rebuilt without focus nodes that were previously there, but I'm not going to change that behavior in this PR.  I also renamed focus_test.dart to focus_scope_test.dart to be more in line with our naming conventions.|
|Dan Field <<EMAIL>>|update readme for LUCI ([#29023](https://github.com/flutter/flutter/issues/29023))  * update readme for LUCI  * Update README.md|
|engine-flutter-autoroll <<EMAIL>>|Roll engine a48cd16e0777..60314077846c (2 commits) ([#29049](https://github.com/flutter/flutter/issues/29049))   https://github.com/flutter/engine/compare/a48cd16e0777...60314077846c  git log a48cd16e077778a7aa5908e4faa4da07714260f2..60314077846c123f45c5aaef911c5ea48343d569 --no-merges --oneline 603140778 Roll src/third_party/skia 88b8d1124b72..3d1b941f3a7d (27 commits) ([flutter/engine#8082](https://github.com/flutter/engine/issues/8082)) 95ef58cf4 Add script to help generate PR messages ([flutter/engine#8074](https://github.com/flutter/engine/issues/8074))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|print system time on all mac builds ([#29025](https://github.com/flutter/flutter/issues/29025))|
|Jonah Williams <<EMAIL>>|Revert "re-enable dart2js test ([#29010](https://github.com/flutter/flutter/issues/29010))" ([#29030](https://github.com/flutter/flutter/issues/29030))  This reverts commit 6a7f231d89156272a5317141f1cde64fae93c9b4.|
|Jonah Williams <<EMAIL>>|re-enable dart2js test ([#29010](https://github.com/flutter/flutter/issues/29010))|
|Gary Qian <<EMAIL>>|Initialize the lifecycle state with initial state in window. Roll engine (19 commits) ([#28688](https://github.com/flutter/flutter/issues/28688))  git log --oneline --no-merges f4951df193a7966f9ed4da43d555eee0913d84d1..a48cd16e077778a7aa5908e4faa4da07714260f2 a48cd16e0 Update a11y word forward/back enum names (https://github.com/flutter/engine/pull/8073) b5f59ed89 Delay the vsync callback till the frame start time specified by embedder. (https://github.com/flutter/engine/pull/8072) 7426305f5 Mark const extern (https://github.com/flutter/engine/pull/8077) d3f6d7a21 only partial rule revert (https://github.com/flutter/engine/pull/8078) d71bfe58d Only build a full Dart SDK when building for the host system (https://github.com/flutter/engine/pull/8071) de90dbff7 Refactor web configuration/ Add dartdevc (https://github.com/flutter/engine/pull/7978) ff46dd38f Roll src/third_party/skia 4c1ea43a79b5..88b8d1124b72 (8 commits) (https://github.com/flutter/engine/pull/8070) 80c6dd286 Roll src/third_party/skia 692122e3ef23..4c1ea43a79b5 (3 commits) (https://github.com/flutter/engine/pull/8069) 68ed654ea Roll src/third_party/skia 3c957d575c58..692122e3ef23 (6 commits) (https://github.com/flutter/engine/pull/8067) ca0bac4fb Revert "add signal to pointer kinds" (https://github.com/flutter/engine/pull/8066) 3fb627f2c add signal to pointer kinds (https://github.com/flutter/engine/pull/8065) 5a06afa2a Roll src/third_party/skia 801a9c16d81e..3c957d575c58 (19 commits) (https://github.com/flutter/engine/pull/8063) a93d99db9 A11y callback (https://github.com/flutter/engine/pull/8005) 3661d5e43 Re-land "Buffer lifecycle in WindowData" (https://github.com/flutter/engine/pull/8032) 471a2c89a Send scroll events from the macOS shell (https://github.com/flutter/engine/pull/8056) 2fe9c9b5f Roll src/third_party/skia 72542816cadb..801a9c16d81e (46 commits) (https://github.com/flutter/engine/pull/8060) 3335764ae Skip skp files in license check (https://github.com/flutter/engine/pull/8050) 7f16789b2 Remove redundant thread checker in FML. (https://github.com/flutter/engine/pull/8053) 840c5233a Correct URL for Cirrus CI build status badge (https://github.com/flutter/engine/pull/8054) 57c120a29 remove extra source files (https://github.com/flutter/engine/pull/8052) 4773375c6 Used named conditionals for platform specific dependencies and suppress Android and Windows hooks on Mac. (https://github.com/flutter/engine/pull/8051) 70a18b515 Add clang static analysis support to gn wrapper (https://github.com/flutter/engine/pull/8047) b30f9897b Improve elevation bounds for physical shape layers (https://github.com/flutter/engine/pull/8044) e37bd27b4 Fix weak pointer use violations in shell and platform view. (https://github.com/flutter/engine/pull/8046) dd80fc9ff Add engine support for scrollwheel events (https://github.com/flutter/engine/pull/7494)|
|Dan Field <<EMAIL>>|make coverage work again ([#29016](https://github.com/flutter/flutter/issues/29016))|
|Jonah Williams <<EMAIL>>|Remove extra build_runner modes, remove flutter_build ([#28873](https://github.com/flutter/flutter/issues/28873))|
|Kate Lovett <<EMAIL>>|Form Snippet for API Docs ([#28857](https://github.com/flutter/flutter/issues/28857))  * Added code snippet for Form Class. ref:[#21136](https://github.com/flutter/flutter/issues/21136)  * Formatting adjustments.  * Changed wording related to GlobalKey.|
|xster <<EMAIL>>|Add more doc pointing to the EditableText's rudimentary nature around gesture handling ([#28955](https://github.com/flutter/flutter/issues/28955))|
|liyuqian <<EMAIL>>|Quick fix for shader warm up ([#28951](https://github.com/flutter/flutter/issues/28951))  Fix issues in https://github.com/flutter/flutter/pull/28687|
|Alexandre Ardhuin <<EMAIL>>|Fix indentations of statements in BlockFunctionBody ([#28933](https://github.com/flutter/flutter/issues/28933))  * fix indentof statements in BlockFunctionBody  * fix indentof statements in BlockFunctionBody in tests|
|Dan Field <<EMAIL>>|Fix coverage shard and print summary after test run ([#28970](https://github.com/flutter/flutter/issues/28970))  * Fix coverage shard  * clarify  * Make sure we print test results  * Actually report test results before exiting  * revert unintended changes|
|Jonah Williams <<EMAIL>>|select ResidentCompiler during FlutterDevice initialization ([#28603](https://github.com/flutter/flutter/issues/28603))|
|Dan Field <<EMAIL>>|Fall-back to platform tools in Android SDK detection logic. ([#28863](https://github.com/flutter/flutter/issues/28863))|
|Jonah Williams <<EMAIL>>|Revert "Roll engine f4951df193a7..471a2c89a689 (11 commits) ([#28966](https://github.com/flutter/flutter/issues/28966))" ([#28975](https://github.com/flutter/flutter/issues/28975))  This reverts commit 1b14339e6ab1365dd8c3104f70cf40bc45f48586.|
|Tong Mu <<EMAIL>>|Add minimum time gap requirement to double tap ([#28749](https://github.com/flutter/flutter/issues/28749))  * First impl with StopwatchWithZone  * Clean up params and name  * Remove outdated TODO  * Fix style  * Fix a missing param. Add @require  * Fix import meta  * Fix code style  * Add missing require. Fix comment style.  * Fix code style  * Fix code style|
|engine-flutter-autoroll <<EMAIL>>|Roll engine f4951df193a7..471a2c89a689 (11 commits) ([#28966](https://github.com/flutter/flutter/issues/28966))   https://github.com/flutter/engine/compare/f4951df193a7...471a2c89a689  git log f4951df193a7966f9ed4da43d555eee0913d84d1..471a2c89a689ff1d5c70a5deb4565f646380bce6 --no-merges --oneline 471a2c89a Send scroll events from the macOS shell ([flutter/engine#8056](https://github.com/flutter/engine/issues/8056)) 2fe9c9b5f Roll src/third_party/skia 72542816cadb..801a9c16d81e (46 commits) ([flutter/engine#8060](https://github.com/flutter/engine/issues/8060)) 3335764ae Skip skp files in license check ([flutter/engine#8050](https://github.com/flutter/engine/issues/8050)) 7f16789b2 Remove redundant thread checker in FML. ([flutter/engine#8053](https://github.com/flutter/engine/issues/8053)) 840c5233a Correct URL for Cirrus CI build status badge ([flutter/engine#8054](https://github.com/flutter/engine/issues/8054)) 57c120a29 remove extra source files ([flutter/engine#8052](https://github.com/flutter/engine/issues/8052)) 4773375c6 Used named conditionals for platform specific dependencies and suppress Android and Windows hooks on Mac. ([flutter/engine#8051](https://github.com/flutter/engine/issues/8051)) 70a18b515 Add clang static analysis support to gn wrapper ([flutter/engine#8047](https://github.com/flutter/engine/issues/8047)) b30f9897b Improve elevation bounds for physical shape layers ([flutter/engine#8044](https://github.com/flutter/engine/issues/8044)) e37bd27b4 Fix weak pointer use violations in shell and platform view. ([flutter/engine#8046](https://github.com/flutter/engine/issues/8046)) dd80fc9ff Add engine support for scrollwheel events ([flutter/engine#7494](https://github.com/flutter/engine/issues/7494))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|xster <<EMAIL>>|Move material iOS back swipe test to material ([#28855](https://github.com/flutter/flutter/issues/28855))|
|Muhammed Salih Guler <<EMAIL>>|Add semantics label to FadeInImage. ([#28799](https://github.com/flutter/flutter/issues/28799))|
|TruongSinh Tran-Nguyen <<EMAIL>>|fix [#19175](https://github.com/flutter/flutter/issues/19175) How should addTime be used from a test? ([#25202](https://github.com/flutter/flutter/issues/25202))|
|Ivan <<EMAIL>>|fix missing variable name ([#28944](https://github.com/flutter/flutter/issues/28944))|
|Jonah Williams <<EMAIL>>|Add build script invalidation and snapshotting logic ([#28866](https://github.com/flutter/flutter/issues/28866))|
|Dan Field <<EMAIL>>|Fix for post submit on cirrus ([#28963](https://github.com/flutter/flutter/issues/28963))  * Fix for post submit on cirrus|
|Dan Field <<EMAIL>>|Test reporter ([#28297](https://github.com/flutter/flutter/issues/28297))  * Wrap test.main with a custom processor * Report test results to bigquery table|
|Jason Simmons <<EMAIL>>|Remove the flutter_shared assets directory from the Gradle script ([#27572](https://github.com/flutter/flutter/issues/27572))  flutter_shared was used to package the icudtl.dat file, but that data is now embedded inside libflutter.so|
|Phil Quitslund <<EMAIL>>|set literal conversions ([#27811](https://github.com/flutter/flutter/issues/27811))|
|Dan Field <<EMAIL>>|Update docs for ancestorWidgetOfExactType ([#28675](https://github.com/flutter/flutter/issues/28675))  * doc update|
|Reece Dunham <<EMAIL>>|Update macOS version in tests ([#28852](https://github.com/flutter/flutter/issues/28852))  This switches the Cirrus macOS images to use Mojave, since Cirrus has deprecated the High Sierra images.|
|Chris Bracken <<EMAIL>>|Fix crash on flutter update-packages ([#28922](https://github.com/flutter/flutter/issues/28922))  When running git update-packages, if the goldens repo is dirty, the flutter tool currently crashes and logs the failure from git pull.  This adds a check for a dirty git client and emits a friendlier error message in the case where it's not clean, and avoid the crash by catching the NonZeroExitCode exception and rethrowing as toolExit instead.  Fixes: https://github.com/flutter/flutter/issues/28915|
|Alexandre Ardhuin <<EMAIL>>|fix some formatting issues ([#28809](https://github.com/flutter/flutter/issues/28809))  * fix some formatting issues  * address review comments  * fix indent|
|Chris Bracken <<EMAIL>>|Roll engine 4f54a1d...2e97618 ([#28899](https://github.com/flutter/flutter/issues/28899))  flutter/engine@f4951df19 (upstream/master) Minor cleanups to CONTRIBUTING.md ([flutter/engine#8043](https://github.com/flutter/engine/issues/8043)) flutter/engine@effee2f80 remove extra statement ([flutter/engine#8041](https://github.com/flutter/engine/issues/8041)) flutter/engine@14e082fa0 (master) add lint script for Android with baseline ([flutter/engine#8036](https://github.com/flutter/engine/issues/8036)) flutter/engine@5fed42197 Roll src/third_party/skia 25bc174cf682..72542816cadb (14 commits) ([flutter/engine#8040](https://github.com/flutter/engine/issues/8040)) flutter/engine@e6a5201f0 Add missing values to semantics action enums ([flutter/engine#8033](https://github.com/flutter/engine/issues/8033)) flutter/engine@75d4db31d Roll src/third_party/skia fbc887df72ec..25bc174cf682 (7 commits) ([flutter/engine#8039](https://github.com/flutter/engine/issues/8039)) flutter/engine@c46226980 Roll src/third_party/skia 0a57971f0544..fbc887df72ec (1 commits) ([flutter/engine#8037](https://github.com/flutter/engine/issues/8037)) flutter/engine@ed628da00 Add missing kHasImplicitScrolling enum value ([flutter/engine#8030](https://github.com/flutter/engine/issues/8030)) flutter/engine@052774e1c Roll src/third_party/skia e3e80b7edfd1..0a57971f0544 (16 commits) ([flutter/engine#8035](https://github.com/flutter/engine/issues/8035)) flutter/engine@2cd9b41ba Select MPL instead of LGPL ([flutter/engine#7991](https://github.com/flutter/engine/issues/7991)) flutter/engine@629c0d836 Roll src/third_party/dart 7d8ffe0daf..571ea80e11 (3 commits) flutter/engine@8f1fdcd19 Android Embedding PR 14: Almost done with FlutterFragment. ([flutter/engine#8000](https://github.com/flutter/engine/issues/8000)) flutter/engine@fb3e35d6a Android Embedding PR15: Add Viewport Metrics to FlutterView ([flutter/engine#8029](https://github.com/flutter/engine/issues/8029)) flutter/engine@36ca5740c ([flutter/engine#8026](https://github.com/flutter/engine/issues/8026)) flutter/engine@3d53e2026 Roll src/third_party/skia 5800f2e8a920..e3e80b7edfd1 (2 commits) ([flutter/engine#8028](https://github.com/flutter/engine/issues/8028)) flutter/engine@f3d4a7f71 Roll src/third_party/dart 7c70ab1817..7d8ffe0daf (77 commits) flutter/engine@a2246c199 Start of linting Android embedding ([flutter/engine#8023](https://github.com/flutter/engine/issues/8023)) flutter/engine@48bf4803e [fuchsia] Fix snapshot BUILD.gn file for Fuchsia roll ([flutter/engine#8024](https://github.com/flutter/engine/issues/8024))|
|Amir Hardon <<EMAIL>>|Make RenderUiKitView reject absorbed touch events ([#28666](https://github.com/flutter/flutter/issues/28666))  When a touch event that is in the bounds of a RenderUiKitView is absorbed by another render object, the RenderUiKitView's handleEvent is not called for that object. On the platform side, the touch event hits the FlutterTouchInterceptingView which is waiting for a framework decision that never arrived on whether to reject or accept the gesture.  This change fixes the issue by having RenderUiKitView register a global PointerRoute, that is used to reject absorbed touch events.|
|Kate Lovett <<EMAIL>>|FAB Snippet for API Docs ([#28752](https://github.com/flutter/flutter/issues/28752))  * Added code snippets for FloatingActionButton Class. ref:[#21136](https://github.com/flutter/flutter/issues/21136)  * Extraneous white spaces.  Co-Authored-By: Piinks <<EMAIL>>  * Alignment correction.  * The analyze result wasn't showing on github interface. Checked through Cirrus and it's all-green.  Co-Authored-By: Piinks <<EMAIL>>|
|Hans Muller <<EMAIL>>|Handle Cupertino back gesture interrupted by Navigator push ([#28756](https://github.com/flutter/flutter/issues/28756))|
|Jason Simmons <<EMAIL>>|Use a simpler implementation of Diagnosticable.toString when running in profile/release mode ([#28661](https://github.com/flutter/flutter/issues/28661))  Diagnosticable.toString may be called in performance sensitive contexts (for example, creating debug labels for Tickers)|
|Jason Simmons <<EMAIL>>|Avoid the overhead of instantiating a generator in paintImage ([#28736](https://github.com/flutter/flutter/issues/28736))  This function is called frequently with non-repeating images during many animations|
|Joshua Seaton <<EMAIL>>|[fuchsia] Permit relative entries to the fuchsia_tester ([#28821](https://github.com/flutter/flutter/issues/28821))  This change permits (1) a relative --flutter-shell (2) relative (source, dill) entries within the test JSON file  and enables portability in the invocation scripts produced by fuchsia's dart test GN template.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine e9eae9a1af77..4f54a1dd9444 (3 commits) ([#28843](https://github.com/flutter/flutter/issues/28843))   https://github.com/flutter/engine/compare/e9eae9a1af77...4f54a1dd9444  git log e9eae9a1af77400d02c498f0ad943f2858845398..4f54a1dd944497bd1ec0f2acca22ff9fbd8b377a --no-merges --oneline 4f54a1dd9 Roll src/third_party/skia c1a9feb6f06e..5800f2e8a920 (2 commits) ([flutter/engine#8022](https://github.com/flutter/engine/issues/8022)) 75e2b4702 Roll src/third_party/skia c6671be0ea75..c1a9feb6f06e (2 commits) ([flutter/engine#8021](https://github.com/flutter/engine/issues/8021)) b44a9305c Roll src/third_party/skia 1b9f889628e0..c6671be0ea75 (1 commits) ([flutter/engine#8020](https://github.com/flutter/engine/issues/8020))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|e9eae9a1a Roll src/third_party/skia c97b0672a73f..1b9f889628e0 (1 commits) ([flutter/engine#8019](https://github.com/flutter/engine/issues/8019)) ([#28801](https://github.com/flutter/flutter/issues/28801))   https://github.com/flutter/engine/compare/a28329cb91ac...e9eae9a1af77  git log a28329cb91ac18ad4d7950b3b170a74a8de979ee..e9eae9a1af77400d02c498f0ad943f2858845398 --no-merges --oneline e9eae9a1a Roll src/third_party/skia c97b0672a73f..1b9f889628e0 (1 commits) ([flutter/engine#8019](https://github.com/flutter/engine/issues/8019))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 39c46dea4b73..a28329cb91ac (10 commits) ([#28797](https://github.com/flutter/flutter/issues/28797))   https://github.com/flutter/engine/compare/39c46dea4b73...a28329cb91ac  git log 39c46dea4b7347a252218aaff5caac5dc2fec8cd..a28329cb91ac18ad4d7950b3b170a74a8de979ee --no-merges --oneline a28329cb9 Roll src/third_party/skia 58a75383a70b..c97b0672a73f (1 commits) ([flutter/engine#8018](https://github.com/flutter/engine/issues/8018)) cdc141072 Roll src/third_party/skia 0d7012a80c17..58a75383a70b (1 commits) ([flutter/engine#8017](https://github.com/flutter/engine/issues/8017)) 01d8a1f27 Roll src/third_party/skia c945f0e31a53..0d7012a80c17 (1 commits) ([flutter/engine#8016](https://github.com/flutter/engine/issues/8016)) 75cd4df37 Roll src/third_party/skia 9159c8ef83b2..c945f0e31a53 (1 commits) ([flutter/engine#8015](https://github.com/flutter/engine/issues/8015)) 13064aec8 Add overloads for lookup that lets you specify a bundle ([flutter/engine#8007](https://github.com/flutter/engine/issues/8007)) 130cf77c5 Build engine for iOS on presubmit ([flutter/engine#8011](https://github.com/flutter/engine/issues/8011)) 7234cc463 Roll src/third_party/skia 705e67347962..9159c8ef83b2 (1 commits) ([flutter/engine#8014](https://github.com/flutter/engine/issues/8014)) 8a0b054a4 Roll src/third_party/skia 09198525261a..705e67347962 (7 commits) ([flutter/engine#8013](https://github.com/flutter/engine/issues/8013)) 36cdd2ee6 Roll src/third_party/skia 436d47d5bde9..09198525261a (6 commits) ([flutter/engine#8012](https://github.com/flutter/engine/issues/8012)) 9bef73a1a Roll src/third_party/skia e1f5b644656f..436d47d5bde9 (3 commits) ([flutter/engine#8009](https://github.com/flutter/engine/issues/8009))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Danny Tuppeny <<EMAIL>>|Fix the test annotation used for test groups ([#28638](https://github.com/flutter/flutter/issues/28638))|
|Jonah Williams <<EMAIL>>|switch tool tests to build runner ([#28759](https://github.com/flutter/flutter/issues/28759))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 66042161eba1..39c46dea4b73 (4 commits) ([#28755](https://github.com/flutter/flutter/issues/28755))   https://github.com/flutter/engine/compare/66042161eba1...39c46dea4b73  git log 66042161eba17da968800c57ddd2c314cd787abb..39c46dea4b7347a252218aaff5caac5dc2fec8cd --no-merges --oneline 39c46dea4 Revert &[#34](https://github.com/flutter/flutter/issues/34);Buffer lifecycle in WindowData ([#7999](https://github.com/flutter/flutter/issues/7999))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#8010](https://github.com/flutter/engine/issues/8010)) 293cfcaa5 Guard against using Android API not defined in API level 16 &amp; 17 ([flutter/engine#8006](https://github.com/flutter/engine/issues/8006)) d7e0bc10c Roll src/third_party/skia 71a23d4195d9..e1f5b644656f (8 commits) ([flutter/engine#8004](https://github.com/flutter/engine/issues/8004)) 0b1740171 Buffer lifecycle in WindowData ([flutter/engine#7999](https://github.com/flutter/engine/issues/7999))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Kate Lovett <<EMAIL>>|Expanded Snippet for API Docs ([#28681](https://github.com/flutter/flutter/issues/28681))  * Added code snippet for Expanded class. ref:[#21136](https://github.com/flutter/flutter/issues/21136)  * Changed the code samples into snippet application samples.|
|Jonah Williams <<EMAIL>>|Revert "Remove the old HaTS implementation on API docs" ([#28748](https://github.com/flutter/flutter/issues/28748))|
|Dan Field <<EMAIL>>|Update README.md ([#28734](https://github.com/flutter/flutter/issues/28734))  @xster|
|Tong Mu <<EMAIL>>|Add animation curve slowMiddle ([#28540](https://github.com/flutter/flutter/issues/28540))  * Add curve fastInFastOut  * Rename it to slowCenter  * Rename to slowMiddle  * Add continuous test|
|Greg Spencer <<EMAIL>>|Be more strict about finding version number attached to a revision. ([#28527](https://github.com/flutter/flutter/issues/28527))  When we package Flutter, we used to find the "current" tag (which is the version number) by starting at the revision we are building on and looking backwards in time to find the most recent tag. This causes problems on release builds when we failed to tag properly.  This PR makes the packaging script be more strict by requiring the given revision to itself have a tag, but only when we're publishing the result. When we're not publishing the result, it's more lenient, since otherwise we couldn't test packaging on non-release commits.  I also renamed ProcessRunnerException to PreparePackageException, since we were using that exception more generally than just for processes.|
|Ja Young Lee <<EMAIL>>|Remove the old HaTS implementation on API docs ([#28683](https://github.com/flutter/flutter/issues/28683))|
|Mouad Debbar <<EMAIL>>|Text selection via mouse ([#28290](https://github.com/flutter/flutter/issues/28290))|
|ocavue <<EMAIL>>|Fix ink highlight effect of RawChip ([#28653](https://github.com/flutter/flutter/issues/28653))|
|Alexander Markov <<EMAIL>>|Include git output into error message from channel command ([#28658](https://github.com/flutter/flutter/issues/28658))|
|Dan Field <<EMAIL>>|improve error messages on Text constructors ([#28709](https://github.com/flutter/flutter/issues/28709))|
|engine-flutter-autoroll <<EMAIL>>|66042161e Roll src/third_party/skia 255569187f27..71a23d4195d9 (1 commits) ([flutter/engine#8003](https://github.com/flutter/engine/issues/8003)) ([#28731](https://github.com/flutter/flutter/issues/28731))   https://github.com/flutter/engine/compare/99f3f7a9c246...66042161eba1  git log 99f3f7a9c246f1ebedc6eefd867cde250b370380..66042161eba17da968800c57ddd2c314cd787abb --no-merges --oneline 66042161e Roll src/third_party/skia 255569187f27..71a23d4195d9 (1 commits) ([flutter/engine#8003](https://github.com/flutter/engine/issues/8003))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Hans Muller <<EMAIL>>|Update TabController.indexIsChanging doc ([#28663](https://github.com/flutter/flutter/issues/28663))|
|Chris Bracken <<EMAIL>>|Log pub return code on failure on Windows ([#28356](https://github.com/flutter/flutter/issues/28356))  When pub fails, log the %ERRRORLEVEL% to help diagnose the root cause of the pub failure.|
|Alexandre Ardhuin <<EMAIL>>|Add missing trailing commas ([#28673](https://github.com/flutter/flutter/issues/28673))  * add trailing commas on list/map/parameters  * add trailing commas on Invocation with nb of arg>1  * add commas for widget containing widgets  * add trailing commas if instantiation contains trailing comma  * revert bad change|
|Chris Bracken <<EMAIL>>|Roll engine to 99f3f7a9c246f1ebedc6eefd867cde250b370380 ([#28698](https://github.com/flutter/flutter/issues/28698))  flutter/engine@99f3f7a9c Fix incorrect transformation matrix ([flutter/engine#8001](https://github.com/flutter/engine/issues/8001)) flutter/engine@c88b09710 Roll src/third_party/skia b7b2da871e95..255569187f27 (23 commits) ([flutter/engine#8002](https://github.com/flutter/engine/issues/8002)) flutter/engine@302e2e9d2 Fix cursor jumping when typing some special characters. ([flutter/engine#7964](https://github.com/flutter/engine/issues/7964)) flutter/engine@fe15149d1 Android Embedding PR 12: Add lifecycle methods to FlutterActivity. ([flutter/engine#7974](https://github.com/flutter/engine/issues/7974)) flutter/engine@6145e9046 Android Embedding PR 13: Integrated text input, keyevent input, and some other channel comms in FlutterView. ([flutter/engine#7979](https://github.com/flutter/engine/issues/7979))|
|liyuqian <<EMAIL>>|Make shader warm-up async so it can handle image ([#28687](https://github.com/flutter/flutter/issues/28687))  ## Description  This moves another 15-20ms from the animation jank of one of our important client to the startup latency. Unfortunately, this is probably not captured in our current benchmarks (presumably some other bottlenecks overshadow this shader compilation in the worst_frame benchmark). Considering that drawing images is such a common operation, maybe we should add one in the future to benchmark this.  We need this PR to land soon for our client because this changes the API to return Future.  ## Related Issues  https://github.com/flutter/flutter/issues/813|
|liyuqian <<EMAIL>>|Shader warm-up doc fixes ([#28679](https://github.com/flutter/flutter/issues/28679))  See https://github.com/flutter/flutter/pull/28614#discussion_r261422778 and https://github.com/flutter/flutter/pull/28614#pullrequestreview-209359908|
|Jonah Williams <<EMAIL>>|Add capability to run build_runner tests for engine ([#28684](https://github.com/flutter/flutter/issues/28684))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5194919e1309..56c16154eada (3 commits) ([#28686](https://github.com/flutter/flutter/issues/28686))   https://github.com/flutter/engine/compare/5194919e1309...56c16154eada  git log 5194919e130917d29ac15f0ac7f52ea94d1a4d67..56c16154eadada5f77c57ddca39a86c5e30c5768 --no-merges --oneline 56c16154e Android Embedding PR 9: Introduce an AndroidTouchProcessor to convert MotionEvents to Flutter touch data. ([flutter/engine#7947](https://github.com/flutter/engine/issues/7947)) a1d84996d Fix spelling errors in dartdocs ([flutter/engine#7997](https://github.com/flutter/engine/issues/7997)) 6bc9c3d17 Roll src/third_party/dart 40bab34fbd..7c70ab1817 (35 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Anthony <<EMAIL>>|add await to fix analyzer ([#28157](https://github.com/flutter/flutter/issues/28157))|
|Michael Goderbauer <<EMAIL>>|Drive-by fix for TODO ([#28669](https://github.com/flutter/flutter/issues/28669))|
|Greg Spencer <<EMAIL>>|Fix spelling errors. ([#28657](https://github.com/flutter/flutter/issues/28657))  Fix the spelling errors in the dartdocs for the framework.  There are no functionality changes here, just documentation fixes.|
|liyuqian <<EMAIL>>|Add convex path and non-AA paint to shader warm-up ([#28614](https://github.com/flutter/flutter/issues/28614))  One of our important client's SKP shows that this could improve one of their janky frame by 20ms. This also improves our flutter_gallery__transition_perf's worst frame time by ~20ms.  On the other hand, 15ms has been added to the start-up latency. I guess it's a little faster to compile the shader on the start-up because we're compiling a lot of shaders there and the CPU cache must be hot.  ## Related Issues  https://github.com/flutter/flutter/issues/813|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 9b924765f4de..5194919e1309 (6 commits) ([#28660](https://github.com/flutter/flutter/issues/28660))   https://github.com/flutter/engine/compare/9b924765f4de...5194919e1309  git log 9b924765f4de29cb9751eb598a629f0cc8d4975b..5194919e130917d29ac15f0ac7f52ea94d1a4d67 --no-merges --oneline 5194919e1 Roll src/third_party/skia 1179d5dfaf8d..b7b2da871e95 (1 commits) ([flutter/engine#7996](https://github.com/flutter/engine/issues/7996)) 58ce0fff7 Roll src/third_party/skia 90043480b587..1179d5dfaf8d (3 commits) ([flutter/engine#7995](https://github.com/flutter/engine/issues/7995)) 72cbe69cc Fix two typos in embedder.h ([flutter/engine#7993](https://github.com/flutter/engine/issues/7993)) 2360b459d Android Embedding PR 11: Add FlutterEngine to FlutterFragment. ([flutter/engine#7972](https://github.com/flutter/engine/issues/7972)) df03ec74f Roll src/third_party/skia 0cedddc1420b..90043480b587 (1 commits) ([flutter/engine#7994](https://github.com/flutter/engine/issues/7994)) 4a148cdc8 Roll src/third_party/skia 67d87128fd00..0cedddc1420b (10 commits) ([flutter/engine#7992](https://github.com/flutter/engine/issues/7992))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Hans Muller <<EMAIL>>|Updated gallery light and dark themes ([#28619](https://github.com/flutter/flutter/issues/28619))|
|Shivam Bhasin <<EMAIL>>|Add Sample code for FlatButton [#21136](https://github.com/flutter/flutter/issues/21136) ([#27751](https://github.com/flutter/flutter/issues/27751))  * Update flat_button.dart  * Added smaple code for Flat Button  * Update flat_button.dart  * Revert "Update flat_button.dart"  This reverts commit c8f00dff39e18b267295890bd0c5a86979b58c24.  * Revert "Added smaple code for Flat Button"  This reverts commit adc4234bc7956293a5b56f39701c6967fe3e919a.  * Update flat_button.dart  * Update flat_button.dart  * Update flat_button.dart  * remove white spaces  * remove white spaces  * add two different samples  * Update flat_button.dart|
|engine-flutter-autoroll <<EMAIL>>|9b924765f Improve performance of Locale.toString ([flutter/engine#7990](https://github.com/flutter/engine/issues/7990)) ([#28623](https://github.com/flutter/flutter/issues/28623))   https://github.com/flutter/engine/compare/3e4e6f5c54db...9b924765f4de  git log 3e4e6f5c54db7a705e6d50f7f3bddfa2ac0d6612..9b924765f4de29cb9751eb598a629f0cc8d4975b --no-merges --oneline 9b924765f Improve performance of Locale.toString ([flutter/engine#7990](https://github.com/flutter/engine/issues/7990))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Hans Muller <<EMAIL>>|Add warnings and an example to the TextEditingController docs ([#28604](https://github.com/flutter/flutter/issues/28604))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 4434a39c7d54..3e4e6f5c54db (18 commits) ([#28612](https://github.com/flutter/flutter/issues/28612))   https://github.com/flutter/engine/compare/4434a39c7d54...3e4e6f5c54db  git log 4434a39c7d545ed47186b2f4d98cd09c8366e720..3e4e6f5c54db7a705e6d50f7f3bddfa2ac0d6612 --no-merges --oneline 3e4e6f5c5 Provide batching for semantics updates ([flutter/engine#7988](https://github.com/flutter/engine/issues/7988)) 0852795bf Roll src/third_party/skia ff84d8a900d4..67d87128fd00 (7 commits) ([flutter/engine#7987](https://github.com/flutter/engine/issues/7987)) 1089b5c9f Check for a null pressure range for motion events ([flutter/engine#7986](https://github.com/flutter/engine/issues/7986)) b7d512949 [macos] Add hover support to FLEViewController ([flutter/engine#7975](https://github.com/flutter/engine/issues/7975)) a82c7ee73 Add async events to pipeline flows. ([flutter/engine#7985](https://github.com/flutter/engine/issues/7985)) c3eb7aea8 Roll src/third_party/skia ab3f8674c9d2..ff84d8a900d4 (1 commits) ([flutter/engine#7983](https://github.com/flutter/engine/issues/7983)) 15793288e Roll src/third_party/dart 9450c6d829..40bab34fbd (8 commits) b324969c1 Roll src/third_party/dart e94e797d7b..9450c6d829 (8 commits) 3f3e4ec90 Roll src/third_party/skia f6faf2fcea7b..ab3f8674c9d2 (2 commits) ([flutter/engine#7980](https://github.com/flutter/engine/issues/7980)) d162ad842 Roll src/third_party/skia 4dbb96dfaa04..f6faf2fcea7b (7 commits) ([flutter/engine#7977](https://github.com/flutter/engine/issues/7977)) 5deb15cdf Roll src/third_party/dart 05bba46133..e94e797d7b (5 commits) 4db7c3050 Android Embedding PR 10: Add system channels to FlutterEngine. ([flutter/engine#7960](https://github.com/flutter/engine/issues/7960)) 9f92324fd Suppress deprecation warning for usage of Configuration.locale ([flutter/engine#7973](https://github.com/flutter/engine/issues/7973)) 9b21ea857 Roll src/third_party/dart 385be0cf31..05bba46133 (6 commits) e3f1bdc4d Roll src/third_party/skia adfc6e40df30..4dbb96dfaa04 (6 commits) ([flutter/engine#7970](https://github.com/flutter/engine/issues/7970)) e32531f0e Switch flutter&[#39](https://github.com/flutter/flutter/issues/39);s dart sdk to full and add dartdevc libraries ([flutter/engine#7968](https://github.com/flutter/engine/issues/7968)) fb1c543dd Link dart:* sources into engine for debugger source support ([flutter/engine#7908](https://github.com/flutter/engine/issues/7908)) dd3656a51 Roll src/third_party/dart 0f9a5198bd..385be0cf31 (14 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Chris Bracken <<EMAIL>>|Roll engine to 3e4e6f5c54db7a705e6d50f7f3bddfa2ac0d6612 ([#28607](https://github.com/flutter/flutter/issues/28607))  flutter/engine@3e4e6f5c5 Provide batching for semantics updates ([flutter/engine#7988](https://github.com/flutter/engine/issues/7988)) flutter/engine@0852795bf Roll src/third_party/skia ff84d8a900d4..67d87128fd00 (7 commits) ([flutter/engine#7987](https://github.com/flutter/engine/issues/7987)) flutter/engine@1089b5c9f Check for a null pressure range for motion events ([flutter/engine#7986](https://github.com/flutter/engine/issues/7986)) flutter/engine@b7d512949 [macos] Add hover support to FLEViewController ([flutter/engine#7975](https://github.com/flutter/engine/issues/7975)) flutter/engine@a82c7ee73 Add async events to pipeline flows. ([flutter/engine#7985](https://github.com/flutter/engine/issues/7985)) flutter/engine@c3eb7aea8 Roll src/third_party/skia ab3f8674c9d2..ff84d8a900d4 (1 commits) ([flutter/engine#7983](https://github.com/flutter/engine/issues/7983)) flutter/engine@15793288e Roll src/third_party/dart 9450c6d829..40bab34fbd (8 commits) flutter/engine@b324969c1 Roll src/third_party/dart e94e797d7b..9450c6d829 (8 commits) flutter/engine@3f3e4ec90 Roll src/third_party/skia f6faf2fcea7b..ab3f8674c9d2 (2 commits) ([flutter/engine#7980](https://github.com/flutter/engine/issues/7980)) flutter/engine@d162ad842 Roll src/third_party/skia 4dbb96dfaa04..f6faf2fcea7b (7 commits) ([flutter/engine#7977](https://github.com/flutter/engine/issues/7977)) flutter/engine@5deb15cdf Roll src/third_party/dart 05bba46133..e94e797d7b (5 commits) flutter/engine@4db7c3050 Android Embedding PR 10: Add system channels to FlutterEngine. ([flutter/engine#7960](https://github.com/flutter/engine/issues/7960)) flutter/engine@9f92324fd Suppress deprecation warning for usage of Configuration.locale ([flutter/engine#7973](https://github.com/flutter/engine/issues/7973)) flutter/engine@9b21ea857 Roll src/third_party/dart 385be0cf31..05bba46133 (6 commits) flutter/engine@e3f1bdc4d Roll src/third_party/skia adfc6e40df30..4dbb96dfaa04 (6 commits) ([flutter/engine#7970](https://github.com/flutter/engine/issues/7970)) flutter/engine@e32531f0e Switch flutter's dart sdk to full and add dartdevc libraries ([flutter/engine#7968](https://github.com/flutter/engine/issues/7968)) flutter/engine@fb1c543dd Link dart:* sources into engine for debugger source support ([flutter/engine#7908](https://github.com/flutter/engine/issues/7908)) flutter/engine@dd3656a51 Roll src/third_party/dart 0f9a5198bd..385be0cf31 (14 commits) flutter/engine@4434a39c7 Add api 21 check to LocalizationChannel.java ([flutter/engine#7967](https://github.com/flutter/engine/issues/7967))|
|Kate Lovett <<EMAIL>>|TextField Snippet for API Docs ([#28598](https://github.com/flutter/flutter/issues/28598))  * Added code snippet for TextField class. ref:[#21136](https://github.com/flutter/flutter/issues/21136)  * Errant semicolon|
|Michael Goderbauer <<EMAIL>>|Call onTapCancel when down pointer gets cancelled ([#28546](https://github.com/flutter/flutter/issues/28546))|
|Chris Bracken <<EMAIL>>|Roll engine to 4434a39c7d545ed47186b2f4d98cd09c8366e720 ([#28608](https://github.com/flutter/flutter/issues/28608))|
|Jonah Williams <<EMAIL>>|disable dart2js test ([#28555](https://github.com/flutter/flutter/issues/28555))|
|Moein Porkamel <<EMAIL>>|Fix typo ([#28558](https://github.com/flutter/flutter/issues/28558))|
|liyuqian <<EMAIL>>|Reland "Shader warm up ([#27660](https://github.com/flutter/flutter/issues/27660))" ([#28537](https://github.com/flutter/flutter/issues/28537))  This reverts commit adc8e159a548121a45ff7a45b4e556aff998d4bd.  This should be safe to land once https://github.com/flutter/flutter/pull/28530 gets merged  Merge on yellow doc test because the doc test is actually green.|
|Jonah Williams <<EMAIL>>|remove personal repo and replace with trivial example for smoke test ([#28386](https://github.com/flutter/flutter/issues/28386))|
|liyuqian <<EMAIL>>|Add LICENSE test to presubmit checks ([#28369](https://github.com/flutter/flutter/issues/28369))  ## Description  Also update the existing dart files with missing licenses.  Without the fix, we'll emit the following error message ``` License headers cannot be found at the beginning of the following files.  /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/lib/src/animation/tween_sequence.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/material/raw_material_button_test.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/async_lifecycle_test.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/sliver_constraints_test.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/app_test.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/test_border.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/physical_model_test.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter/test/widgets/inherited_model.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter_tools/lib/src/base/user_messages.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter_tools/test/src/pubspec_schema.dart /usr/local/google/home/<USER>/flutter/flutter/packages/flutter_tools/test/ios/simulators_test.dart ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ ```  ## Related Issues  Fixes https://github.com/flutter/flutter/issues/28368  ## Checklist  Before you create this PR confirm that it meets all requirements listed below by checking the relevant checkboxes (`[x]`). This will ensure a smooth and quick review process.  - [x] I read the [Contributor Guide] and followed the process outlined there for submitting PRs. - [x] My PR includes tests for *all* changed/updated/fixed behaviors (See [Test Coverage]). - [x] All existing and new tests are passing. - [x] I updated/added relevant documentation (doc comments with `///`). - [x] The analyzer (`flutter analyze --flutter-repo`) does not report any problems on my PR. - [x] I read and followed the [Flutter Style Guide], including [Features we expect every widget to implement]. - [x] I signed the [CLA]. - [x] I am willing to follow-up on review comments in a timely manner.  ## Breaking Change  Does your PR require Flutter developers to manually update their apps to accommodate your change?  - [ ] Yes, this is a breaking change (Please read [Handling breaking changes]). - [x] No, this is *not* a breaking change.  <!-- Links --> [issue database]: https://github.com/flutter/flutter/issues [Contributor Guide]: https://github.com/flutter/flutter/blob/main/docs/contributing/Tree-hygiene.md#overview [Test Coverage]: https://github.com/flutter/flutter/blob/main/docs/contributing/testing/Test-coverage-for-package-flutter.md [Flutter Style Guide]: https://github.com/flutter/flutter/blob/main/docs/contributing/Style-guide-for-Flutter-repo.md [Features we expect every widget to implement]: https://github.com/flutter/flutter/blob/main/docs/contributing/Style-guide-for-Flutter-repo.md#features-we-expect-every-widget-to-implement [CLA]: https://cla.developers.google.com/ [Handling breaking changes]: https://github.com/flutter/flutter/blob/main/docs/contributing/Tree-hygiene.md#handling-breaking-changes|
|liyuqian <<EMAIL>>|Fix backspace and clear length in AnsiStatus ([#28477](https://github.com/flutter/flutter/issues/28477))  Fixes https://github.com/flutter/flutter/issues/26922  Without the fix, the newly added test failed with the following error:  ``` 00:07 +24 -4: Spinners AnsiStatus works for fuchsia [E] Expected: a non-negative value Actual: <-1> Which: is not a non-negative value The following line has overflow backtraces: "Hello world                      \b\b\b\b\b\b\b\b       ⣽\b\b\b\b\b\b\b\b       ⣻\b\b\b\b\b\b\b\b       ⢿\b\b\b\b\b\b\b\b       ⡿\b\b\b\b\b\b\b\b       ⣟\b\b\b\b\b\b\b\b       ⣯\b\b\b\b\b\b\b\b       ⣷\b\b\b\b\b\b\b\b       ⣾\b\b\b\b\b\b\b\b       ⣽\b\b\b\b\b\b\b\b       ⣻\b\b\b\b\b\b\b\b       ⢿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⡿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣟ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣯ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣷ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣾ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣽ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣻ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⢿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⡿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣟ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣯ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣷ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣾ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣽ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣻ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⢿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⡿ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣟ (This is taking an unexpectedly long time.)\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b                                                                                                                                                                                                                                                                                                                           ⣯ (This is taking an unexpectedly long time.)" ```|
|Jonah Williams <<EMAIL>>|Add basic web device and run support ([#28302](https://github.com/flutter/flutter/issues/28302))|
|Dan Field <<EMAIL>>|increase timeout ([#28480](https://github.com/flutter/flutter/issues/28480))|
|liyuqian <<EMAIL>>|Fix type issue of the timelineEvents assignment ([#28530](https://github.com/flutter/flutter/issues/28530))  This fixes https://github.com/flutter/flutter/issues/28374  The fact that json_rpc_2 is hiding exceptions like this might be a bigger issue: https://github.com/flutter/flutter/issues/28531  Merge on red because add2app test is currently flaking out according to @dnfield|
|Sander Kersten <<EMAIL>>|Add lerping between Gradients with arbitrary number of colors and stops ([#27435](https://github.com/flutter/flutter/issues/27435))|
|Anthony <<EMAIL>>|[Material] Unit test for skipping Slider tick mark due to overdensity ([#28013](https://github.com/flutter/flutter/issues/28013))  Added a unit test to check that the tick marks are skipped over when the divisions are set to a number that makes them less than 6dp away from each other.|
|xster <<EMAIL>>|Support iOS devices reporting pressure data of 0 ([#28478](https://github.com/flutter/flutter/issues/28478))|
|engine-flutter-autoroll <<EMAIL>>|113b91fe8 Roll src/third_party/skia 8abc84294571..adfc6e40df30 (9 commits) ([flutter/engine#7965](https://github.com/flutter/engine/issues/7965)) ([#28526](https://github.com/flutter/flutter/issues/28526))   https://github.com/flutter/engine/compare/131091827790...113b91fe82f1  git log 1310918277907d48ad25a336bbee3269ccbb2674..113b91fe82f147b3fb77899726736171c06dad26 --no-merges --oneline 113b91fe8 Roll src/third_party/skia 8abc84294571..adfc6e40df30 (9 commits) ([flutter/engine#7965](https://github.com/flutter/engine/issues/7965))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|pass --skip-build-script-checks and remove module usage ([#28343](https://github.com/flutter/flutter/issues/28343))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine fa7a25aea3e3..131091827790 (2 commits) ([#28522](https://github.com/flutter/flutter/issues/28522))   https://github.com/flutter/engine/compare/fa7a25aea3e3...131091827790  git log fa7a25aea3e35beee9824011798b66738e2005f0..1310918277907d48ad25a336bbee3269ccbb2674 --no-merges --oneline 131091827 Roll src/third_party/dart f7e435ac26..0f9a5198bd (2 commits) ef1e21f53 Roll src/third_party/skia 346e10e8ae1a..8abc84294571 (1 commits) ([flutter/engine#7962](https://github.com/flutter/engine/issues/7962))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|use deviceManager discovery in daemon protocol ([#28341](https://github.com/flutter/flutter/issues/28341))|
|Greg Spencer <<EMAIL>>|Add SHA256 checksums to published metadata ([#28472](https://github.com/flutter/flutter/issues/28472))  Adds SHA256 checksums to the released package database, in preparation for showing them on the download website. (I'll update the existing data by hand).  Fixes [#28465](https://github.com/flutter/flutter/issues/28465)|
|Jonah Williams <<EMAIL>>|remove json_schema dep again ([#28517](https://github.com/flutter/flutter/issues/28517))|
|Zachary Anderson <<EMAIL>>|[flutter_tool,fuchsia_tester] Only require a test source dir for coverage ([#28168](https://github.com/flutter/flutter/issues/28168))|
|engine-flutter-autoroll <<EMAIL>>|fa7a25aea Roll src/third_party/skia ac6d4c0c8eae..346e10e8ae1a (3 commits) ([flutter/engine#7959](https://github.com/flutter/engine/issues/7959)) ([#28511](https://github.com/flutter/flutter/issues/28511))   https://github.com/flutter/engine/compare/9f95ef23aac7...fa7a25aea3e3  git log 9f95ef23aac76c4f112446412cc6ae117e743378..fa7a25aea3e35beee9824011798b66738e2005f0 --no-merges --oneline fa7a25aea Roll src/third_party/skia ac6d4c0c8eae..346e10e8ae1a (3 commits) ([flutter/engine#7959](https://github.com/flutter/engine/issues/7959))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|9f95ef23a Android Embedding PR 8: Add FlutterEngine attachment/detachment to FlutterView ([flutter/engine#7946](https://github.com/flutter/engine/issues/7946)) ([#28510](https://github.com/flutter/flutter/issues/28510))   https://github.com/flutter/engine/compare/31d31e6d2ae8...9f95ef23aac7  git log 31d31e6d2ae853798c105f606bf2e5cfdd6e0b04..9f95ef23aac76c4f112446412cc6ae117e743378 --no-merges --oneline 9f95ef23a Android Embedding PR 8: Add FlutterEngine attachment/detachment to FlutterView ([flutter/engine#7946](https://github.com/flutter/engine/issues/7946))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 1d0615af4ee5..31d31e6d2ae8 (6 commits) ([#28507](https://github.com/flutter/flutter/issues/28507))   https://github.com/flutter/engine/compare/1d0615af4ee5...31d31e6d2ae8  git log 1d0615af4ee5a962a023e9ad2a0c2fab24d6dd1b..31d31e6d2ae853798c105f606bf2e5cfdd6e0b04 --no-merges --oneline 31d31e6d2 Android PR 7: Introduce structure of FlutterActivity and FlutterFragment ([flutter/engine#7912](https://github.com/flutter/engine/issues/7912)) 3962bbdc6 Roll src/third_party/skia bcfbf6e94c22..ac6d4c0c8eae (1 commits) ([flutter/engine#7958](https://github.com/flutter/engine/issues/7958)) cb9e6734a Roll src/third_party/dart 7a63d31151..f7e435ac26 (252 commits) 18ad03d49 Fixed an Android keyboard entry bug that was introduced by the embedding refactor. ([#28438](https://github.com/flutter/flutter/issues/28438)) ([flutter/engine#7954](https://github.com/flutter/engine/issues/7954)) a61056aa5 Roll src/third_party/skia 0b2c05470bab..bcfbf6e94c22 (8 commits) ([flutter/engine#7956](https://github.com/flutter/engine/issues/7956)) b36f9034c Roll src/third_party/dart c92d5ca288..7a63d31151 (6 commits) ([flutter/engine#7955](https://github.com/flutter/engine/issues/7955))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Stevie Strickland <<EMAIL>>|Remove unused --packages argument to gen_snapshot. ([#28334](https://github.com/flutter/flutter/issues/28334))  This command line argument has already been removed in the main Dart repository, so building flutter with the latest dart fails.  This time, also update the tests to avoid the checks for a package path on snapshot generation.|
|Michael Goderbauer <<EMAIL>>|Fuschia -> Fuchsia ([#28482](https://github.com/flutter/flutter/issues/28482))|
|engine-flutter-autoroll <<EMAIL>>|Roll engine f7306394e303..1d0615af4ee5 (7 commits) ([#28487](https://github.com/flutter/flutter/issues/28487))   https://github.com/flutter/engine/compare/f7306394e303...1d0615af4ee5  git log f7306394e303a210a9971acb60f9ba11d7c64ec8..1d0615af4ee5a962a023e9ad2a0c2fab24d6dd1b --no-merges --oneline 1d0615af4 libtxt: remove a debug log message in ComputeStrut ([flutter/engine#7953](https://github.com/flutter/engine/issues/7953)) fd6c3fc70 Do not clear FlutterJNI state when a FlutterView is detached ([flutter/engine#7929](https://github.com/flutter/engine/issues/7929)) dff323d21 Roll src/third_party/skia 507158f88d4d..0b2c05470bab (19 commits) ([flutter/engine#7952](https://github.com/flutter/engine/issues/7952)) 5fcc6aca8 Roll src/third_party/skia 478d7d46f8d7..507158f88d4d (1 commits) ([flutter/engine#7949](https://github.com/flutter/engine/issues/7949)) 6e1f0f063 Roll src/third_party/skia 5bbd02d6bb3d..478d7d46f8d7 (1 commits) ([flutter/engine#7948](https://github.com/flutter/engine/issues/7948)) 9f6b297cc Roll src/third_party/skia e1636a4b56aa..5bbd02d6bb3d (1 commits) ([flutter/engine#7944](https://github.com/flutter/engine/issues/7944)) f2489c1b5 Correct FlutterSemanticsNode member name style ([flutter/engine#7942](https://github.com/flutter/engine/issues/7942))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|KyleWong <<EMAIL>>|Add tests for pr:27687 ([#27944](https://github.com/flutter/flutter/issues/27944))|
|lsaudon <<EMAIL>>|Add shapeBorder option on App Bar ([#21834](https://github.com/flutter/flutter/issues/21834))|
|xster <<EMAIL>>|Add long-press-move support for text fields 2 ([#28242](https://github.com/flutter/flutter/issues/28242))|
|jslavitz <<EMAIL>>|Breaks the moveBy call from drag and dragFrom into two separate calls and changes the default behavior of DragStartBehavior to DragStartBehavior.start ([#26438](https://github.com/flutter/flutter/issues/26438))  * Breaking change which sets the default DragStartBehavior to DragStartBehavior.start and changes WidgetTester.drag and WidgetTester.dragFrom by breaking the moveBy function call into multiple pieces.|
|stuartmorgan <<EMAIL>>|Convert PointerEvent's toString to Diagnosticable ([#27904](https://github.com/flutter/flutter/issues/27904))  Rather than building a description string by hand, use Diagnosticable properties. Simplifies output, since many fields have uninteresting default values in many cases, and makes it easier to add more loggable properties in subclasses.  Issue [#28014](https://github.com/flutter/flutter/issues/28014)|
|Jason Simmons <<EMAIL>>|Only call Activity.reportFullyDrawn on Lollipop or above ([#28175](https://github.com/flutter/flutter/issues/28175))  This API requires the UPDATE_DEVICE_STATS permission on KitKat|
|Hans Muller <<EMAIL>>|Ensure that the DropdownButton menu respects its parents bounds ([#28371](https://github.com/flutter/flutter/issues/28371))|
|rami-a <<EMAIL>>|[Material] Add the ability to theme trailing app bar actions independently from leading ([#28214](https://github.com/flutter/flutter/issues/28214))  * Allow app bar actions to be themed independently if needed  * Update SliverAppBar docs that were out of date  * Clarify fallback behavior in a comment  * Analyzer  * Address PR feedback  * Address PR feedback  * Put back checks on actions icon theme  * Address PR feedback  * Retrigger CI|
|Brandon Rodriguez <<EMAIL>>|[Typo] Update 'use' to 'user' ([#28245](https://github.com/flutter/flutter/issues/28245))|
|Reece Dunham <<EMAIL>>|Removed trailing whitespace from the end of the CI config ([#28370](https://github.com/flutter/flutter/issues/28370))|
|Michael Goderbauer <<EMAIL>>|Add more docs to BackdropFilter ([#28017](https://github.com/flutter/flutter/issues/28017))|
|liyuqian <<EMAIL>>|Mark non-flaky test as such ([#28373](https://github.com/flutter/flutter/issues/28373))|
|engine-flutter-autoroll <<EMAIL>>|f7306394e fix sendLocales on old android versions ([flutter/engine#7937](https://github.com/flutter/engine/issues/7937)) ([#28450](https://github.com/flutter/flutter/issues/28450))   https://github.com/flutter/engine/compare/502c9c4002a6...f7306394e303  git log 502c9c4002a6b456f829f79bfbd2ff61d075d1d7..f7306394e303a210a9971acb60f9ba11d7c64ec8 --no-merges --oneline f7306394e fix sendLocales on old android versions ([flutter/engine#7937](https://github.com/flutter/engine/issues/7937))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0546e1e5e901..502c9c4002a6 (2 commits) ([#28446](https://github.com/flutter/flutter/issues/28446))   https://github.com/flutter/engine/compare/0546e1e5e901...502c9c4002a6  git log 0546e1e5e9015e7a597c9e59cf89ee614a260251..502c9c4002a6b456f829f79bfbd2ff61d075d1d7 --no-merges --oneline 502c9c400 Android Embedding PR 6: Introduce FlutterView structure with FlutterSurfaceView and FlutterTextureView. ([flutter/engine#7896](https://github.com/flutter/engine/issues/7896)) 687229e70 Roll src/third_party/skia 52d8a5976a23..e1636a4b56aa (1 commits) ([flutter/engine#7943](https://github.com/flutter/engine/issues/7943))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Will Larche <<EMAIL>>|[Gallery] Fix fortnightly analysis for consts. ([#28431](https://github.com/flutter/flutter/issues/28431))|
|engine-flutter-autoroll <<EMAIL>>|0546e1e5e Roll src/third_party/skia 33c1b315265c..52d8a5976a23 (1 commits) ([flutter/engine#7941](https://github.com/flutter/engine/issues/7941)) ([#28422](https://github.com/flutter/flutter/issues/28422))   https://github.com/flutter/engine/compare/90fcaff9008e...0546e1e5e901  git log 90fcaff9008e04b71c00cd6ce27ae335c299c8a6..0546e1e5e9015e7a597c9e59cf89ee614a260251 --no-merges --oneline 0546e1e5e Roll src/third_party/skia 33c1b315265c..52d8a5976a23 (1 commits) ([flutter/engine#7941](https://github.com/flutter/engine/issues/7941))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Will Larche <<EMAIL>>|[Gallery] Fortnightly demo moved from flutter/samples. ([#28125](https://github.com/flutter/flutter/issues/28125))  * [Gallery] Fortnightly demo moved from flutter/samples.  * [Gallery] Fortnightly analysis corrections.  * [Gallery] Minor rename.  * [Gallery] Making floats ints.  * [Gallery] Fortnightly minor visual polish.  * [Gallery] Formatting.  * [Gallery] Upping package version for gallery to support fortnightly demo.  * [Gallery] Fortnightly copy correction.  * [Gallery] Fortnightly study polish.  * [Gallery] Fortnightly polish.  * [Gallery] Fortnightly copy correction.  * [Gallery] Fortnightly copy now held in '''.  * [Gallery] Fortnightly comment.  * [Gallery] Updates necessary when flutter_gallery_assets updates.  * [Gallery] Updating macrobenchmarks because we updated flutter_gallery_assets.  * [Gallery] Moving spaces to beginning of copy lines.  * [Gallery] Copy correction.  * [Gallery] Adding tooltip to satisfy smoke test.  * [Gallery] Minor pr feedback.  * [Gallery] Forced upgrade.|
|engine-flutter-autoroll <<EMAIL>>|90fcaff90 Roll src/third_party/skia 2ed90e152d47..33c1b315265c (1 commits) ([flutter/engine#7940](https://github.com/flutter/engine/issues/7940)) ([#28413](https://github.com/flutter/flutter/issues/28413))   https://github.com/flutter/engine/compare/b1779e7d95ea...90fcaff9008e  git log b1779e7d95ea812ced2f4993932bdb2325f5a8ba..90fcaff9008e04b71c00cd6ce27ae335c299c8a6 --no-merges --oneline 90fcaff90 Roll src/third_party/skia 2ed90e152d47..33c1b315265c (1 commits) ([flutter/engine#7940](https://github.com/flutter/engine/issues/7940))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 0d777b947e4c..b1779e7d95ea (2 commits) ([#28410](https://github.com/flutter/flutter/issues/28410))   https://github.com/flutter/engine/compare/0d777b947e4c...b1779e7d95ea  git log 0d777b947e4cc52a0bc17566f3e450f869ca00c9..b1779e7d95ea812ced2f4993932bdb2325f5a8ba --no-merges --oneline b1779e7d9 Roll src/third_party/skia 867da4b96e8e..2ed90e152d47 (1 commits) ([flutter/engine#7939](https://github.com/flutter/engine/issues/7939)) 018a4f783 Roll src/third_party/skia 12f1fecea947..867da4b96e8e (1 commits) ([flutter/engine#7936](https://github.com/flutter/engine/issues/7936))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|update packages ([#28400](https://github.com/flutter/flutter/issues/28400))|
|engine-flutter-autoroll <<EMAIL>>|0d777b947 Roll src/third_party/skia 72f6f044cc73..12f1fecea947 (1 commits) ([flutter/engine#7935](https://github.com/flutter/engine/issues/7935)) ([#28392](https://github.com/flutter/flutter/issues/28392))   https://github.com/flutter/engine/compare/3efc9f5099e5...0d777b947e4c  git log 3efc9f5099e537b3885aacfe3506903c23c4e1ca..0d777b947e4cc52a0bc17566f3e450f869ca00c9 --no-merges --oneline 0d777b947 Roll src/third_party/skia 72f6f044cc73..12f1fecea947 (1 commits) ([flutter/engine#7935](https://github.com/flutter/engine/issues/7935))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Dan Field <<EMAIL>>|Reland "Allow for gradle downloading missing SDK assets" ([#28097](https://github.com/flutter/flutter/issues/28097)) ([#28355](https://github.com/flutter/flutter/issues/28355))  * Allow for gradle downloading missing SDK assets if SDK licenses are present.  *  Improvements for windows testing|
|Dan Field <<EMAIL>>|add2app test ([#27712](https://github.com/flutter/flutter/issues/27712))  * add2app test|
|Dan Field <<EMAIL>>|fix red build for analysis ([#28398](https://github.com/flutter/flutter/issues/28398))|
|Gary Qian <<EMAIL>>|Force line height in TextFields with strut ([#27612](https://github.com/flutter/flutter/issues/27612))|
|engine-flutter-autoroll <<EMAIL>>|3efc9f509 Roll src/third_party/skia 2a5511307d34..72f6f044cc73 (2 commits) ([flutter/engine#7934](https://github.com/flutter/engine/issues/7934)) ([#28387](https://github.com/flutter/flutter/issues/28387))   https://github.com/flutter/engine/compare/1741497a574f...3efc9f5099e5  git log 1741497a574fb5b23a32d11f7f499a3065009585..3efc9f5099e537b3885aacfe3506903c23c4e1ca --no-merges --oneline 3efc9f509 Roll src/third_party/skia 2a5511307d34..72f6f044cc73 (2 commits) ([flutter/engine#7934](https://github.com/flutter/engine/issues/7934))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Bryan Henry <<EMAIL>>|Clean up flutter_gallery.cmx's sandbox ([#28193](https://github.com/flutter/flutter/issues/28193))  Delete unused 'persistent-storage' feature (which is being removed from Fuchsia) and inclusion of '/system/data' in namepsace|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 5809adea4156..1741497a574f (5 commits) ([#28378](https://github.com/flutter/flutter/issues/28378))   https://github.com/flutter/engine/compare/5809adea4156...1741497a574f  git log 5809adea415605b821704967cd9e0b0fc05ce34a..1741497a574fb5b23a32d11f7f499a3065009585 --no-merges --oneline 1741497a5 Roll src/third_party/skia a753d1a3475d..2a5511307d34 (3 commits) ([flutter/engine#7933](https://github.com/flutter/engine/issues/7933)) 0a50e0846 Roll src/third_party/skia ee1c8a733e5b..a753d1a3475d (16 commits) ([flutter/engine#7930](https://github.com/flutter/engine/issues/7930)) 204e7da36 New setting to decide whether we want the engine to load ICU mapping. ([flutter/engine#7928](https://github.com/flutter/engine/issues/7928)) 36d495d57 Dynamic patching support for native code libraries. ([flutter/engine#7927](https://github.com/flutter/engine/issues/7927)) 21c59197e [re-land] Use all font managers to discover fonts for strut. ([flutter/engine#7803](https://github.com/flutter/engine/issues/7803))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine f80928a9e403..5809adea4156 (3 commits) ([#28367](https://github.com/flutter/flutter/issues/28367))   https://github.com/flutter/engine/compare/f80928a9e403...5809adea4156  git log f80928a9e403c6b2fc1b8de6a00bf6189b715c40..5809adea415605b821704967cd9e0b0fc05ce34a --no-merges --oneline 5809adea4 Make the layout of dynamic patch bundle similar to APK. ([flutter/engine#7925](https://github.com/flutter/engine/issues/7925)) e2a449a51 Move canvas clear after preroll ([flutter/engine#7923](https://github.com/flutter/engine/issues/7923)) 8529dbc86 Remove unused FML file export.h ([flutter/engine#7926](https://github.com/flutter/engine/issues/7926))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Jonah Williams <<EMAIL>>|only perform multi-root mapping if there are multiple roots ([#28352](https://github.com/flutter/flutter/issues/28352))|
|Stanislav Baranov <<EMAIL>>|Dynamic patching support for native code libraries. ([#28349](https://github.com/flutter/flutter/issues/28349))|
|liyuqian <<EMAIL>>|Revert "Shader warm up ([#27660](https://github.com/flutter/flutter/issues/27660))" ([#28376](https://github.com/flutter/flutter/issues/28376))  This reverts commit a44f174efc218ac1b4b94b9d77dafdb5dfea1432.  Reason: start_up tests become flaky.  See https://github.com/flutter/flutter/issues/28374  TBR: xster  Merge on red to fix the tree|
|liyuqian <<EMAIL>>|Shader warm up ([#27660](https://github.com/flutter/flutter/issues/27660))  This patch adds a default shader warm up process which moves shader compilation from the animation time to the startup time. This also provides an extension for `runApp` so developers can customize the warm up process.  This should reduce our worst_frame_rasterizer_time_millis from ~100ms to ~20-30ms for both flutter_gallery and complex_layout benchmarks. Besides, this should also have a significant improvement on 90th and 99th percentile time (50%-100% speedup in some cases, but I haven't tested them thoroughly; I'll let our device lab collect the data afterwards).  The tradeoff the is the startup time (time to first frame). Our `flutter run --profile --trace-startup` seems to be a little noisy and I see about 100ms-200ms increase in that measurement for complex_layout and flutter_gallery. Note that this only happens on the first run after install or data wipe. Later the Skia persistent cache will remove the overhead.  This also adds a cubic_bezier benchmark to test the custom shader warm up process.  This should fix https://github.com/flutter/flutter/issues/813 (either by `defaultShaderWarmUp`, or a `customShaderWarmUp`).|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 33bb91cc1591..f80928a9e403 (4 commits) ([#28353](https://github.com/flutter/flutter/issues/28353))   https://github.com/flutter/engine/compare/33bb91cc1591...f80928a9e403  git log 33bb91cc15916610261097eb971ec8a11b804d06..f80928a9e403c6b2fc1b8de6a00bf6189b715c40 --no-merges --oneline f80928a9e Roll src/third_party/skia fdd15284affe..ee1c8a733e5b (15 commits) ([flutter/engine#7924](https://github.com/flutter/engine/issues/7924)) 93f339fb6 fix Memory leak when using PlatformView [IOS] [#24714](https://github.com/flutter/flutter/issues/24714) ([flutter/engine#7919](https://github.com/flutter/engine/issues/7919)) 2d33e775f Roll src/third_party/skia 969659dbb313..fdd15284affe (2 commits) ([flutter/engine#7922](https://github.com/flutter/engine/issues/7922)) 6d4edf235 Roll src/third_party/skia 9431161ca973..969659dbb313 (3 commits) ([flutter/engine#7921](https://github.com/flutter/engine/issues/7921))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Zachary Anderson <<EMAIL>>|[flutter_tool,fuchsia] Add missing dep to flutter_tool ([#28346](https://github.com/flutter/flutter/issues/28346))  Needed for Fuchsia roll.|
|rami-a <<EMAIL>>|Minor UI tweaks to Cards demo based on internal feedback ([#28215](https://github.com/flutter/flutter/issues/28215))  * Minor UI tweaks to Cards demo based on internal feedback  * Analyzer  * Tweak ink ripple opacity  * Address PR feedback|
|rami-a <<EMAIL>>|[Material] Add ability to set shadow color and selected shadow color for chips and for chip themes ([#28163](https://github.com/flutter/flutter/issues/28163))  * Add ability to set shadow color for chips and for chip themes  * Add selected shadow color as a property as well  * Update phrasing of documentation  * Address PR feedback|
|ocavue <<EMAIL>>|Make extended FAB's icon optional ([#27711](https://github.com/flutter/flutter/issues/27711))|
|Chris Bracken <<EMAIL>>|Roll engine to 33bb91cc15916610261097eb971ec8a11b804d06 ([#28315](https://github.com/flutter/flutter/issues/28315))  flutter/engine@33bb91c Allow embedders to specify a vsync waiter. ([flutter/engine#7914](https://github.com/flutter/engine/issues/7914)) flutter/engine@4cb9a14 Allow embedders to add events to the timeline. ([flutter/engine#7917](https://github.com/flutter/engine/issues/7917))|
|Zachary Anderson <<EMAIL>>|[fuchsia] Fix paths to find and ls ([#28280](https://github.com/flutter/flutter/issues/28280))|
|Chris Bracken <<EMAIL>>|Roll engine to cb0f7ece1f251c78a07550db89d0fcb3edf61e3c ([#28309](https://github.com/flutter/flutter/issues/28309))  flutter/engine@cb0f7ece1 Roll src/third_party/skia 63c4d3596915..e9912ca049a0 (8 commits) ([flutter/engine#7918](https://github.com/flutter/engine/issues/7918))|
|Chris Bracken <<EMAIL>>|Roll engine to 043d92c48abdebdad926569bc204a59c5cf20a11 ([#28308](https://github.com/flutter/flutter/issues/28308))  flutter/engine@043d92c48 Embedder API for setting the persistent cache path ([flutter/engine#7915](https://github.com/flutter/engine/issues/7915))|
|xster <<EMAIL>>|Add some more CupertinoPicker doc ([#28166](https://github.com/flutter/flutter/issues/28166))|
|Stanislav Baranov <<EMAIL>>|Reland [#27754](https://github.com/flutter/flutter/issues/27754), now that bsdiff has moved to flutter/packages. ([#28291](https://github.com/flutter/flutter/issues/28291))|
|Ray Rischpater, KF6GPE <<EMAIL>>|deploy to .dev firebase projects. ([#28171](https://github.com/flutter/flutter/issues/28171))  * deploy to .dev firebase projects.  * Updated secrets for .dev web sites.  * Fixed missing docs.*.io references.|
|Chris Bracken <<EMAIL>>|Roll the engine to 5db4b377244bae48bc21e449e616417e68c9a6b9 ([#28296](https://github.com/flutter/flutter/issues/28296))  flutter/engine@f1f19bba8 Revert "Revert "Revert "Reland PerformanceOverlayLayer golden test ([flutter/engine#7863](https://github.com/flutter/engine/issues/7863))" ([flutter/engine#7895](https://github.com/flutter/engine/issues/7895))" ([flutter/engine#7911](https://github.com/flutter/engine/issues/7911))" ([flutter/engine#7916](https://github.com/flutter/engine/issues/7916)) flutter/engine@5db4b3772 Revert dart rolls ([flutter/engine#7913](https://github.com/flutter/engine/issues/7913)) flutter/engine@0053c1d50 Roll src/third_party/dart 75ee523f91..4cff5a4fd6 (6 commits) flutter/engine@8620579cf Roll src/third_party/skia 9a049e6ce39d..63c4d3596915 (8 commits) ([flutter/engine#7909](https://github.com/flutter/engine/issues/7909)) flutter/engine@aa2b287cb Reland [flutter/engine#7777](https://github.com/flutter/engine/issues/7777) with proper LICENSE ([flutter/engine#7888](https://github.com/flutter/engine/issues/7888)) flutter/engine@a5773d121 Do not add ghost runs for trailing whitespace if the text is ellipsized ([flutter/engine#7906](https://github.com/flutter/engine/issues/7906)) flutter/engine@93eeb48ba Revert "Revert "Remove unnecessary entry-point closurization. ([flutter/engine#7827](https://github.com/flutter/engine/issues/7827))" ([flutter/engine#7876](https://github.com/flutter/engine/issues/7876))" ([flutter/engine#7904](https://github.com/flutter/engine/issues/7904)) flutter/engine@bbaeba3f3 Roll src/third_party/skia 05ff93c43ea5..9a049e6ce39d (11 commits) ([flutter/engine#7903](https://github.com/flutter/engine/issues/7903)) flutter/engine@1cf6b8b64 Roll src/third_party/dart 5ddd157809..75ee523f91 (19 commits) flutter/engine@be32040ef Roll src/third_party/skia 348227b89430..05ff93c43ea5 (2 commits) ([flutter/engine#7901](https://github.com/flutter/engine/issues/7901)) flutter/engine@293b3de98 Roll src/third_party/skia 20ebd0cb3882..348227b89430 (4 commits) ([flutter/engine#7900](https://github.com/flutter/engine/issues/7900)) flutter/engine@6e6020d29 Eliminate .member = foo struct initialization ([flutter/engine#7899](https://github.com/flutter/engine/issues/7899)) flutter/engine@6d8bd99af Revert "Reland PerformanceOverlayLayer golden test ([flutter/engine#7863](https://github.com/flutter/engine/issues/7863))" ([flutter/engine#7895](https://github.com/flutter/engine/issues/7895)) flutter/engine@39f7066b6 Test profile and release build and unit tests ([flutter/engine#7880](https://github.com/flutter/engine/issues/7880)) flutter/engine@abe9826a9 Add accessibility semantics support to embedder ([flutter/engine#7891](https://github.com/flutter/engine/issues/7891)) flutter/engine@ce7016e1f Roll src/third_party/skia e471c05f92e8..20ebd0cb3882 (4 commits) ([flutter/engine#7894](https://github.com/flutter/engine/issues/7894)) flutter/engine@684c9394c Respect the custom GL proc table when creating the resource context on the IO thread. ([flutter/engine#7893](https://github.com/flutter/engine/issues/7893)) flutter/engine@e11d0e96f Android embedding refactor pr5 add flutterengine impl ([flutter/engine#7878](https://github.com/flutter/engine/issues/7878)) flutter/engine@2f4a38dbd Android embedding refactor pr3 add remaining systemchannels ([flutter/engine#7892](https://github.com/flutter/engine/issues/7892)) flutter/engine@8427d73c8 Reland PerformanceOverlayLayer golden test ([flutter/engine#7863](https://github.com/flutter/engine/issues/7863)) flutter/engine@61fc1786f Roll src/third_party/dart c92d5ca288..5ddd157809 (153 commits) flutter/engine@b0671145a Roll src/third_party/skia 7738736f9622..e471c05f92e8 (23 commits) ([flutter/engine#7889](https://github.com/flutter/engine/issues/7889)) flutter/engine@6d7eb5218 Revert "Android embedding refactor pr3 add remaining systemchannels ([flutter/engine#7874](https://github.com/flutter/engine/issues/7874))" ([flutter/engine#7886](https://github.com/flutter/engine/issues/7886)) flutter/engine@dad6ec01c Correct onAccessibilityFeaturesChanged docs ([flutter/engine#7883](https://github.com/flutter/engine/issues/7883))|
|Dan Field <<EMAIL>>|Revert "Allow for gradle downloading missing SDK assets ([#28097](https://github.com/flutter/flutter/issues/28097))" ([#28295](https://github.com/flutter/flutter/issues/28295))  This reverts commit 6f5e88a59f5ac01fd55b5c3a11b7c2ddc6c7402c.|
|xster <<EMAIL>>|Add slight clarification to debugDeterministicCursor ([#27898](https://github.com/flutter/flutter/issues/27898))|
|Hans Muller <<EMAIL>>|[Material] Expand BottomNavigationBar API (reprise) ([#28159](https://github.com/flutter/flutter/issues/28159))|
|Michael Goderbauer <<EMAIL>>|Add backgroundColor argument to TextStyle for convenience ([#28172](https://github.com/flutter/flutter/issues/28172))|
|Dan Field <<EMAIL>>|Allow for gradle downloading missing SDK assets ([#28097](https://github.com/flutter/flutter/issues/28097))  * Allow for gradle downloading missing SDK assets if SDK licenses are present.  * Fix license path for missing sdkmanager  * Cirrus re-run...  * Fix condition check  * rename props, add docs, rename method  * fix tests after param changes  * Fix weird path for flutter run and add tests  * remove print, fix tests|
|Greg Spencer <<EMAIL>>|Fix the length of the valueHelp on the --sample option for the create command, and turn on wrapping. ([#28264](https://github.com/flutter/flutter/issues/28264))  The wrapping for some commands was never actually turned on, so this turns it on.  Also, it shortens the `valueHelp` for the `--sample` option to be just "id" instead of "the sample ID of the desired sample from the API documentation website (http://docs.flutter.io)", which was causing the line to get far too long.  Fixes [#23074](https://github.com/flutter/flutter/issues/23074)|
|Dan Field <<EMAIL>>|Reduce memory benchmark perf tests outer loop to 10. ([#28281](https://github.com/flutter/flutter/issues/28281))  * reduce iterations to avoid test timing out|
|Dan Field <<EMAIL>>|Make logcat less chatty on perf tests ([#28272](https://github.com/flutter/flutter/issues/28272))|
|Greg Spencer <<EMAIL>>|Revert "Remove unused --packages argument to gen_snapshot. ([#28101](https://github.com/flutter/flutter/issues/28101))" ([#28265](https://github.com/flutter/flutter/issues/28265))  This reverts commit 1c021506df23434dd5e881ba0e4d8c5a9f88df8d.  The pre-commit tests never actually ran, so it looked green when it wasn't. This breaks 16 tests that depend on the argument existing, even though it isn't actually used.|
|Alexandre Ardhuin <<EMAIL>>|format parameter list ([#27261](https://github.com/flutter/flutter/issues/27261))|
|Stevie Strickland <<EMAIL>>|Remove unused --packages argument to gen_snapshot. ([#28101](https://github.com/flutter/flutter/issues/28101))  This command line argument has already been removed in the main Dart repository, so building flutter with the latest dart fails.|
|Volodymyr Lykhonis <<EMAIL>>|Update AUTHORS to add Volodymyr Lykhonis ([#24889](https://github.com/flutter/flutter/issues/24889))|
|Jonah Williams <<EMAIL>>|Remove extra slash from builder that is not handled on windows ([#28027](https://github.com/flutter/flutter/issues/28027))|
|Jonah Williams <<EMAIL>>|Remove set literal syntax ([#28238](https://github.com/flutter/flutter/issues/28238))|
|Michael Goderbauer <<EMAIL>>|Remove line breaks from PR template ([#28235](https://github.com/flutter/flutter/issues/28235))|
|jslavitz <<EMAIL>>|Adds fix for NAN value and pressure values outside of device reported min and max ([#26659](https://github.com/flutter/flutter/issues/26659))  * Fix for nan pressure values and values outside min and max pressure ranges|
|jslavitz <<EMAIL>>|Adds media query check in editable_text ([#27955](https://github.com/flutter/flutter/issues/27955))  * media query check|
|Mike Roosa <<EMAIL>>|removed image with 404 ([#26376](https://github.com/flutter/flutter/issues/26376))  Update README.md to remove 404 link on Posse image|
|Matt Sullivan <<EMAIL>>|Added sample code to AnimatedWidget ([#27803](https://github.com/flutter/flutter/issues/27803))  * Added sample to AnimatedWidget  * Updated AnimatedWidget example to use listenable from parent  * Made progress private and changed widget name|
|Ali EP <<EMAIL>>|Fixes crossAxisSpacing overflow in RTL ([#27945](https://github.com/flutter/flutter/issues/27945))|
|Sander Kersten <<EMAIL>>|Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView ([#27433](https://github.com/flutter/flutter/issues/27433))|
|Michael Goderbauer <<EMAIL>>|Adds a template for Pull Requests ([#28224](https://github.com/flutter/flutter/issues/28224))|
|Michael Goderbauer <<EMAIL>>|Add TextOverflow.visible ([#28182](https://github.com/flutter/flutter/issues/28182))|
|Dan Field <<EMAIL>>|Remove json_schema and cli_util deps from flutter_tool ([#28040](https://github.com/flutter/flutter/issues/28040))  * remove json_schema dep  * remove unnecessary pin  * removed unused dep  * remove unused impport  * Fix incorrect condition|
|xster <<EMAIL>>|Rename SuperellipseShare ContinuousRectangleBorder ([#28183](https://github.com/flutter/flutter/issues/28183))|
|xster <<EMAIL>>|revert 26001 ([#28032](https://github.com/flutter/flutter/issues/28032))|
|Kaushik Iska <<EMAIL>>|Add outer try block for obtainKey errors. Add docs. ([#27953](https://github.com/flutter/flutter/issues/27953))  * Add outer try block for obtainKey errors. Add docs.  * change to // from ///, and do specific error handling  * Add return statement after error  * Add test|
|Jonah Williams <<EMAIL>>|Outline for survey implementation ([#27305](https://github.com/flutter/flutter/issues/27305))|
|Dan Field <<EMAIL>>|Manual engine roll to ([#28222](https://github.com/flutter/flutter/issues/28222))  flutter/engine@6d7eb5218 Revert "Android embedding refactor pr3 add remaining systemchannels ([flutter/engine#7874](https://github.com/flutter/engine/issues/7874))" ([flutter/engine#7886](https://github.com/flutter/engine/issues/7886)) flutter/engine@dad6ec01c Correct onAccessibilityFeaturesChanged docs ([flutter/engine#7883](https://github.com/flutter/engine/issues/7883)) flutter/engine@fc1a68ea1 Roll src/third_party/skia e0a6a048bc4a..7738736f9622 (4 commits) ([flutter/engine#7884](https://github.com/flutter/engine/issues/7884)) flutter/engine@a150cc125 Fix minor typos in accessibility action docs ([flutter/engine#7882](https://github.com/flutter/engine/issues/7882)) flutter/engine@bf1654450 Roll src/third_party/skia 249a25730521..e0a6a048bc4a (2 commits) ([flutter/engine#7881](https://github.com/flutter/engine/issues/7881)) flutter/engine@d60ef0c36 Roll src/third_party/skia ce62dec02293..249a25730521 (15 commits) ([flutter/engine#7879](https://github.com/flutter/engine/issues/7879)) flutter/engine@1263d28ab Fix caret being at left edge when newline pressed on centered text ([flutter/engine#7875](https://github.com/flutter/engine/issues/7875))|
|Michael Goderbauer <<EMAIL>>|Fix 'to to' ([#28216](https://github.com/flutter/flutter/issues/28216))|
|Dan Field <<EMAIL>>|Roll engine to f45572e95f93edb89b6750a4f36ad8ed7c0a2560 ([#28178](https://github.com/flutter/flutter/issues/28178))  * Roll engine to f45572e95f93edb89b6750a4f36ad8ed7c0a2560  * update goldens  * update goldens version|
|engine-flutter-autoroll <<EMAIL>>|f37b09a11 [fuchsia][SCN-1054] Map elevation onto -Z in Scenic ([flutter/engine#7834](https://github.com/flutter/engine/issues/7834)) ([#28036](https://github.com/flutter/flutter/issues/28036))   https://github.com/flutter/engine/compare/163a2fd5e140...f37b09a11b28  git log 163a2fd5e140d10a30645e73031054ef39105d87..f37b09a11b281b2a4e5dfcb45029069c3ac6e460 --no-merges --oneline f37b09a11 [fuchsia][SCN-1054] Map elevation onto -Z in Scenic ([flutter/engine#7834](https://github.com/flutter/engine/issues/7834))  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|engine-flutter-autoroll <<EMAIL>>|Roll engine 033f2072c315..163a2fd5e140 (26 commits) ([#28035](https://github.com/flutter/flutter/issues/28035))   https://github.com/flutter/engine/compare/033f2072c315...163a2fd5e140  git log 033f2072c315d48b4d7680d4cbbe856012b1259b..163a2fd5e140d10a30645e73031054ef39105d87 --no-merges --oneline 163a2fd5e Revert &[#34](https://github.com/flutter/flutter/issues/34);Android embedding refactor pr3 add remaining systemchannels ([#7738](https://github.com/flutter/flutter/issues/7738))&[#34](https://github.com/flutter/flutter/issues/34); ([flutter/engine#7849](https://github.com/flutter/engine/issues/7849)) 0d6ff1669 Shut down and restart the Dart VM as needed. ([flutter/engine#7832](https://github.com/flutter/engine/issues/7832)) 256db4bc2 Android embedding refactor pr3 add remaining systemchannels ([flutter/engine#7738](https://github.com/flutter/engine/issues/7738)) 02d7ca31d Don&[#39](https://github.com/flutter/flutter/issues/39);t call static method from instance variable ([flutter/engine#7841](https://github.com/flutter/engine/issues/7841)) 10cee612b Delete GL textures when they are released from the texture registry. ([flutter/engine#7836](https://github.com/flutter/engine/issues/7836)) 69e460607 Fix NullPointerException in SurfaceTextureRegistryEntry ([flutter/engine#7837](https://github.com/flutter/engine/issues/7837)) 9a965bc3d Fix NullPointerException in ResourceCleaner ([flutter/engine#7838](https://github.com/flutter/engine/issues/7838)) 2acd79480 Add fml::FileExists implementation for Windows ([flutter/engine#7845](https://github.com/flutter/engine/issues/7845)) fa38a985c Update buildroot to 7f64ff4928e to unblock Mac builds. ([flutter/engine#7846](https://github.com/flutter/engine/issues/7846)) 1ba329550 Add support for calling into other plugins from a background context on iOS ([flutter/engine#7843](https://github.com/flutter/engine/issues/7843)) df2fc973a Roll src/third_party/skia 154acd7a1374..be9aff25bddc (13 commits) ([flutter/engine#7842](https://github.com/flutter/engine/issues/7842)) 0ca1d1f31 Remove the Dart JIT snapshot data from AOT builds of the embedder library ([flutter/engine#7806](https://github.com/flutter/engine/issues/7806)) c51ea4157 Roll src/third_party/skia 7a74c7cb6da0..154acd7a1374 (1 commits) ([flutter/engine#7840](https://github.com/flutter/engine/issues/7840)) ecbaea10a Ensure to pass dill file after VM options for gen_snapshot ([flutter/engine#7839](https://github.com/flutter/engine/issues/7839)) 5d3f7145a Roll src/third_party/skia b00f7b34751b..7a74c7cb6da0 (1 commits) ([flutter/engine#7835](https://github.com/flutter/engine/issues/7835)) 733739938 Roll src/third_party/skia 9a88bee122f1..b00f7b34751b (24 commits) ([flutter/engine#7831](https://github.com/flutter/engine/issues/7831)) 6f318efe0 Move up ndk version that is being downloaded(old one no longer available) ([flutter/engine#7830](https://github.com/flutter/engine/issues/7830)) a8aa1ee06 Track flow id ([flutter/engine#7826](https://github.com/flutter/engine/issues/7826)) b1ce6b72f Roll src/third_party/skia a0dcd29f536b..9a88bee122f1 (4 commits) ([flutter/engine#7825](https://github.com/flutter/engine/issues/7825)) dbc166387 Roll src/third_party/skia 6152470dc69e..a0dcd29f536b (2 commits) ([flutter/engine#7824](https://github.com/flutter/engine/issues/7824)) 942677695 Roll src/third_party/skia 4037f7f5d8b4..6152470dc69e (1 commits) ([flutter/engine#7823](https://github.com/flutter/engine/issues/7823)) e2394ad77 Add flow events connecting pointer events to frames ([flutter/engine#7807](https://github.com/flutter/engine/issues/7807)) 0a5a7c56d Roll src/third_party/skia 09c01e9df260..4037f7f5d8b4 (3 commits) ([flutter/engine#7821](https://github.com/flutter/engine/issues/7821)) b867f4858 Roll src/third_party/skia 186669c4128b..09c01e9df260 (5 commits) ([flutter/engine#7820](https://github.com/flutter/engine/issues/7820)) af39e22c3 Fix tests that were committed after cirrus ran ([flutter/engine#7819](https://github.com/flutter/engine/issues/7819)) 1f2cbf792 Roll src/third_party/dart 0a7dcf17eb..c92d5ca288 (64 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/flutter-engine-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Stanislav Baranov <<EMAIL>>|Revert "Add support for binary compression of dynamic patches by the flutter tool. ([#27754](https://github.com/flutter/flutter/issues/27754))" ([#28031](https://github.com/flutter/flutter/issues/28031))  This reverts commit b47da6c2b79d90f33d73e9225b4b3bbbd0ffd99e.|
|Dan Field <<EMAIL>>|Reland automatic discovery of observatory port for iOS ([#27908](https://github.com/flutter/flutter/issues/27908))  * Discover port over mDNS  * Update BUILD.gn for tools|
|liyuqian <<EMAIL>>|Increase our build budget to 16ms ([#27709](https://github.com/flutter/flutter/issues/27709))  Because we now have 2 separate GPU and UI threads.|
|KyleWong <<EMAIL>>|Refactor local engine logic ([#27765](https://github.com/flutter/flutter/issues/27765))  * Refactor for iOS.  * For android_xxx_unopt_arm64&ios_xxx_unopt_arm, remove the _arm64/_arm suffix. dart generated in host_xxx_unopt/host_xxx has an arch of x86_64 which supports arm64/arm.  * Remove suffixes for various archs.|
|Jason Simmons <<EMAIL>>|Disable prefer_collection_literals in the analyzer until we can update to using set literals ([#28024](https://github.com/flutter/flutter/issues/28024))|
|Hans Muller <<EMAIL>>|Add extendBody parameter to Scaffold, body MediaQuery reflects BAB height ([#27973](https://github.com/flutter/flutter/issues/27973))|
|Jonah Williams <<EMAIL>>|remove accidentally commited libs ([#28011](https://github.com/flutter/flutter/issues/28011))|
|jslavitz <<EMAIL>>|Fixes switch vibration ([#27900](https://github.com/flutter/flutter/issues/27900))  * Fixes Cupertino switch vibration|
|Gary Qian <<EMAIL>>|Roll engine for [flutter/engine#7791](https://github.com/flutter/engine/issues/7791) - Add trailing whitespace tracking.|
|Kate Lovett <<EMAIL>>|Sample code for Icon class ([#27968](https://github.com/flutter/flutter/issues/27968))  * Added sample code for a simple Icon, ref: [#21136](https://github.com/flutter/flutter/issues/21136)  * Removed duplicate line.  * Fixed incoreect parameter.  * Update icon.dart  Removed empty prose and added better context for sample code. Changed sample code for better visibility in execution of default app themes.  * Alas, Trailing space.  Nixed trailing space in comments.|
|Michael Goderbauer <<EMAIL>>|Reapply "Fix overflow clipping/fading for text ([#27892](https://github.com/flutter/flutter/issues/27892))" ([#27980](https://github.com/flutter/flutter/issues/27980))  Fixes https://github.com/flutter/flutter/issues/25025|
|Todd Volkert <<EMAIL>>|Re-apply "Ensure all curves return 0 and 1 in .transform(t) when t=0/1" ([#28006](https://github.com/flutter/flutter/issues/28006))  Re-applies [#27409](https://github.com/flutter/flutter/issues/27409) by reverting [#27919](https://github.com/flutter/flutter/issues/27919)  The analyzer issue ([dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940)) has been temporarily worked around by virtue of [#27929](https://github.com/flutter/flutter/issues/27929)|
|Greg Spencer <<EMAIL>>|Fix Dashing rules to use new dartdoc CSS entity markers. ([#27818](https://github.com/flutter/flutter/issues/27818))  This fixes the offline Dash/Zeal docs generation to use the newly added dartdoc markers for different entity types to make it more exact when finding entity names.  Bumped Dartdoc to the next release.|
|Todd Volkert <<EMAIL>>|Ensure that Dart SDK dirs have appropriate permissions ([#26315](https://github.com/flutter/flutter/issues/26315))  This is a partial re-application of [#24669](https://github.com/flutter/flutter/issues/24669)  https://github.com/flutter/flutter/issues/24413|
|Greg Spencer <<EMAIL>>|Remove remaining "## Sample code" segments, and fix the snippet generator. ([#27793](https://github.com/flutter/flutter/issues/27793))  This converts all remaining "## Sample code" segments into snippets, and fixes the snippet generator to handle multiple snippets in the same dartdoc block properly.  I also generated, compiled, and ran each of the existing application samples, and fixed them up to be more useful and/or just run without errors.  This PR fixes these problems with examples:  1. Switching tabs in a snippet now works if there is more than one snippet in a single dartdoc block. 2. Generation of snippet code now works if there is more than one snippet. 3. Contrast of text and links in the code sample block has been improved to recommended levels. 4. Added five new snippet templates, including a "freeform" template to make it possible to show examples that need to change the app instantiation. 5. Fixed several examples to run properly, a couple by adding the "Scaffold" widget to the template, a couple by just fixing their code. 6. Fixed visual look of some of the samples when they run by placing many samples inside of a Scaffold. 7. In order to make it easier to run locally, changed the sample analyzer to remove the contents of the supplied temp directory before running, since having files that hang around is problematic (only a problem when running locally with the `--temp` argument). 8. Added a `SampleCheckerException` class, and handle sample checking exceptions more gracefully. 9. Deprecated the old "## Sample code" designation, and added enforcement for the deprecation. 10. Removed unnecessary `new` from templates (although they never appeared in the samples thanks to dartfmt, but still).  Fixes [#26398](https://github.com/flutter/flutter/issues/26398) Fixes [#27411](https://github.com/flutter/flutter/issues/27411)|
|Martin Kustermann <<EMAIL>>|Use double literals where a double type is expected ([#27929](https://github.com/flutter/flutter/issues/27929))  This makes the code more consistent but also fixes our flutter-analyze bot.  Issue https://github.com/dart-lang/sdk/issues/35940|
|Jonah Williams <<EMAIL>>|Add basic codegen app to be used for integration testing and benchmarks ([#27257](https://github.com/flutter/flutter/issues/27257))|
|Jonah Williams <<EMAIL>>|add ui.Window fallback to TestViewConfiguration ([#27987](https://github.com/flutter/flutter/issues/27987))|
|Jonah Williams <<EMAIL>>|Wire dart2js through flutter tool, add compilation test ([#27668](https://github.com/flutter/flutter/issues/27668))|

In `flutter/flutter` there were 647 commits

## Labels Addressed by Label

| Label | Number of Issues |
|--------|--------|
| **Work in progress (WIP)** | **1 issue with this label** |
|   | [flutter/engine#8274](https://github.com/flutter/engine/pull/8274) [ui] Add null check in FontWeight.lerp |
| **a: accessibility** | **1 issue with this label** |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
| **a: animation** | **5 issues with this label** |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#29069](https://github.com/flutter/flutter/pull/29069) Heroes and nested Navigators |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#28006](https://github.com/flutter/flutter/pull/28006) Re-apply "Ensure all curves return 0 and 1 in .transform(t) when t=0/1" |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **a: debugging** | **1 issue with this label** |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
| **a: desktop** | **5 issues with this label** |
|   | [flutter/flutter#30938](https://github.com/flutter/flutter/pull/30938) Update keycodes, fix a comment. |
|   | [flutter/flutter#30648](https://github.com/flutter/flutter/pull/30648) Allow downloading of desktop embedding artifacts |
|   | [flutter/flutter#30643](https://github.com/flutter/flutter/pull/30643) Add Form.onSaved |
|   | [flutter/flutter#29993](https://github.com/flutter/flutter/pull/29993) Adds the keyboard mapping for Linux |
|   | [flutter/flutter#29231](https://github.com/flutter/flutter/pull/29231) Adds macOS raw keyboard mapping |
| **a: existing-apps** | **1 issue with this label** |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
| **a: fidelity** | **3 issues with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
| **a: first hour** | **1 issue with this label** |
|   | [flutter/flutter#30562](https://github.com/flutter/flutter/pull/30562) Replace flutter.io with flutter.dev |
| **a: internationalization** | **1 issue with this label** |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
| **a: quality** | **1 issue with this label** |
|   | [flutter/flutter#30562](https://github.com/flutter/flutter/pull/30562) Replace flutter.io with flutter.dev |
| **a: tests** | **23 issues with this label** |
|   | [flutter/flutter#30857](https://github.com/flutter/flutter/pull/30857) Added support for authentication codes for the VM service. |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#30871](https://github.com/flutter/flutter/pull/30871) Update the upload key which seems to have trouble for some reason |
|   | [flutter/flutter#30747](https://github.com/flutter/flutter/pull/30747) Print warning if flutter drive is run in debug |
|   | [flutter/flutter#29669](https://github.com/flutter/flutter/pull/29669) Speed up CI via mojave-flutter image |
|   | [flutter/flutter#29467](https://github.com/flutter/flutter/pull/29467) prevent stream notifications from interfering with reload |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
|   | [flutter/flutter#29566](https://github.com/flutter/flutter/pull/29566) Manually roll engine to 5088735e5f |
|   | [flutter/flutter#29340](https://github.com/flutter/flutter/pull/29340) guard new formatter behind env var |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#29025](https://github.com/flutter/flutter/pull/29025) print system time on all mac builds |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#28852](https://github.com/flutter/flutter/pull/28852) Update macOS version in tests |
|   | [flutter/flutter#28638](https://github.com/flutter/flutter/pull/28638) Fix the test annotation used for test groups |
|   | [flutter/flutter#28555](https://github.com/flutter/flutter/pull/28555) disable dart2js test |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
|   | [flutter/flutter#27944](https://github.com/flutter/flutter/pull/27944) Add tests |
|   | [flutter/flutter#28373](https://github.com/flutter/flutter/pull/28373) Mark non-flaky test as such |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
|   | [flutter/flutter#27987](https://github.com/flutter/flutter/pull/27987) add ui.Window fallback to TestViewConfiguration |
| **a: text input** | **12 issues with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29395](https://github.com/flutter/flutter/pull/29395) Fix text selection when user is dragging in the opposite direction |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#28169](https://github.com/flutter/flutter/pull/28169) Add/rewrite tests for FocusScope. |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **a: typography** | **4 issues with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28751](https://github.com/flutter/flutter/pull/28751) Expose decorationThickness in TextStyle. Roll engine (12 commits) |
|   | [flutter/flutter#28398](https://github.com/flutter/flutter/pull/28398) fix red build for analysis |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
| **affects: dev experience** | **1 issue with this label** |
|   | [flutter/engine#7908](https://github.com/flutter/engine/pull/7908) Link dart:* sources into engine for debugger source support |
| **affects: docs** | **3 issues with this label** |
|   | [flutter/engine#8617](https://github.com/flutter/engine/pull/8617) Fixes a typo in comment |
|   | [flutter/engine#8472](https://github.com/flutter/engine/pull/8472) [Docs] Correcting link to contributing guide. |
|   | [flutter/engine#8295](https://github.com/flutter/engine/pull/8295) Fixing links between higher and lower classes. |
| **affects: engine** | **1 issue with this label** |
|   | [flutter/engine#7908](https://github.com/flutter/engine/pull/7908) Link dart:* sources into engine for debugger source support |
| **area-analyzer** | **1 issue with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **cla: no** | **2 issues with this label** |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
| **customer: dream (g3)** | **1 issue with this label** |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
| **customer: fuchsia** | **3 issues with this label** |
|   | [flutter/flutter#30458](https://github.com/flutter/flutter/pull/30458) [fuchsia] Fix isolate filter |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
|   | [flutter/flutter#29454](https://github.com/flutter/flutter/pull/29454) Update another SDK constraint |
| **d: api docs** | **36 issues with this label** |
|   | [flutter/flutter#30887](https://github.com/flutter/flutter/pull/30887) Add more dialog doc cross-reference |
|   | [flutter/flutter#30814](https://github.com/flutter/flutter/pull/30814) Fix StatefulWidget and StatelessWidget Sample Documentation |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30626](https://github.com/flutter/flutter/pull/30626) Add sample for ValueListenable |
|   | [flutter/flutter#30563](https://github.com/flutter/flutter/pull/30563) Fixed a typo in the Expanded API doc |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30452](https://github.com/flutter/flutter/pull/30452) Moar Videos |
|   | [flutter/flutter#30451](https://github.com/flutter/flutter/pull/30451) Bump dartdocs to 0.28.3 |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#22859](https://github.com/flutter/flutter/issues/22859) Provide links between dart:ui classes and their corresponding classes in higher layers |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#13637](https://github.com/flutter/flutter/issues/13637) Add images and examples to Align |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#28748](https://github.com/flutter/flutter/pull/28748) Revert "Remove the old HaTS implementation on API docs" |
|   | [flutter/flutter#28683](https://github.com/flutter/flutter/pull/28683) Remove the old HaTS implementation on API docs |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28017](https://github.com/flutter/flutter/pull/28017) Add more docs to BackdropFilter |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27305](https://github.com/flutter/flutter/pull/27305) Outline for survey implementation |
|   | [flutter/flutter#27793](https://github.com/flutter/flutter/pull/27793) Remove remaining "## Sample code" segments, and fix the snippet generator. |
| **d: examples** | **8 issues with this label** |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
| **e: desktop** | **5 issues with this label** |
|   | [flutter/flutter#31283](https://github.com/flutter/flutter/pull/31283) Add desktop workflows to doctor |
|   | [flutter/flutter#31229](https://github.com/flutter/flutter/pull/31229) Add flutter run support for linux and windows |
|   | [flutter/flutter#31277](https://github.com/flutter/flutter/pull/31277) pass track widget creation flag through to build script |
|   | [flutter/flutter#31218](https://github.com/flutter/flutter/pull/31218) Add run capability for macOS target |
|   | [flutter/flutter#31205](https://github.com/flutter/flutter/pull/31205) Add desktop projects and build commands (experimental) |
| **easy fix** | **3 issues with this label** |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#7738](https://github.com/flutter/flutter/issues/7738) Allow for more flexible unselected label color parameter in TabBar |
|   | [flutter/engine#8472](https://github.com/flutter/engine/pull/8472) [Docs] Correcting link to contributing guide. |
| **engine** | **3 issues with this label** |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
| **f: cupertino** | **20 issues with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#30946](https://github.com/flutter/flutter/pull/30946) Add some more cupertino icons |
|   | [flutter/flutter#29451](https://github.com/flutter/flutter/pull/29451) Added opacity to cupertino switch when disabled |
|   | [flutter/flutter#30862](https://github.com/flutter/flutter/pull/30862) CupertinoDatePicker initialDateTime accounts for minuteInterval  |
|   | [flutter/flutter#29822](https://github.com/flutter/flutter/pull/29822) Cupertino localization step 7: modularize material specific things out of gen_localizations.dart |
|   | [flutter/flutter#30527](https://github.com/flutter/flutter/pull/30527) Cupertino localization step 11: add more translation clarifications in the instructions |
|   | [flutter/flutter#29767](https://github.com/flutter/flutter/pull/29767) Cupertino localization step 6: add a GlobalCupertinoLocalizations base class with date time formatting |
|   | [flutter/flutter#29708](https://github.com/flutter/flutter/pull/29708) Cupertino localization step 5: add french arb as translated example |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29821](https://github.com/flutter/flutter/pull/29821) Cupertino localization step 1.5: fix a resource mismatch in cupertino_en.arb |
|   | [flutter/flutter#29946](https://github.com/flutter/flutter/pull/29946) Let CupertinoPageScaffold have tap status bar to scroll to top |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#29943](https://github.com/flutter/flutter/pull/29943) Remove unwanted gap between navigation bar and safe area's child |
|   | [flutter/flutter#29630](https://github.com/flutter/flutter/pull/29630) Add heart shapes to CupertinoIcons |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#29463](https://github.com/flutter/flutter/pull/29463) Make real JSON in arb |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28855](https://github.com/flutter/flutter/pull/28855) Move material iOS back swipe test to material |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **f: gestures** | **6 issues with this label** |
|   | [flutter/flutter#28749](https://github.com/flutter/flutter/pull/28749) Add minimum time gap requirement to double tap |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28546](https://github.com/flutter/flutter/pull/28546) Call onTapCancel when down pointer gets cancelled |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
| **f: material design** | **44 issues with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
|   | [flutter/flutter#30746](https://github.com/flutter/flutter/pull/30746) Baseline Aligned Row |
|   | [flutter/flutter#30793](https://github.com/flutter/flutter/pull/30793) Revert "Add Form.onSaved" |
|   | [flutter/flutter#30525](https://github.com/flutter/flutter/pull/30525) Fix cursor outside of input width |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30497](https://github.com/flutter/flutter/pull/30497) Add confirmDismiss example to flutter_gallery |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#29413](https://github.com/flutter/flutter/pull/29413) Fix MaterialApp's _navigatorObserver when only builder used |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30353](https://github.com/flutter/flutter/pull/30353) Fix minor typo |
|   | [flutter/flutter#30212](https://github.com/flutter/flutter/pull/30212) Added assert to prevent complete ListTile trailing/leading horizontal expansion |
|   | [flutter/flutter#30123](https://github.com/flutter/flutter/pull/30123) Fix OutlineInputBorder crash |
|   | [flutter/flutter#30219](https://github.com/flutter/flutter/pull/30219) Added helpful Material assert message |
|   | [flutter/flutter#29771](https://github.com/flutter/flutter/pull/29771) Set Max Height for ListTile trailing and leading widgets |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#29811](https://github.com/flutter/flutter/pull/29811) TextField Validator Height Docs |
|   | [flutter/flutter#29980](https://github.com/flutter/flutter/pull/29980) Fix issue with account drawer header arrow rotating when setState is called |
|   | [flutter/flutter#29563](https://github.com/flutter/flutter/pull/29563) Avoid flickering while dragging to select text |
|   | [flutter/flutter#29138](https://github.com/flutter/flutter/pull/29138) Update DropdownButton underline to be customizable |
|   | [flutter/flutter#28735](https://github.com/flutter/flutter/pull/28735) [Material] Create a FloatingActionButton ThemeData and honor it within the FloatingActionButton |
|   | [flutter/flutter#29564](https://github.com/flutter/flutter/pull/29564) Update progress indicator API docs |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#29183](https://github.com/flutter/flutter/pull/29183) Implement labelPadding configuration in TabBarTheme  |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29175](https://github.com/flutter/flutter/pull/29175) Ensure that animated pairs of Tabs TextStyles have matching inherited values |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28013](https://github.com/flutter/flutter/pull/28013) [Material] Unit test for skipping Slider tick mark due to overdensity |
|   | [flutter/flutter#21834](https://github.com/flutter/flutter/pull/21834)  Add shapeBorder option on App Bar |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28371](https://github.com/flutter/flutter/pull/28371) Ensure that the DropdownButton menu respects its parents bounds |
|   | [flutter/flutter#28214](https://github.com/flutter/flutter/pull/28214) [Material] Add the ability to theme trailing app bar actions independently from leading |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28163](https://github.com/flutter/flutter/pull/28163) [Material] Add ability to set shadow color and selected shadow color for chips and for chip themes |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
|   | [flutter/flutter#28216](https://github.com/flutter/flutter/pull/28216) Fix 'to to' |
|   | [flutter/flutter#27973](https://github.com/flutter/flutter/pull/27973) Add extendBody parameter to Scaffold, body MediaQuery reflects BAB height |
| **f: routes** | **1 issue with this label** |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
| **f: scrolling** | **2 issues with this label** |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
| **framework** | **144 issues with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
|   | [flutter/flutter#30422](https://github.com/flutter/flutter/pull/30422) Commit a navigator.pop as soon as the back swipe is lifted |
|   | [flutter/flutter#30946](https://github.com/flutter/flutter/pull/30946) Add some more cupertino icons |
|   | [flutter/flutter#31159](https://github.com/flutter/flutter/pull/31159) Revert "Use full height of the glyph for caret height on Android" |
|   | [flutter/flutter#30938](https://github.com/flutter/flutter/pull/30938) Update keycodes, fix a comment. |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30809](https://github.com/flutter/flutter/pull/30809)  Fix issue 23527: Exception: RenderViewport exceeded its maximum numb… |
|   | [flutter/flutter#30746](https://github.com/flutter/flutter/pull/30746) Baseline Aligned Row |
|   | [flutter/flutter#30880](https://github.com/flutter/flutter/pull/30880) Let `sliver.dart` `_createErrorWidget` work with other Widgets |
|   | [flutter/flutter#30862](https://github.com/flutter/flutter/pull/30862) CupertinoDatePicker initialDateTime accounts for minuteInterval  |
|   | [flutter/flutter#30876](https://github.com/flutter/flutter/pull/30876) Simplify toImage future handling |
|   | [flutter/flutter#30470](https://github.com/flutter/flutter/pull/30470) Fixed Table flex column layout error #30437 |
|   | [flutter/flutter#30667](https://github.com/flutter/flutter/pull/30667) Fix additional @mustCallSuper indirect overrides and mixins |
|   | [flutter/flutter#30643](https://github.com/flutter/flutter/pull/30643) Add Form.onSaved |
|   | [flutter/flutter#30793](https://github.com/flutter/flutter/pull/30793) Revert "Add Form.onSaved" |
|   | [flutter/flutter#30414](https://github.com/flutter/flutter/pull/30414) Remove pressure customization from some pointer events |
|   | [flutter/flutter#30525](https://github.com/flutter/flutter/pull/30525) Fix cursor outside of input width |
|   | [flutter/flutter#29822](https://github.com/flutter/flutter/pull/29822) Cupertino localization step 7: modularize material specific things out of gen_localizations.dart |
|   | [flutter/flutter#30814](https://github.com/flutter/flutter/pull/30814) Fix StatefulWidget and StatelessWidget Sample Documentation |
|   | [flutter/flutter#30527](https://github.com/flutter/flutter/pull/30527) Cupertino localization step 11: add more translation clarifications in the instructions |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30760](https://github.com/flutter/flutter/pull/30760) fix cast NPE in invokeListMethod and invokeMapMethod |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30640](https://github.com/flutter/flutter/pull/30640) Add `const Border.uniformSide()` |
|   | [flutter/flutter#30644](https://github.com/flutter/flutter/pull/30644) Make FormField._validate() return void |
|   | [flutter/flutter#30645](https://github.com/flutter/flutter/pull/30645) Add docs to FormFieldValidator |
|   | [flutter/flutter#30626](https://github.com/flutter/flutter/pull/30626) Add sample for ValueListenable |
|   | [flutter/flutter#30563](https://github.com/flutter/flutter/pull/30563) Fixed a typo in the Expanded API doc |
|   | [flutter/flutter#30513](https://github.com/flutter/flutter/pull/30513) Fix issue 21640: Assertion Error : '_listenerAttached': is not true |
|   | [flutter/flutter#30305](https://github.com/flutter/flutter/pull/30305) shorter nullable list duplications |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#29708](https://github.com/flutter/flutter/pull/29708) Cupertino localization step 5: add french arb as translated example |
|   | [flutter/flutter#29413](https://github.com/flutter/flutter/pull/29413) Fix MaterialApp's _navigatorObserver when only builder used |
|   | [flutter/flutter#29998](https://github.com/flutter/flutter/pull/29998) Fix edge cases of PointerEventConverter |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30275](https://github.com/flutter/flutter/pull/30275) Implement compute for async function (#16265) |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#30463](https://github.com/flutter/flutter/pull/30463) Revert "Error message for setting shaderWarmUp too late (#30145)" |
|   | [flutter/flutter#30452](https://github.com/flutter/flutter/pull/30452) Moar Videos |
|   | [flutter/flutter#30451](https://github.com/flutter/flutter/pull/30451) Bump dartdocs to 0.28.3 |
|   | [flutter/flutter#30058](https://github.com/flutter/flutter/pull/30058) Draggable Scrollable sheet |
|   | [flutter/flutter#30353](https://github.com/flutter/flutter/pull/30353) Fix minor typo |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#30304](https://github.com/flutter/flutter/pull/30304) no need .toList() before .join() |
|   | [flutter/flutter#30212](https://github.com/flutter/flutter/pull/30212) Added assert to prevent complete ListTile trailing/leading horizontal expansion |
|   | [flutter/flutter#30227](https://github.com/flutter/flutter/pull/30227) Simplify logic of TapGestureRecognizer |
|   | [flutter/flutter#30123](https://github.com/flutter/flutter/pull/30123) Fix OutlineInputBorder crash |
|   | [flutter/flutter#30219](https://github.com/flutter/flutter/pull/30219) Added helpful Material assert message |
|   | [flutter/flutter#29771](https://github.com/flutter/flutter/pull/29771) Set Max Height for ListTile trailing and leading widgets |
|   | [flutter/flutter#29993](https://github.com/flutter/flutter/pull/29993) Adds the keyboard mapping for Linux |
|   | [flutter/flutter#29860](https://github.com/flutter/flutter/pull/29860) Move binarySearch to foundation. |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29821](https://github.com/flutter/flutter/pull/29821) Cupertino localization step 1.5: fix a resource mismatch in cupertino_en.arb |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#29811](https://github.com/flutter/flutter/pull/29811) TextField Validator Height Docs |
|   | [flutter/flutter#29943](https://github.com/flutter/flutter/pull/29943) Remove unwanted gap between navigation bar and safe area's child |
|   | [flutter/flutter#22859](https://github.com/flutter/flutter/issues/22859) Provide links between dart:ui classes and their corresponding classes in higher layers |
|   | [flutter/flutter#29929](https://github.com/flutter/flutter/pull/29929) Remove tranparent paint hack from BackdropFilter |
|   | [flutter/flutter#29630](https://github.com/flutter/flutter/pull/29630) Add heart shapes to CupertinoIcons |
|   | [flutter/flutter#29563](https://github.com/flutter/flutter/pull/29563) Avoid flickering while dragging to select text |
|   | [flutter/flutter#29701](https://github.com/flutter/flutter/pull/29701) Reland composite physical layers for all platforms |
|   | [flutter/flutter#28735](https://github.com/flutter/flutter/pull/28735) [Material] Create a FloatingActionButton ThemeData and honor it within the FloatingActionButton |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
|   | [flutter/flutter#29623](https://github.com/flutter/flutter/pull/29623) Revert "Reland composite physical layers on all platforms" |
|   | [flutter/flutter#29564](https://github.com/flutter/flutter/pull/29564) Update progress indicator API docs |
|   | [flutter/flutter#29566](https://github.com/flutter/flutter/pull/29566) Manually roll engine to 5088735e5f |
|   | [flutter/flutter#29463](https://github.com/flutter/flutter/pull/29463) Make real JSON in arb |
|   | [flutter/flutter#29231](https://github.com/flutter/flutter/pull/29231) Adds macOS raw keyboard mapping |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#13637](https://github.com/flutter/flutter/issues/13637) Add images and examples to Align |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29395](https://github.com/flutter/flutter/pull/29395) Fix text selection when user is dragging in the opposite direction |
|   | [flutter/flutter#28470](https://github.com/flutter/flutter/pull/28470) Throw assertion error when a Hero has a Hero child. |
|   | [flutter/flutter#29183](https://github.com/flutter/flutter/pull/29183) Implement labelPadding configuration in TabBarTheme  |
|   | [flutter/flutter#29329](https://github.com/flutter/flutter/pull/29329) Error message when TextSelectionOverlay finds no Overlay |
|   | [flutter/flutter#28919](https://github.com/flutter/flutter/pull/28919) Always composite PhysicalModels |
|   | [flutter/flutter#28751](https://github.com/flutter/flutter/pull/28751) Expose decorationThickness in TextStyle. Roll engine (12 commits) |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29179](https://github.com/flutter/flutter/pull/29179) No image shader caching in default shader warm-up |
|   | [flutter/flutter#29175](https://github.com/flutter/flutter/pull/29175) Ensure that animated pairs of Tabs TextStyles have matching inherited values |
|   | [flutter/flutter#29069](https://github.com/flutter/flutter/pull/29069) Heroes and nested Navigators |
|   | [flutter/flutter#28953](https://github.com/flutter/flutter/pull/28953) Include platformViewId in semantics tree |
|   | [flutter/flutter#29054](https://github.com/flutter/flutter/pull/29054) Deprecate profile() |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#29051](https://github.com/flutter/flutter/pull/29051) fix block formatting |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28169](https://github.com/flutter/flutter/pull/28169) Add/rewrite tests for FocusScope. |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#28951](https://github.com/flutter/flutter/pull/28951) Quick fix for shader warm up |
|   | [flutter/flutter#28749](https://github.com/flutter/flutter/pull/28749) Add minimum time gap requirement to double tap |
|   | [flutter/flutter#28855](https://github.com/flutter/flutter/pull/28855) Move material iOS back swipe test to material |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28944](https://github.com/flutter/flutter/pull/28944) fix missing variable name |
|   | [flutter/flutter#27811](https://github.com/flutter/flutter/pull/27811) set literal conversions |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28809](https://github.com/flutter/flutter/pull/28809) fix some formatting issues |
|   | [flutter/flutter#28666](https://github.com/flutter/flutter/pull/28666) Make RenderUiKitView reject absorbed touch events |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#28661](https://github.com/flutter/flutter/pull/28661) Use a simpler implementation of Diagnosticable.toString when running in profile/release mode |
|   | [flutter/flutter#28736](https://github.com/flutter/flutter/pull/28736) Avoid the overhead of instantiating a generator in paintImage |
|   | [flutter/flutter#28638](https://github.com/flutter/flutter/pull/28638) Fix the test annotation used for test groups |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28709](https://github.com/flutter/flutter/pull/28709) improve error messages on Text constructors |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28546](https://github.com/flutter/flutter/pull/28546) Call onTapCancel when down pointer gets cancelled |
|   | [flutter/flutter#28537](https://github.com/flutter/flutter/pull/28537) Reland "Shader warm up (#27660)" |
|   | [flutter/flutter#28013](https://github.com/flutter/flutter/pull/28013) [Material] Unit test for skipping Slider tick mark due to overdensity |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
|   | [flutter/flutter#21834](https://github.com/flutter/flutter/pull/21834)  Add shapeBorder option on App Bar |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
|   | [flutter/flutter#28371](https://github.com/flutter/flutter/pull/28371) Ensure that the DropdownButton menu respects its parents bounds |
|   | [flutter/flutter#28214](https://github.com/flutter/flutter/pull/28214) [Material] Add the ability to theme trailing app bar actions independently from leading |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28017](https://github.com/flutter/flutter/pull/28017) Add more docs to BackdropFilter |
|   | [flutter/flutter#28398](https://github.com/flutter/flutter/pull/28398) fix red build for analysis |
|   | [flutter/flutter#28163](https://github.com/flutter/flutter/pull/28163) [Material] Add ability to set shadow color and selected shadow color for chips and for chip themes |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
|   | [flutter/flutter#28172](https://github.com/flutter/flutter/pull/28172) Add backgroundColor argument to TextStyle for convenience |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
|   | [flutter/flutter#27953](https://github.com/flutter/flutter/pull/27953) Add outer try block for obtainKey errors. Add docs. |
|   | [flutter/flutter#28216](https://github.com/flutter/flutter/pull/28216) Fix 'to to' |
|   | [flutter/flutter#7738](https://github.com/flutter/flutter/issues/7738) Allow for more flexible unselected label color parameter in TabBar |
|   | [flutter/flutter#27973](https://github.com/flutter/flutter/pull/27973) Add extendBody parameter to Scaffold, body MediaQuery reflects BAB height |
|   | [flutter/flutter#28006](https://github.com/flutter/flutter/pull/28006) Re-apply "Ensure all curves return 0 and 1 in .transform(t) when t=0/1" |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **p1-high** | **1 issue with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **platform-ios** | **1 issue with this label** |
|   | [flutter/engine#8202](https://github.com/flutter/engine/pull/8202) [platform_view] iOSP platformView composition optimize. |
| **prod: API break** | **1 issue with this label** |
|   | [flutter/engine#8274](https://github.com/flutter/engine/pull/8274) [ui] Add null check in FontWeight.lerp |
| **severe: API break** | **6 issues with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#30414](https://github.com/flutter/flutter/pull/30414) Remove pressure customization from some pointer events |
|   | [flutter/flutter#28919](https://github.com/flutter/flutter/pull/28919) Always composite PhysicalModels |
|   | [flutter/flutter#28953](https://github.com/flutter/flutter/pull/28953) Include platformViewId in semantics tree |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **severe: crash** | **1 issue with this label** |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
| **severe: new feature** | **1 issue with this label** |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
| **severe: performance** | **5 issues with this label** |
|   | [flutter/flutter#30990](https://github.com/flutter/flutter/pull/30990) Allow profile widget builds in profile mode |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28537](https://github.com/flutter/flutter/pull/28537) Reland "Shader warm up (#27660)" |
| **severe: regression** | **3 issues with this label** |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
| **t: flutter doctor** | **1 issue with this label** |
|   | [flutter/flutter#8094](https://github.com/flutter/flutter/issues/8094) Flutter doesn't find xcode 8.2 |
| **t: flutter driver** | **3 issues with this label** |
|   | [flutter/flutter#30747](https://github.com/flutter/flutter/pull/30747) Print warning if flutter drive is run in debug |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
|   | [flutter/flutter#27709](https://github.com/flutter/flutter/pull/27709) Increase our build budget to 16ms |
| **t: gradle** | **4 issues with this label** |
|   | [flutter/flutter#28097](https://github.com/flutter/flutter/pull/28097) Allow for gradle downloading missing SDK assets |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#27754](https://github.com/flutter/flutter/pull/27754) Add support for binary compression of dynamic patches by the flutter tool. |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
| **t: hot reload** | **2 issues with this label** |
|   | [flutter/flutter#29693](https://github.com/flutter/flutter/pull/29693) Use source list from the compiler to track invalidated files for hot reload. |
|   | [flutter/flutter#28152](https://github.com/flutter/flutter/pull/28152) Improve hot reload performance |
| **t: xcode** | **1 issue with this label** |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
| **team** | **55 issues with this label** |
|   | [flutter/flutter#30990](https://github.com/flutter/flutter/pull/30990) Allow profile widget builds in profile mode |
|   | [flutter/flutter#30832](https://github.com/flutter/flutter/pull/30832) Bump Android build tools to 28.0.3 in Dockerfile |
|   | [flutter/flutter#30871](https://github.com/flutter/flutter/pull/30871) Update the upload key which seems to have trouble for some reason |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#25164](https://github.com/flutter/flutter/pull/25164) Pan and zoom gallery demo |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30460](https://github.com/flutter/flutter/pull/30460) Fix gallery API doc URL launcher |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#29621](https://github.com/flutter/flutter/pull/29621) Update PULL_REQUEST_TEMPLATE.md |
|   | [flutter/flutter#30019](https://github.com/flutter/flutter/pull/30019) Update to latest matcher |
|   | [flutter/flutter#29908](https://github.com/flutter/flutter/pull/29908) Update Twitter handle @flutterio -> @FlutterDev |
|   | [flutter/flutter#29632](https://github.com/flutter/flutter/pull/29632) Enable platform views for Flutter Gallery on iOS. |
|   | [flutter/flutter#29669](https://github.com/flutter/flutter/pull/29669) Speed up CI via mojave-flutter image |
|   | [flutter/flutter#29467](https://github.com/flutter/flutter/pull/29467) prevent stream notifications from interfering with reload |
|   | [flutter/flutter#29619](https://github.com/flutter/flutter/pull/29619) make literals const for @immutable constructors |
|   | [flutter/flutter#29454](https://github.com/flutter/flutter/pull/29454) Update another SDK constraint |
|   | [flutter/flutter#29340](https://github.com/flutter/flutter/pull/29340) guard new formatter behind env var |
|   | [flutter/flutter#29229](https://github.com/flutter/flutter/pull/29229) Install JDK and Android SDK only for integration tests |
|   | [flutter/flutter#29126](https://github.com/flutter/flutter/pull/29126) Cause `flutter analyze` to fail if the analysis server experienced an error. |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#29051](https://github.com/flutter/flutter/pull/29051) fix block formatting |
|   | [flutter/flutter#29025](https://github.com/flutter/flutter/pull/29025) print system time on all mac builds |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#27811](https://github.com/flutter/flutter/pull/27811) set literal conversions |
|   | [flutter/flutter#28852](https://github.com/flutter/flutter/pull/28852) Update macOS version in tests |
|   | [flutter/flutter#28809](https://github.com/flutter/flutter/pull/28809) fix some formatting issues |
|   | [flutter/flutter#28748](https://github.com/flutter/flutter/pull/28748) Revert "Remove the old HaTS implementation on API docs" |
|   | [flutter/flutter#28658](https://github.com/flutter/flutter/pull/28658) Include git output into error message from channel command |
|   | [flutter/flutter#28673](https://github.com/flutter/flutter/pull/28673) Add missing trailing commas |
|   | [flutter/flutter#28555](https://github.com/flutter/flutter/pull/28555) disable dart2js test |
|   | [flutter/flutter#28369](https://github.com/flutter/flutter/pull/28369) Add LICENSE test to presubmit checks |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
|   | [flutter/flutter#28343](https://github.com/flutter/flutter/pull/28343) pass --skip-build-script-checks and remove module usage |
|   | [flutter/flutter#28334](https://github.com/flutter/flutter/pull/28334) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#28482](https://github.com/flutter/flutter/pull/28482) Fuschia -> Fuchsia |
|   | [flutter/flutter#28175](https://github.com/flutter/flutter/pull/28175) Only call Activity.reportFullyDrawn on Lollipop or above |
|   | [flutter/flutter#28370](https://github.com/flutter/flutter/pull/28370) Removed trailing whitespace from the end of the CI config |
|   | [flutter/flutter#28373](https://github.com/flutter/flutter/pull/28373) Mark non-flaky test as such |
|   | [flutter/flutter#28125](https://github.com/flutter/flutter/pull/28125) [Gallery] Fortnightly demo moved from flutter/samples. |
|   | [flutter/flutter#28400](https://github.com/flutter/flutter/pull/28400) update packages |
|   | [flutter/flutter#28215](https://github.com/flutter/flutter/pull/28215) Minor UI tweaks to Cards demo based on internal feedback |
|   | [flutter/flutter#28291](https://github.com/flutter/flutter/pull/28291) Reland #27754, now that bsdiff has moved to flutter/packages. |
|   | [flutter/flutter#28171](https://github.com/flutter/flutter/pull/28171) deploy to .dev firebase projects. |
|   | [flutter/flutter#28281](https://github.com/flutter/flutter/pull/28281) Reduce memory benchmark perf tests outer loop to 10. |
|   | [flutter/flutter#27261](https://github.com/flutter/flutter/pull/27261) format parameter list |
|   | [flutter/flutter#24889](https://github.com/flutter/flutter/pull/24889) Update AUTHORS |
|   | [flutter/flutter#28238](https://github.com/flutter/flutter/pull/28238) Remove set literal syntax |
|   | [flutter/flutter#28235](https://github.com/flutter/flutter/pull/28235) Remove line breaks from PR template |
|   | [flutter/flutter#26376](https://github.com/flutter/flutter/pull/26376) removed image with 404 |
|   | [flutter/flutter#28224](https://github.com/flutter/flutter/pull/28224) Adds a template for Pull Requests |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27305](https://github.com/flutter/flutter/pull/27305) Outline for survey implementation |
|   | [flutter/flutter#28024](https://github.com/flutter/flutter/pull/28024) Disable prefer_collection_literals in the analyzer until we can update to using set literals |
|   | [flutter/flutter#27793](https://github.com/flutter/flutter/pull/27793) Remove remaining "## Sample code" segments, and fix the snippet generator. |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **team: flakes** | **1 issue with this label** |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
| **team: gallery** | **11 issues with this label** |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30497](https://github.com/flutter/flutter/pull/30497) Add confirmDismiss example to flutter_gallery |
|   | [flutter/flutter#25164](https://github.com/flutter/flutter/pull/25164) Pan and zoom gallery demo |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30460](https://github.com/flutter/flutter/pull/30460) Fix gallery API doc URL launcher |
|   | [flutter/flutter#29908](https://github.com/flutter/flutter/pull/29908) Update Twitter handle @flutterio -> @FlutterDev |
|   | [flutter/flutter#29632](https://github.com/flutter/flutter/pull/29632) Enable platform views for Flutter Gallery on iOS. |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28175](https://github.com/flutter/flutter/pull/28175) Only call Activity.reportFullyDrawn on Lollipop or above |
|   | [flutter/flutter#28125](https://github.com/flutter/flutter/pull/28125) [Gallery] Fortnightly demo moved from flutter/samples. |
|   | [flutter/flutter#28215](https://github.com/flutter/flutter/pull/28215) Minor UI tweaks to Cards demo based on internal feedback |
| **tool** | **72 issues with this label** |
|   | [flutter/flutter#30857](https://github.com/flutter/flutter/pull/30857) Added support for authentication codes for the VM service. |
|   | [flutter/flutter#31283](https://github.com/flutter/flutter/pull/31283) Add desktop workflows to doctor |
|   | [flutter/flutter#31229](https://github.com/flutter/flutter/pull/31229) Add flutter run support for linux and windows |
|   | [flutter/flutter#31277](https://github.com/flutter/flutter/pull/31277) pass track widget creation flag through to build script |
|   | [flutter/flutter#31171](https://github.com/flutter/flutter/pull/31171) Allow disabling all fingerprint caches via environment variable |
|   | [flutter/flutter#31205](https://github.com/flutter/flutter/pull/31205) Add desktop projects and build commands (experimental) |
|   | [flutter/flutter#31073](https://github.com/flutter/flutter/pull/31073) Fuchsia step 1: add SDK version file and artifact download |
|   | [flutter/flutter#31064](https://github.com/flutter/flutter/pull/31064) Add sorting to flutter version command |
|   | [flutter/flutter#31063](https://github.com/flutter/flutter/pull/31063) Download and handle product version of flutter patched sdk |
|   | [flutter/flutter#31074](https://github.com/flutter/flutter/pull/31074) make flutterProject option of CoverageCollector optional |
|   | [flutter/flutter#30818](https://github.com/flutter/flutter/pull/30818) New flag to `flutter drive` to skip installing fresh app on device |
|   | [flutter/flutter#30811](https://github.com/flutter/flutter/pull/30811) Make coverage, like, really fast |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#30867](https://github.com/flutter/flutter/pull/30867) Add toggle for debugProfileWidgetBuilds |
|   | [flutter/flutter#30648](https://github.com/flutter/flutter/pull/30648) Allow downloading of desktop embedding artifacts |
|   | [flutter/flutter#30235](https://github.com/flutter/flutter/pull/30235) Warn on uncomitted changes |
|   | [flutter/flutter#30115](https://github.com/flutter/flutter/pull/30115) Forward missing pub commands |
|   | [flutter/flutter#30578](https://github.com/flutter/flutter/pull/30578) Mark ios-deploy version 2.0.0 as bad |
|   | [flutter/flutter#30458](https://github.com/flutter/flutter/pull/30458) [fuchsia] Fix isolate filter |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#30218](https://github.com/flutter/flutter/pull/30218) [fuchsia_tester] Plumb through the location of icudtl |
|   | [flutter/flutter#30075](https://github.com/flutter/flutter/pull/30075) Ensure that flutter run/drive/test/update_packages only downloads required artifacts |
|   | [flutter/flutter#30071](https://github.com/flutter/flutter/pull/30071) Move spinner `_defaultSlowWarning` message to a new line |
|   | [flutter/flutter#29938](https://github.com/flutter/flutter/pull/29938) Pass FLUTTER_TOOL_ARGS to snapshot command. |
|   | [flutter/flutter#29768](https://github.com/flutter/flutter/pull/29768) Directly depend on frontend_server tool in fuchsia_attach |
|   | [flutter/flutter#29783](https://github.com/flutter/flutter/pull/29783) Fix cache location, artifacts, and re-enable dart2js test |
|   | [flutter/flutter#29818](https://github.com/flutter/flutter/pull/29818) dont fail build if codegen fails |
|   | [flutter/flutter#29192](https://github.com/flutter/flutter/pull/29192) Update upgrade to rebase and stash local changes. |
|   | [flutter/flutter#29764](https://github.com/flutter/flutter/pull/29764) update fuchsia-attach to configure dev_finder location |
|   | [flutter/flutter#29721](https://github.com/flutter/flutter/pull/29721) Use Dart version in script cache check |
|   | [flutter/flutter#29399](https://github.com/flutter/flutter/pull/29399) Enable code generation features in tool (via opt-in) |
|   | [flutter/flutter#29699](https://github.com/flutter/flutter/pull/29699) Fix more tests for ANSI terminals |
|   | [flutter/flutter#29469](https://github.com/flutter/flutter/pull/29469) fix asset reloading |
|   | [flutter/flutter#29693](https://github.com/flutter/flutter/pull/29693) Use source list from the compiler to track invalidated files for hot reload. |
|   | [flutter/flutter#29434](https://github.com/flutter/flutter/pull/29434) Add builders and engine hash to fingerprint |
|   | [flutter/flutter#29625](https://github.com/flutter/flutter/pull/29625) Fix typo in flutter_tools |
|   | [flutter/flutter#29604](https://github.com/flutter/flutter/pull/29604) added friendlier error for invalid AndroidManifest.xml |
|   | [flutter/flutter#29461](https://github.com/flutter/flutter/pull/29461) Remove explicit frame schedule |
|   | [flutter/flutter#29048](https://github.com/flutter/flutter/pull/29048) Use async execution for xcodebuild commands |
|   | [flutter/flutter#29528](https://github.com/flutter/flutter/pull/29528) Ensure that assumed formatting of properties file is correct |
|   | [flutter/flutter#27749](https://github.com/flutter/flutter/pull/27749) Switch flutter_tools from script to app-jit snapshot. |
|   | [flutter/flutter#28152](https://github.com/flutter/flutter/pull/28152) Improve hot reload performance |
|   | [flutter/flutter#29258](https://github.com/flutter/flutter/pull/29258) Add dump-shader-skp flag to flutter tools |
|   | [flutter/flutter#29134](https://github.com/flutter/flutter/pull/29134) Properly escape Android SDK Manager path in error message |
|   | [flutter/flutter#29171](https://github.com/flutter/flutter/pull/29171) Only run codegen at start of flutter_test |
|   | [flutter/flutter#28938](https://github.com/flutter/flutter/pull/28938) Add a `flutter create --list-samples` command |
|   | [flutter/flutter#29126](https://github.com/flutter/flutter/pull/29126) Cause `flutter analyze` to fail if the analysis server experienced an error. |
|   | [flutter/flutter#29062](https://github.com/flutter/flutter/pull/29062) fix windows codegen |
|   | [flutter/flutter#28603](https://github.com/flutter/flutter/pull/28603) select ResidentCompiler during FlutterDevice initialization |
|   | [flutter/flutter#28863](https://github.com/flutter/flutter/pull/28863) Fall-back to platform tools in Android SDK detection logic. |
|   | [flutter/flutter#28658](https://github.com/flutter/flutter/pull/28658) Include git output into error message from channel command |
|   | [flutter/flutter#28356](https://github.com/flutter/flutter/pull/28356) Log pub return code on failure on Windows |
|   | [flutter/flutter#28386](https://github.com/flutter/flutter/pull/28386) remove personal repo and replace with trivial example for smoke test |
|   | [flutter/flutter#28477](https://github.com/flutter/flutter/pull/28477) Fix backspace and clear length in AnsiStatus |
|   | [flutter/flutter#28302](https://github.com/flutter/flutter/pull/28302) Add basic web device and run support |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
|   | [flutter/flutter#28334](https://github.com/flutter/flutter/pull/28334) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#27944](https://github.com/flutter/flutter/pull/27944) Add tests |
|   | [flutter/flutter#28097](https://github.com/flutter/flutter/pull/28097) Allow for gradle downloading missing SDK assets |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#28352](https://github.com/flutter/flutter/pull/28352) only perform multi-root mapping if there are multiple roots |
|   | [flutter/flutter#28101](https://github.com/flutter/flutter/pull/28101) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#28027](https://github.com/flutter/flutter/pull/28027) Remove extra slash from builder that is not handled on windows |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27754](https://github.com/flutter/flutter/pull/27754) Add support for binary compression of dynamic patches by the flutter tool. |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/flutter#27709](https://github.com/flutter/flutter/pull/27709) Increase our build budget to 16ms |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
|   | [flutter/flutter#28011](https://github.com/flutter/flutter/pull/28011) Remove accidentally committed libs |
|   | [flutter/flutter#27987](https://github.com/flutter/flutter/pull/27987) add ui.Window fallback to TestViewConfiguration |
|   | [flutter/flutter#27668](https://github.com/flutter/flutter/pull/27668) Wire dart2js through flutter tool, add compilation test |
| **type-bug** | **1 issue with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **waiting for PR to land (fixed)** | **1 issue with this label** |
|   | [flutter/flutter#7999](https://github.com/flutter/flutter/issues/7999) Consider renaming the Design Principles guide |
| **waiting for tree to go green** | **37 issues with this label** |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30305](https://github.com/flutter/flutter/pull/30305) shorter nullable list duplications |
|   | [flutter/flutter#30535](https://github.com/flutter/flutter/pull/30535) Correctly synthesise event buttons |
|   | [flutter/flutter#29998](https://github.com/flutter/flutter/pull/29998) Fix edge cases of PointerEventConverter |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#30463](https://github.com/flutter/flutter/pull/30463) Revert "Error message for setting shaderWarmUp too late (#30145)" |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#29134](https://github.com/flutter/flutter/pull/29134) Properly escape Android SDK Manager path in error message |
|   | [flutter/flutter#28470](https://github.com/flutter/flutter/pull/28470) Throw assertion error when a Hero has a Hero child. |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29229](https://github.com/flutter/flutter/pull/29229) Install JDK and Android SDK only for integration tests |
|   | [flutter/flutter#29054](https://github.com/flutter/flutter/pull/29054) Deprecate profile() |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28970](https://github.com/flutter/flutter/pull/28970) Fix coverage shard and print summary after test run |
|   | [flutter/flutter#28863](https://github.com/flutter/flutter/pull/28863) Fall-back to platform tools in Android SDK detection logic. |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28944](https://github.com/flutter/flutter/pull/28944) fix missing variable name |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
|   | [flutter/flutter#28224](https://github.com/flutter/flutter/pull/28224) Adds a template for Pull Requests |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27953](https://github.com/flutter/flutter/pull/27953) Add outer try block for obtainKey errors. Add docs. |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/engine#8074](https://github.com/flutter/engine/pull/8074) Add script to help generate PR messages |
| **⌘‬ platform-mac** | **2 issues with this label** |
|   | [flutter/flutter#31218](https://github.com/flutter/flutter/pull/31218) Add run capability for macOS target |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
| **⌺‬ platform-ios** | **4 issues with this label** |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#8094](https://github.com/flutter/flutter/issues/8094) Flutter doesn't find xcode 8.2 |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
| **▣ platform-android** | **5 issues with this label** |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#29604](https://github.com/flutter/flutter/pull/29604) added friendlier error for invalid AndroidManifest.xml |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
| **○ platform-fuchsia** | **1 issue with this label** |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
| **⚠ TODAY** | **2 issues with this label** |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
| **❖ platform-windows** | **1 issue with this label** |
|   | [flutter/flutter#28356](https://github.com/flutter/flutter/pull/28356) Log pub return code on failure on Windows |
## Labels Addressed Sorted by Issue

| Label | Number of Issues |
|--------|--------|
| **framework** | **144 issues  with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
|   | [flutter/flutter#30422](https://github.com/flutter/flutter/pull/30422) Commit a navigator.pop as soon as the back swipe is lifted |
|   | [flutter/flutter#30946](https://github.com/flutter/flutter/pull/30946) Add some more cupertino icons |
|   | [flutter/flutter#31159](https://github.com/flutter/flutter/pull/31159) Revert "Use full height of the glyph for caret height on Android" |
|   | [flutter/flutter#30938](https://github.com/flutter/flutter/pull/30938) Update keycodes, fix a comment. |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30809](https://github.com/flutter/flutter/pull/30809)  Fix issue 23527: Exception: RenderViewport exceeded its maximum numb… |
|   | [flutter/flutter#30746](https://github.com/flutter/flutter/pull/30746) Baseline Aligned Row |
|   | [flutter/flutter#30880](https://github.com/flutter/flutter/pull/30880) Let `sliver.dart` `_createErrorWidget` work with other Widgets |
|   | [flutter/flutter#30862](https://github.com/flutter/flutter/pull/30862) CupertinoDatePicker initialDateTime accounts for minuteInterval  |
|   | [flutter/flutter#30876](https://github.com/flutter/flutter/pull/30876) Simplify toImage future handling |
|   | [flutter/flutter#30470](https://github.com/flutter/flutter/pull/30470) Fixed Table flex column layout error #30437 |
|   | [flutter/flutter#30667](https://github.com/flutter/flutter/pull/30667) Fix additional @mustCallSuper indirect overrides and mixins |
|   | [flutter/flutter#30643](https://github.com/flutter/flutter/pull/30643) Add Form.onSaved |
|   | [flutter/flutter#30793](https://github.com/flutter/flutter/pull/30793) Revert "Add Form.onSaved" |
|   | [flutter/flutter#30414](https://github.com/flutter/flutter/pull/30414) Remove pressure customization from some pointer events |
|   | [flutter/flutter#30525](https://github.com/flutter/flutter/pull/30525) Fix cursor outside of input width |
|   | [flutter/flutter#29822](https://github.com/flutter/flutter/pull/29822) Cupertino localization step 7: modularize material specific things out of gen_localizations.dart |
|   | [flutter/flutter#30814](https://github.com/flutter/flutter/pull/30814) Fix StatefulWidget and StatelessWidget Sample Documentation |
|   | [flutter/flutter#30527](https://github.com/flutter/flutter/pull/30527) Cupertino localization step 11: add more translation clarifications in the instructions |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30760](https://github.com/flutter/flutter/pull/30760) fix cast NPE in invokeListMethod and invokeMapMethod |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30640](https://github.com/flutter/flutter/pull/30640) Add `const Border.uniformSide()` |
|   | [flutter/flutter#30644](https://github.com/flutter/flutter/pull/30644) Make FormField._validate() return void |
|   | [flutter/flutter#30645](https://github.com/flutter/flutter/pull/30645) Add docs to FormFieldValidator |
|   | [flutter/flutter#30626](https://github.com/flutter/flutter/pull/30626) Add sample for ValueListenable |
|   | [flutter/flutter#30563](https://github.com/flutter/flutter/pull/30563) Fixed a typo in the Expanded API doc |
|   | [flutter/flutter#30513](https://github.com/flutter/flutter/pull/30513) Fix issue 21640: Assertion Error : '_listenerAttached': is not true |
|   | [flutter/flutter#30305](https://github.com/flutter/flutter/pull/30305) shorter nullable list duplications |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#29708](https://github.com/flutter/flutter/pull/29708) Cupertino localization step 5: add french arb as translated example |
|   | [flutter/flutter#29413](https://github.com/flutter/flutter/pull/29413) Fix MaterialApp's _navigatorObserver when only builder used |
|   | [flutter/flutter#29998](https://github.com/flutter/flutter/pull/29998) Fix edge cases of PointerEventConverter |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30275](https://github.com/flutter/flutter/pull/30275) Implement compute for async function (#16265) |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#30463](https://github.com/flutter/flutter/pull/30463) Revert "Error message for setting shaderWarmUp too late (#30145)" |
|   | [flutter/flutter#30452](https://github.com/flutter/flutter/pull/30452) Moar Videos |
|   | [flutter/flutter#30451](https://github.com/flutter/flutter/pull/30451) Bump dartdocs to 0.28.3 |
|   | [flutter/flutter#30058](https://github.com/flutter/flutter/pull/30058) Draggable Scrollable sheet |
|   | [flutter/flutter#30353](https://github.com/flutter/flutter/pull/30353) Fix minor typo |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#30304](https://github.com/flutter/flutter/pull/30304) no need .toList() before .join() |
|   | [flutter/flutter#30212](https://github.com/flutter/flutter/pull/30212) Added assert to prevent complete ListTile trailing/leading horizontal expansion |
|   | [flutter/flutter#30227](https://github.com/flutter/flutter/pull/30227) Simplify logic of TapGestureRecognizer |
|   | [flutter/flutter#30123](https://github.com/flutter/flutter/pull/30123) Fix OutlineInputBorder crash |
|   | [flutter/flutter#30219](https://github.com/flutter/flutter/pull/30219) Added helpful Material assert message |
|   | [flutter/flutter#29771](https://github.com/flutter/flutter/pull/29771) Set Max Height for ListTile trailing and leading widgets |
|   | [flutter/flutter#29993](https://github.com/flutter/flutter/pull/29993) Adds the keyboard mapping for Linux |
|   | [flutter/flutter#29860](https://github.com/flutter/flutter/pull/29860) Move binarySearch to foundation. |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29821](https://github.com/flutter/flutter/pull/29821) Cupertino localization step 1.5: fix a resource mismatch in cupertino_en.arb |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#29811](https://github.com/flutter/flutter/pull/29811) TextField Validator Height Docs |
|   | [flutter/flutter#29943](https://github.com/flutter/flutter/pull/29943) Remove unwanted gap between navigation bar and safe area's child |
|   | [flutter/flutter#22859](https://github.com/flutter/flutter/issues/22859) Provide links between dart:ui classes and their corresponding classes in higher layers |
|   | [flutter/flutter#29929](https://github.com/flutter/flutter/pull/29929) Remove tranparent paint hack from BackdropFilter |
|   | [flutter/flutter#29630](https://github.com/flutter/flutter/pull/29630) Add heart shapes to CupertinoIcons |
|   | [flutter/flutter#29563](https://github.com/flutter/flutter/pull/29563) Avoid flickering while dragging to select text |
|   | [flutter/flutter#29701](https://github.com/flutter/flutter/pull/29701) Reland composite physical layers for all platforms |
|   | [flutter/flutter#28735](https://github.com/flutter/flutter/pull/28735) [Material] Create a FloatingActionButton ThemeData and honor it within the FloatingActionButton |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
|   | [flutter/flutter#29623](https://github.com/flutter/flutter/pull/29623) Revert "Reland composite physical layers on all platforms" |
|   | [flutter/flutter#29564](https://github.com/flutter/flutter/pull/29564) Update progress indicator API docs |
|   | [flutter/flutter#29566](https://github.com/flutter/flutter/pull/29566) Manually roll engine to 5088735e5f |
|   | [flutter/flutter#29463](https://github.com/flutter/flutter/pull/29463) Make real JSON in arb |
|   | [flutter/flutter#29231](https://github.com/flutter/flutter/pull/29231) Adds macOS raw keyboard mapping |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#13637](https://github.com/flutter/flutter/issues/13637) Add images and examples to Align |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29395](https://github.com/flutter/flutter/pull/29395) Fix text selection when user is dragging in the opposite direction |
|   | [flutter/flutter#28470](https://github.com/flutter/flutter/pull/28470) Throw assertion error when a Hero has a Hero child. |
|   | [flutter/flutter#29183](https://github.com/flutter/flutter/pull/29183) Implement labelPadding configuration in TabBarTheme  |
|   | [flutter/flutter#29329](https://github.com/flutter/flutter/pull/29329) Error message when TextSelectionOverlay finds no Overlay |
|   | [flutter/flutter#28919](https://github.com/flutter/flutter/pull/28919) Always composite PhysicalModels |
|   | [flutter/flutter#28751](https://github.com/flutter/flutter/pull/28751) Expose decorationThickness in TextStyle. Roll engine (12 commits) |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29179](https://github.com/flutter/flutter/pull/29179) No image shader caching in default shader warm-up |
|   | [flutter/flutter#29175](https://github.com/flutter/flutter/pull/29175) Ensure that animated pairs of Tabs TextStyles have matching inherited values |
|   | [flutter/flutter#29069](https://github.com/flutter/flutter/pull/29069) Heroes and nested Navigators |
|   | [flutter/flutter#28953](https://github.com/flutter/flutter/pull/28953) Include platformViewId in semantics tree |
|   | [flutter/flutter#29054](https://github.com/flutter/flutter/pull/29054) Deprecate profile() |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#29051](https://github.com/flutter/flutter/pull/29051) fix block formatting |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28169](https://github.com/flutter/flutter/pull/28169) Add/rewrite tests for FocusScope. |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#28951](https://github.com/flutter/flutter/pull/28951) Quick fix for shader warm up |
|   | [flutter/flutter#28749](https://github.com/flutter/flutter/pull/28749) Add minimum time gap requirement to double tap |
|   | [flutter/flutter#28855](https://github.com/flutter/flutter/pull/28855) Move material iOS back swipe test to material |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28944](https://github.com/flutter/flutter/pull/28944) fix missing variable name |
|   | [flutter/flutter#27811](https://github.com/flutter/flutter/pull/27811) set literal conversions |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28809](https://github.com/flutter/flutter/pull/28809) fix some formatting issues |
|   | [flutter/flutter#28666](https://github.com/flutter/flutter/pull/28666) Make RenderUiKitView reject absorbed touch events |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#28661](https://github.com/flutter/flutter/pull/28661) Use a simpler implementation of Diagnosticable.toString when running in profile/release mode |
|   | [flutter/flutter#28736](https://github.com/flutter/flutter/pull/28736) Avoid the overhead of instantiating a generator in paintImage |
|   | [flutter/flutter#28638](https://github.com/flutter/flutter/pull/28638) Fix the test annotation used for test groups |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28709](https://github.com/flutter/flutter/pull/28709) improve error messages on Text constructors |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28546](https://github.com/flutter/flutter/pull/28546) Call onTapCancel when down pointer gets cancelled |
|   | [flutter/flutter#28537](https://github.com/flutter/flutter/pull/28537) Reland "Shader warm up (#27660)" |
|   | [flutter/flutter#28013](https://github.com/flutter/flutter/pull/28013) [Material] Unit test for skipping Slider tick mark due to overdensity |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
|   | [flutter/flutter#21834](https://github.com/flutter/flutter/pull/21834)  Add shapeBorder option on App Bar |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
|   | [flutter/flutter#28371](https://github.com/flutter/flutter/pull/28371) Ensure that the DropdownButton menu respects its parents bounds |
|   | [flutter/flutter#28214](https://github.com/flutter/flutter/pull/28214) [Material] Add the ability to theme trailing app bar actions independently from leading |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28017](https://github.com/flutter/flutter/pull/28017) Add more docs to BackdropFilter |
|   | [flutter/flutter#28398](https://github.com/flutter/flutter/pull/28398) fix red build for analysis |
|   | [flutter/flutter#28163](https://github.com/flutter/flutter/pull/28163) [Material] Add ability to set shadow color and selected shadow color for chips and for chip themes |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
|   | [flutter/flutter#28172](https://github.com/flutter/flutter/pull/28172) Add backgroundColor argument to TextStyle for convenience |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
|   | [flutter/flutter#27953](https://github.com/flutter/flutter/pull/27953) Add outer try block for obtainKey errors. Add docs. |
|   | [flutter/flutter#28216](https://github.com/flutter/flutter/pull/28216) Fix 'to to' |
|   | [flutter/flutter#7738](https://github.com/flutter/flutter/issues/7738) Allow for more flexible unselected label color parameter in TabBar |
|   | [flutter/flutter#27973](https://github.com/flutter/flutter/pull/27973) Add extendBody parameter to Scaffold, body MediaQuery reflects BAB height |
|   | [flutter/flutter#28006](https://github.com/flutter/flutter/pull/28006) Re-apply "Ensure all curves return 0 and 1 in .transform(t) when t=0/1" |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **tool** | **72 issues  with this label** |
|   | [flutter/flutter#30857](https://github.com/flutter/flutter/pull/30857) Added support for authentication codes for the VM service. |
|   | [flutter/flutter#31283](https://github.com/flutter/flutter/pull/31283) Add desktop workflows to doctor |
|   | [flutter/flutter#31229](https://github.com/flutter/flutter/pull/31229) Add flutter run support for linux and windows |
|   | [flutter/flutter#31277](https://github.com/flutter/flutter/pull/31277) pass track widget creation flag through to build script |
|   | [flutter/flutter#31171](https://github.com/flutter/flutter/pull/31171) Allow disabling all fingerprint caches via environment variable |
|   | [flutter/flutter#31205](https://github.com/flutter/flutter/pull/31205) Add desktop projects and build commands (experimental) |
|   | [flutter/flutter#31073](https://github.com/flutter/flutter/pull/31073) Fuchsia step 1: add SDK version file and artifact download |
|   | [flutter/flutter#31064](https://github.com/flutter/flutter/pull/31064) Add sorting to flutter version command |
|   | [flutter/flutter#31063](https://github.com/flutter/flutter/pull/31063) Download and handle product version of flutter patched sdk |
|   | [flutter/flutter#31074](https://github.com/flutter/flutter/pull/31074) make flutterProject option of CoverageCollector optional |
|   | [flutter/flutter#30818](https://github.com/flutter/flutter/pull/30818) New flag to `flutter drive` to skip installing fresh app on device |
|   | [flutter/flutter#30811](https://github.com/flutter/flutter/pull/30811) Make coverage, like, really fast |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#30867](https://github.com/flutter/flutter/pull/30867) Add toggle for debugProfileWidgetBuilds |
|   | [flutter/flutter#30648](https://github.com/flutter/flutter/pull/30648) Allow downloading of desktop embedding artifacts |
|   | [flutter/flutter#30235](https://github.com/flutter/flutter/pull/30235) Warn on uncomitted changes |
|   | [flutter/flutter#30115](https://github.com/flutter/flutter/pull/30115) Forward missing pub commands |
|   | [flutter/flutter#30578](https://github.com/flutter/flutter/pull/30578) Mark ios-deploy version 2.0.0 as bad |
|   | [flutter/flutter#30458](https://github.com/flutter/flutter/pull/30458) [fuchsia] Fix isolate filter |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#30218](https://github.com/flutter/flutter/pull/30218) [fuchsia_tester] Plumb through the location of icudtl |
|   | [flutter/flutter#30075](https://github.com/flutter/flutter/pull/30075) Ensure that flutter run/drive/test/update_packages only downloads required artifacts |
|   | [flutter/flutter#30071](https://github.com/flutter/flutter/pull/30071) Move spinner `_defaultSlowWarning` message to a new line |
|   | [flutter/flutter#29938](https://github.com/flutter/flutter/pull/29938) Pass FLUTTER_TOOL_ARGS to snapshot command. |
|   | [flutter/flutter#29768](https://github.com/flutter/flutter/pull/29768) Directly depend on frontend_server tool in fuchsia_attach |
|   | [flutter/flutter#29783](https://github.com/flutter/flutter/pull/29783) Fix cache location, artifacts, and re-enable dart2js test |
|   | [flutter/flutter#29818](https://github.com/flutter/flutter/pull/29818) dont fail build if codegen fails |
|   | [flutter/flutter#29192](https://github.com/flutter/flutter/pull/29192) Update upgrade to rebase and stash local changes. |
|   | [flutter/flutter#29764](https://github.com/flutter/flutter/pull/29764) update fuchsia-attach to configure dev_finder location |
|   | [flutter/flutter#29721](https://github.com/flutter/flutter/pull/29721) Use Dart version in script cache check |
|   | [flutter/flutter#29399](https://github.com/flutter/flutter/pull/29399) Enable code generation features in tool (via opt-in) |
|   | [flutter/flutter#29699](https://github.com/flutter/flutter/pull/29699) Fix more tests for ANSI terminals |
|   | [flutter/flutter#29469](https://github.com/flutter/flutter/pull/29469) fix asset reloading |
|   | [flutter/flutter#29693](https://github.com/flutter/flutter/pull/29693) Use source list from the compiler to track invalidated files for hot reload. |
|   | [flutter/flutter#29434](https://github.com/flutter/flutter/pull/29434) Add builders and engine hash to fingerprint |
|   | [flutter/flutter#29625](https://github.com/flutter/flutter/pull/29625) Fix typo in flutter_tools |
|   | [flutter/flutter#29604](https://github.com/flutter/flutter/pull/29604) added friendlier error for invalid AndroidManifest.xml |
|   | [flutter/flutter#29461](https://github.com/flutter/flutter/pull/29461) Remove explicit frame schedule |
|   | [flutter/flutter#29048](https://github.com/flutter/flutter/pull/29048) Use async execution for xcodebuild commands |
|   | [flutter/flutter#29528](https://github.com/flutter/flutter/pull/29528) Ensure that assumed formatting of properties file is correct |
|   | [flutter/flutter#27749](https://github.com/flutter/flutter/pull/27749) Switch flutter_tools from script to app-jit snapshot. |
|   | [flutter/flutter#28152](https://github.com/flutter/flutter/pull/28152) Improve hot reload performance |
|   | [flutter/flutter#29258](https://github.com/flutter/flutter/pull/29258) Add dump-shader-skp flag to flutter tools |
|   | [flutter/flutter#29134](https://github.com/flutter/flutter/pull/29134) Properly escape Android SDK Manager path in error message |
|   | [flutter/flutter#29171](https://github.com/flutter/flutter/pull/29171) Only run codegen at start of flutter_test |
|   | [flutter/flutter#28938](https://github.com/flutter/flutter/pull/28938) Add a `flutter create --list-samples` command |
|   | [flutter/flutter#29126](https://github.com/flutter/flutter/pull/29126) Cause `flutter analyze` to fail if the analysis server experienced an error. |
|   | [flutter/flutter#29062](https://github.com/flutter/flutter/pull/29062) fix windows codegen |
|   | [flutter/flutter#28603](https://github.com/flutter/flutter/pull/28603) select ResidentCompiler during FlutterDevice initialization |
|   | [flutter/flutter#28863](https://github.com/flutter/flutter/pull/28863) Fall-back to platform tools in Android SDK detection logic. |
|   | [flutter/flutter#28658](https://github.com/flutter/flutter/pull/28658) Include git output into error message from channel command |
|   | [flutter/flutter#28356](https://github.com/flutter/flutter/pull/28356) Log pub return code on failure on Windows |
|   | [flutter/flutter#28386](https://github.com/flutter/flutter/pull/28386) remove personal repo and replace with trivial example for smoke test |
|   | [flutter/flutter#28477](https://github.com/flutter/flutter/pull/28477) Fix backspace and clear length in AnsiStatus |
|   | [flutter/flutter#28302](https://github.com/flutter/flutter/pull/28302) Add basic web device and run support |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
|   | [flutter/flutter#28334](https://github.com/flutter/flutter/pull/28334) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#27944](https://github.com/flutter/flutter/pull/27944) Add tests |
|   | [flutter/flutter#28097](https://github.com/flutter/flutter/pull/28097) Allow for gradle downloading missing SDK assets |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#28352](https://github.com/flutter/flutter/pull/28352) only perform multi-root mapping if there are multiple roots |
|   | [flutter/flutter#28101](https://github.com/flutter/flutter/pull/28101) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#28027](https://github.com/flutter/flutter/pull/28027) Remove extra slash from builder that is not handled on windows |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27754](https://github.com/flutter/flutter/pull/27754) Add support for binary compression of dynamic patches by the flutter tool. |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/flutter#27709](https://github.com/flutter/flutter/pull/27709) Increase our build budget to 16ms |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
|   | [flutter/flutter#28011](https://github.com/flutter/flutter/pull/28011) Remove accidentally committed libs |
|   | [flutter/flutter#27987](https://github.com/flutter/flutter/pull/27987) add ui.Window fallback to TestViewConfiguration |
|   | [flutter/flutter#27668](https://github.com/flutter/flutter/pull/27668) Wire dart2js through flutter tool, add compilation test |
| **team** | **55 issues  with this label** |
|   | [flutter/flutter#30990](https://github.com/flutter/flutter/pull/30990) Allow profile widget builds in profile mode |
|   | [flutter/flutter#30832](https://github.com/flutter/flutter/pull/30832) Bump Android build tools to 28.0.3 in Dockerfile |
|   | [flutter/flutter#30871](https://github.com/flutter/flutter/pull/30871) Update the upload key which seems to have trouble for some reason |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#25164](https://github.com/flutter/flutter/pull/25164) Pan and zoom gallery demo |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30460](https://github.com/flutter/flutter/pull/30460) Fix gallery API doc URL launcher |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#29621](https://github.com/flutter/flutter/pull/29621) Update PULL_REQUEST_TEMPLATE.md |
|   | [flutter/flutter#30019](https://github.com/flutter/flutter/pull/30019) Update to latest matcher |
|   | [flutter/flutter#29908](https://github.com/flutter/flutter/pull/29908) Update Twitter handle @flutterio -> @FlutterDev |
|   | [flutter/flutter#29632](https://github.com/flutter/flutter/pull/29632) Enable platform views for Flutter Gallery on iOS. |
|   | [flutter/flutter#29669](https://github.com/flutter/flutter/pull/29669) Speed up CI via mojave-flutter image |
|   | [flutter/flutter#29467](https://github.com/flutter/flutter/pull/29467) prevent stream notifications from interfering with reload |
|   | [flutter/flutter#29619](https://github.com/flutter/flutter/pull/29619) make literals const for @immutable constructors |
|   | [flutter/flutter#29454](https://github.com/flutter/flutter/pull/29454) Update another SDK constraint |
|   | [flutter/flutter#29340](https://github.com/flutter/flutter/pull/29340) guard new formatter behind env var |
|   | [flutter/flutter#29229](https://github.com/flutter/flutter/pull/29229) Install JDK and Android SDK only for integration tests |
|   | [flutter/flutter#29126](https://github.com/flutter/flutter/pull/29126) Cause `flutter analyze` to fail if the analysis server experienced an error. |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#29051](https://github.com/flutter/flutter/pull/29051) fix block formatting |
|   | [flutter/flutter#29025](https://github.com/flutter/flutter/pull/29025) print system time on all mac builds |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#27811](https://github.com/flutter/flutter/pull/27811) set literal conversions |
|   | [flutter/flutter#28852](https://github.com/flutter/flutter/pull/28852) Update macOS version in tests |
|   | [flutter/flutter#28809](https://github.com/flutter/flutter/pull/28809) fix some formatting issues |
|   | [flutter/flutter#28748](https://github.com/flutter/flutter/pull/28748) Revert "Remove the old HaTS implementation on API docs" |
|   | [flutter/flutter#28658](https://github.com/flutter/flutter/pull/28658) Include git output into error message from channel command |
|   | [flutter/flutter#28673](https://github.com/flutter/flutter/pull/28673) Add missing trailing commas |
|   | [flutter/flutter#28555](https://github.com/flutter/flutter/pull/28555) disable dart2js test |
|   | [flutter/flutter#28369](https://github.com/flutter/flutter/pull/28369) Add LICENSE test to presubmit checks |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
|   | [flutter/flutter#28343](https://github.com/flutter/flutter/pull/28343) pass --skip-build-script-checks and remove module usage |
|   | [flutter/flutter#28334](https://github.com/flutter/flutter/pull/28334) Remove unused --packages argument to gen_snapshot. |
|   | [flutter/flutter#28482](https://github.com/flutter/flutter/pull/28482) Fuschia -> Fuchsia |
|   | [flutter/flutter#28175](https://github.com/flutter/flutter/pull/28175) Only call Activity.reportFullyDrawn on Lollipop or above |
|   | [flutter/flutter#28370](https://github.com/flutter/flutter/pull/28370) Removed trailing whitespace from the end of the CI config |
|   | [flutter/flutter#28373](https://github.com/flutter/flutter/pull/28373) Mark non-flaky test as such |
|   | [flutter/flutter#28125](https://github.com/flutter/flutter/pull/28125) [Gallery] Fortnightly demo moved from flutter/samples. |
|   | [flutter/flutter#28400](https://github.com/flutter/flutter/pull/28400) update packages |
|   | [flutter/flutter#28215](https://github.com/flutter/flutter/pull/28215) Minor UI tweaks to Cards demo based on internal feedback |
|   | [flutter/flutter#28291](https://github.com/flutter/flutter/pull/28291) Reland #27754, now that bsdiff has moved to flutter/packages. |
|   | [flutter/flutter#28171](https://github.com/flutter/flutter/pull/28171) deploy to .dev firebase projects. |
|   | [flutter/flutter#28281](https://github.com/flutter/flutter/pull/28281) Reduce memory benchmark perf tests outer loop to 10. |
|   | [flutter/flutter#27261](https://github.com/flutter/flutter/pull/27261) format parameter list |
|   | [flutter/flutter#24889](https://github.com/flutter/flutter/pull/24889) Update AUTHORS |
|   | [flutter/flutter#28238](https://github.com/flutter/flutter/pull/28238) Remove set literal syntax |
|   | [flutter/flutter#28235](https://github.com/flutter/flutter/pull/28235) Remove line breaks from PR template |
|   | [flutter/flutter#26376](https://github.com/flutter/flutter/pull/26376) removed image with 404 |
|   | [flutter/flutter#28224](https://github.com/flutter/flutter/pull/28224) Adds a template for Pull Requests |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27305](https://github.com/flutter/flutter/pull/27305) Outline for survey implementation |
|   | [flutter/flutter#28024](https://github.com/flutter/flutter/pull/28024) Disable prefer_collection_literals in the analyzer until we can update to using set literals |
|   | [flutter/flutter#27793](https://github.com/flutter/flutter/pull/27793) Remove remaining "## Sample code" segments, and fix the snippet generator. |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **f: material design** | **44 issues  with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
|   | [flutter/flutter#30746](https://github.com/flutter/flutter/pull/30746) Baseline Aligned Row |
|   | [flutter/flutter#30793](https://github.com/flutter/flutter/pull/30793) Revert "Add Form.onSaved" |
|   | [flutter/flutter#30525](https://github.com/flutter/flutter/pull/30525) Fix cursor outside of input width |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30497](https://github.com/flutter/flutter/pull/30497) Add confirmDismiss example to flutter_gallery |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#29413](https://github.com/flutter/flutter/pull/29413) Fix MaterialApp's _navigatorObserver when only builder used |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30353](https://github.com/flutter/flutter/pull/30353) Fix minor typo |
|   | [flutter/flutter#30212](https://github.com/flutter/flutter/pull/30212) Added assert to prevent complete ListTile trailing/leading horizontal expansion |
|   | [flutter/flutter#30123](https://github.com/flutter/flutter/pull/30123) Fix OutlineInputBorder crash |
|   | [flutter/flutter#30219](https://github.com/flutter/flutter/pull/30219) Added helpful Material assert message |
|   | [flutter/flutter#29771](https://github.com/flutter/flutter/pull/29771) Set Max Height for ListTile trailing and leading widgets |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#29811](https://github.com/flutter/flutter/pull/29811) TextField Validator Height Docs |
|   | [flutter/flutter#29980](https://github.com/flutter/flutter/pull/29980) Fix issue with account drawer header arrow rotating when setState is called |
|   | [flutter/flutter#29563](https://github.com/flutter/flutter/pull/29563) Avoid flickering while dragging to select text |
|   | [flutter/flutter#29138](https://github.com/flutter/flutter/pull/29138) Update DropdownButton underline to be customizable |
|   | [flutter/flutter#28735](https://github.com/flutter/flutter/pull/28735) [Material] Create a FloatingActionButton ThemeData and honor it within the FloatingActionButton |
|   | [flutter/flutter#29564](https://github.com/flutter/flutter/pull/29564) Update progress indicator API docs |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#29183](https://github.com/flutter/flutter/pull/29183) Implement labelPadding configuration in TabBarTheme  |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29175](https://github.com/flutter/flutter/pull/29175) Ensure that animated pairs of Tabs TextStyles have matching inherited values |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28013](https://github.com/flutter/flutter/pull/28013) [Material] Unit test for skipping Slider tick mark due to overdensity |
|   | [flutter/flutter#21834](https://github.com/flutter/flutter/pull/21834)  Add shapeBorder option on App Bar |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28371](https://github.com/flutter/flutter/pull/28371) Ensure that the DropdownButton menu respects its parents bounds |
|   | [flutter/flutter#28214](https://github.com/flutter/flutter/pull/28214) [Material] Add the ability to theme trailing app bar actions independently from leading |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28163](https://github.com/flutter/flutter/pull/28163) [Material] Add ability to set shadow color and selected shadow color for chips and for chip themes |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
|   | [flutter/flutter#28216](https://github.com/flutter/flutter/pull/28216) Fix 'to to' |
|   | [flutter/flutter#27973](https://github.com/flutter/flutter/pull/27973) Add extendBody parameter to Scaffold, body MediaQuery reflects BAB height |
| **waiting for tree to go green** | **37 issues  with this label** |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30305](https://github.com/flutter/flutter/pull/30305) shorter nullable list duplications |
|   | [flutter/flutter#30535](https://github.com/flutter/flutter/pull/30535) Correctly synthesise event buttons |
|   | [flutter/flutter#29998](https://github.com/flutter/flutter/pull/29998) Fix edge cases of PointerEventConverter |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#30463](https://github.com/flutter/flutter/pull/30463) Revert "Error message for setting shaderWarmUp too late (#30145)" |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#29134](https://github.com/flutter/flutter/pull/29134) Properly escape Android SDK Manager path in error message |
|   | [flutter/flutter#28470](https://github.com/flutter/flutter/pull/28470) Throw assertion error when a Hero has a Hero child. |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29229](https://github.com/flutter/flutter/pull/29229) Install JDK and Android SDK only for integration tests |
|   | [flutter/flutter#29054](https://github.com/flutter/flutter/pull/29054) Deprecate profile() |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28970](https://github.com/flutter/flutter/pull/28970) Fix coverage shard and print summary after test run |
|   | [flutter/flutter#28863](https://github.com/flutter/flutter/pull/28863) Fall-back to platform tools in Android SDK detection logic. |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28944](https://github.com/flutter/flutter/pull/28944) fix missing variable name |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
|   | [flutter/flutter#28224](https://github.com/flutter/flutter/pull/28224) Adds a template for Pull Requests |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
|   | [flutter/flutter#28040](https://github.com/flutter/flutter/pull/28040) Remove json_schema and cli_util deps from flutter_tool |
|   | [flutter/flutter#27953](https://github.com/flutter/flutter/pull/27953) Add outer try block for obtainKey errors. Add docs. |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/engine#8074](https://github.com/flutter/engine/pull/8074) Add script to help generate PR messages |
| **d: api docs** | **36 issues  with this label** |
|   | [flutter/flutter#30887](https://github.com/flutter/flutter/pull/30887) Add more dialog doc cross-reference |
|   | [flutter/flutter#30814](https://github.com/flutter/flutter/pull/30814) Fix StatefulWidget and StatelessWidget Sample Documentation |
|   | [flutter/flutter#30805](https://github.com/flutter/flutter/pull/30805) Update ExpansionPanelList Samples with Scaffold Template |
|   | [flutter/flutter#30537](https://github.com/flutter/flutter/pull/30537) Embedded images and added variations to ListTile sample code |
|   | [flutter/flutter#30626](https://github.com/flutter/flutter/pull/30626) Add sample for ValueListenable |
|   | [flutter/flutter#30563](https://github.com/flutter/flutter/pull/30563) Fixed a typo in the Expanded API doc |
|   | [flutter/flutter#30343](https://github.com/flutter/flutter/pull/30343) ExpansionPanelList and ExpansionPanelList.radio documentation |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30452](https://github.com/flutter/flutter/pull/30452) Moar Videos |
|   | [flutter/flutter#30451](https://github.com/flutter/flutter/pull/30451) Bump dartdocs to 0.28.3 |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#30201](https://github.com/flutter/flutter/pull/30201) update sample code analyzer regexp & test case |
|   | [flutter/flutter#30048](https://github.com/flutter/flutter/pull/30048) Document that Hero needs to be present on destination page's zero frame |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#22859](https://github.com/flutter/flutter/issues/22859) Provide links between dart:ui classes and their corresponding classes in higher layers |
|   | [flutter/flutter#29445](https://github.com/flutter/flutter/pull/29445) Add doc about MediaQuery to Chip |
|   | [flutter/flutter#13637](https://github.com/flutter/flutter/issues/13637) Add images and examples to Align |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29245](https://github.com/flutter/flutter/pull/29245) Fix DartDoc for UniqueKey |
|   | [flutter/flutter#29165](https://github.com/flutter/flutter/pull/29165) Docs edit for Tab Label Color |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#28675](https://github.com/flutter/flutter/pull/28675) Update docs for ancestorWidgetOfExactType |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
|   | [flutter/flutter#28748](https://github.com/flutter/flutter/pull/28748) Revert "Remove the old HaTS implementation on API docs" |
|   | [flutter/flutter#28683](https://github.com/flutter/flutter/pull/28683) Remove the old HaTS implementation on API docs |
|   | [flutter/flutter#27751](https://github.com/flutter/flutter/pull/27751) Add Sample code for FlatButton #21136 |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28245](https://github.com/flutter/flutter/pull/28245) [Typo] Update 'use' to 'user' |
|   | [flutter/flutter#28017](https://github.com/flutter/flutter/pull/28017) Add more docs to BackdropFilter |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#27305](https://github.com/flutter/flutter/pull/27305) Outline for survey implementation |
|   | [flutter/flutter#27793](https://github.com/flutter/flutter/pull/27793) Remove remaining "## Sample code" segments, and fix the snippet generator. |
| **a: tests** | **23 issues  with this label** |
|   | [flutter/flutter#30857](https://github.com/flutter/flutter/pull/30857) Added support for authentication codes for the VM service. |
|   | [flutter/flutter#30898](https://github.com/flutter/flutter/pull/30898) Check that ErrorWidget.builder is not modified after test |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#30871](https://github.com/flutter/flutter/pull/30871) Update the upload key which seems to have trouble for some reason |
|   | [flutter/flutter#30747](https://github.com/flutter/flutter/pull/30747) Print warning if flutter drive is run in debug |
|   | [flutter/flutter#29669](https://github.com/flutter/flutter/pull/29669) Speed up CI via mojave-flutter image |
|   | [flutter/flutter#29467](https://github.com/flutter/flutter/pull/29467) prevent stream notifications from interfering with reload |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
|   | [flutter/flutter#29566](https://github.com/flutter/flutter/pull/29566) Manually roll engine to 5088735e5f |
|   | [flutter/flutter#29340](https://github.com/flutter/flutter/pull/29340) guard new formatter behind env var |
|   | [flutter/flutter#27971](https://github.com/flutter/flutter/pull/27971) Run non-perf sensitive tests on Cirrus |
|   | [flutter/flutter#29025](https://github.com/flutter/flutter/pull/29025) print system time on all mac builds |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28297](https://github.com/flutter/flutter/pull/28297) Test reporter |
|   | [flutter/flutter#28852](https://github.com/flutter/flutter/pull/28852) Update macOS version in tests |
|   | [flutter/flutter#28638](https://github.com/flutter/flutter/pull/28638) Fix the test annotation used for test groups |
|   | [flutter/flutter#28555](https://github.com/flutter/flutter/pull/28555) disable dart2js test |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
|   | [flutter/flutter#27944](https://github.com/flutter/flutter/pull/27944) Add tests |
|   | [flutter/flutter#28373](https://github.com/flutter/flutter/pull/28373) Mark non-flaky test as such |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
|   | [flutter/flutter#27987](https://github.com/flutter/flutter/pull/27987) add ui.Window fallback to TestViewConfiguration |
| **f: cupertino** | **20 issues  with this label** |
|   | [flutter/flutter#31088](https://github.com/flutter/flutter/pull/31088) Text field scroll physics |
|   | [flutter/flutter#30946](https://github.com/flutter/flutter/pull/30946) Add some more cupertino icons |
|   | [flutter/flutter#29451](https://github.com/flutter/flutter/pull/29451) Added opacity to cupertino switch when disabled |
|   | [flutter/flutter#30862](https://github.com/flutter/flutter/pull/30862) CupertinoDatePicker initialDateTime accounts for minuteInterval  |
|   | [flutter/flutter#29822](https://github.com/flutter/flutter/pull/29822) Cupertino localization step 7: modularize material specific things out of gen_localizations.dart |
|   | [flutter/flutter#30527](https://github.com/flutter/flutter/pull/30527) Cupertino localization step 11: add more translation clarifications in the instructions |
|   | [flutter/flutter#29767](https://github.com/flutter/flutter/pull/29767) Cupertino localization step 6: add a GlobalCupertinoLocalizations base class with date time formatting |
|   | [flutter/flutter#29708](https://github.com/flutter/flutter/pull/29708) Cupertino localization step 5: add french arb as translated example |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29821](https://github.com/flutter/flutter/pull/29821) Cupertino localization step 1.5: fix a resource mismatch in cupertino_en.arb |
|   | [flutter/flutter#29946](https://github.com/flutter/flutter/pull/29946) Let CupertinoPageScaffold have tap status bar to scroll to top |
|   | [flutter/flutter#29987](https://github.com/flutter/flutter/pull/29987) update CupertinoSwitch documentation |
|   | [flutter/flutter#29943](https://github.com/flutter/flutter/pull/29943) Remove unwanted gap between navigation bar and safe area's child |
|   | [flutter/flutter#29630](https://github.com/flutter/flutter/pull/29630) Add heart shapes to CupertinoIcons |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#29463](https://github.com/flutter/flutter/pull/29463) Make real JSON in arb |
|   | [flutter/flutter#28900](https://github.com/flutter/flutter/pull/28900) Add key support to cupertino button |
|   | [flutter/flutter#28855](https://github.com/flutter/flutter/pull/28855) Move material iOS back swipe test to material |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **a: text input** | **12 issues  with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28001](https://github.com/flutter/flutter/pull/28001) CupertinoTextField: added ability to change placeholder color |
|   | [flutter/flutter#29395](https://github.com/flutter/flutter/pull/29395) Fix text selection when user is dragging in the opposite direction |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#28169](https://github.com/flutter/flutter/pull/28169) Add/rewrite tests for FocusScope. |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28558](https://github.com/flutter/flutter/pull/28558) Fix typo |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27898](https://github.com/flutter/flutter/pull/27898) Add slight clarification to debugDeterministicCursor |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **team: gallery** | **11 issues  with this label** |
|   | [flutter/flutter#30455](https://github.com/flutter/flutter/pull/30455) Prevent vertical scroll in shrine by ensuring card size fits the screen |
|   | [flutter/flutter#30497](https://github.com/flutter/flutter/pull/30497) Add confirmDismiss example to flutter_gallery |
|   | [flutter/flutter#25164](https://github.com/flutter/flutter/pull/25164) Pan and zoom gallery demo |
|   | [flutter/flutter#27217](https://github.com/flutter/flutter/pull/27217) Fix Shrine overscroll glow indicator |
|   | [flutter/flutter#30460](https://github.com/flutter/flutter/pull/30460) Fix gallery API doc URL launcher |
|   | [flutter/flutter#29908](https://github.com/flutter/flutter/pull/29908) Update Twitter handle @flutterio -> @FlutterDev |
|   | [flutter/flutter#29632](https://github.com/flutter/flutter/pull/29632) Enable platform views for Flutter Gallery on iOS. |
|   | [flutter/flutter#25202](https://github.com/flutter/flutter/pull/25202) fix #19175 How should addTime be used from a test? |
|   | [flutter/flutter#28175](https://github.com/flutter/flutter/pull/28175) Only call Activity.reportFullyDrawn on Lollipop or above |
|   | [flutter/flutter#28125](https://github.com/flutter/flutter/pull/28125) [Gallery] Fortnightly demo moved from flutter/samples. |
|   | [flutter/flutter#28215](https://github.com/flutter/flutter/pull/28215) Minor UI tweaks to Cards demo based on internal feedback |
| **d: examples** | **8 issues  with this label** |
|   | [flutter/flutter#30398](https://github.com/flutter/flutter/pull/30398) Embedding new raised button diagram. |
|   | [flutter/flutter#30453](https://github.com/flutter/flutter/pull/30453) Updating sample code for BottomNavigationBar class for diagram. |
|   | [flutter/flutter#30348](https://github.com/flutter/flutter/pull/30348) RaisedButton Sample Code Update for Diagrams |
|   | [flutter/flutter#29697](https://github.com/flutter/flutter/pull/29697) Embedding new diagrams for API Docs |
|   | [flutter/flutter#29442](https://github.com/flutter/flutter/pull/29442) Align Snippet for API Docs |
|   | [flutter/flutter#29053](https://github.com/flutter/flutter/pull/29053) Update to Container Sample Code in API Docs |
|   | [flutter/flutter#28857](https://github.com/flutter/flutter/pull/28857) Form Snippet for API Docs |
|   | [flutter/flutter#28752](https://github.com/flutter/flutter/pull/28752) FAB Snippet for API Docs |
| **f: gestures** | **6 issues  with this label** |
|   | [flutter/flutter#28749](https://github.com/flutter/flutter/pull/28749) Add minimum time gap requirement to double tap |
|   | [flutter/flutter#28290](https://github.com/flutter/flutter/pull/28290) Text selection via mouse |
|   | [flutter/flutter#28546](https://github.com/flutter/flutter/pull/28546) Call onTapCancel when down pointer gets cancelled |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
|   | [flutter/flutter#26659](https://github.com/flutter/flutter/pull/26659) Adds fix for NAN value and pressure values outside of device reported min and max |
| **severe: API break** | **6 issues  with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#30414](https://github.com/flutter/flutter/pull/30414) Remove pressure customization from some pointer events |
|   | [flutter/flutter#28919](https://github.com/flutter/flutter/pull/28919) Always composite PhysicalModels |
|   | [flutter/flutter#28953](https://github.com/flutter/flutter/pull/28953) Include platformViewId in semantics tree |
|   | [flutter/flutter#28242](https://github.com/flutter/flutter/pull/28242) Add long-press-move support for text fields 2 |
|   | [flutter/flutter#28032](https://github.com/flutter/flutter/pull/28032) Revert 26001 |
| **a: animation** | **5 issues  with this label** |
|   | [flutter/flutter#30276](https://github.com/flutter/flutter/pull/30276) Random trivial fixes in the animation packages |
|   | [flutter/flutter#29069](https://github.com/flutter/flutter/pull/29069) Heroes and nested Navigators |
|   | [flutter/flutter#27803](https://github.com/flutter/flutter/pull/27803) Added sample code to AnimatedWidget |
|   | [flutter/flutter#28006](https://github.com/flutter/flutter/pull/28006) Re-apply "Ensure all curves return 0 and 1 in .transform(t) when t=0/1" |
|   | [flutter/flutter#27929](https://github.com/flutter/flutter/pull/27929) Use double literals where a double type is expected |
| **a: desktop** | **5 issues  with this label** |
|   | [flutter/flutter#30938](https://github.com/flutter/flutter/pull/30938) Update keycodes, fix a comment. |
|   | [flutter/flutter#30648](https://github.com/flutter/flutter/pull/30648) Allow downloading of desktop embedding artifacts |
|   | [flutter/flutter#30643](https://github.com/flutter/flutter/pull/30643) Add Form.onSaved |
|   | [flutter/flutter#29993](https://github.com/flutter/flutter/pull/29993) Adds the keyboard mapping for Linux |
|   | [flutter/flutter#29231](https://github.com/flutter/flutter/pull/29231) Adds macOS raw keyboard mapping |
| **e: desktop** | **5 issues  with this label** |
|   | [flutter/flutter#31283](https://github.com/flutter/flutter/pull/31283) Add desktop workflows to doctor |
|   | [flutter/flutter#31229](https://github.com/flutter/flutter/pull/31229) Add flutter run support for linux and windows |
|   | [flutter/flutter#31277](https://github.com/flutter/flutter/pull/31277) pass track widget creation flag through to build script |
|   | [flutter/flutter#31218](https://github.com/flutter/flutter/pull/31218) Add run capability for macOS target |
|   | [flutter/flutter#31205](https://github.com/flutter/flutter/pull/31205) Add desktop projects and build commands (experimental) |
| **severe: performance** | **5 issues  with this label** |
|   | [flutter/flutter#30990](https://github.com/flutter/flutter/pull/30990) Allow profile widget builds in profile mode |
|   | [flutter/flutter#30985](https://github.com/flutter/flutter/pull/30985) Add rrect contains microbenchmark |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28537](https://github.com/flutter/flutter/pull/28537) Reland "Shader warm up (#27660)" |
| **▣ platform-android** | **5 issues  with this label** |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#29604](https://github.com/flutter/flutter/pull/29604) added friendlier error for invalid AndroidManifest.xml |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
| **a: typography** | **4 issues  with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28751](https://github.com/flutter/flutter/pull/28751) Expose decorationThickness in TextStyle. Roll engine (12 commits) |
|   | [flutter/flutter#28398](https://github.com/flutter/flutter/pull/28398) fix red build for analysis |
|   | [flutter/flutter#28182](https://github.com/flutter/flutter/pull/28182) Add TextOverflow.visible |
| **t: gradle** | **4 issues  with this label** |
|   | [flutter/flutter#28097](https://github.com/flutter/flutter/pull/28097) Allow for gradle downloading missing SDK assets |
|   | [flutter/flutter#28355](https://github.com/flutter/flutter/pull/28355) Reland "Allow for gradle downloading missing SDK assets" (#28097) |
|   | [flutter/flutter#27754](https://github.com/flutter/flutter/pull/27754) Add support for binary compression of dynamic patches by the flutter tool. |
|   | [flutter/flutter#28031](https://github.com/flutter/flutter/pull/28031) Revert "Add support for binary compression of dynamic patches by the flutter tool. (#27754)" |
| **⌺‬ platform-ios** | **4 issues  with this label** |
|   | [flutter/flutter#30475](https://github.com/flutter/flutter/pull/30475) Trackpad mode crash fix |
|   | [flutter/flutter#8094](https://github.com/flutter/flutter/issues/8094) Flutter doesn't find xcode 8.2 |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
| **a: fidelity** | **3 issues  with this label** |
|   | [flutter/flutter#30991](https://github.com/flutter/flutter/pull/30991) Use full height of the glyph for caret height on Android |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
|   | [flutter/flutter#27711](https://github.com/flutter/flutter/pull/27711) Make extended FAB's icon optional |
| **affects: docs** | **3 issues  with this label** |
|   | [flutter/engine#8617](https://github.com/flutter/engine/pull/8617) Fixes a typo in comment |
|   | [flutter/engine#8472](https://github.com/flutter/engine/pull/8472) [Docs] Correcting link to contributing guide. |
|   | [flutter/engine#8295](https://github.com/flutter/engine/pull/8295) Fixing links between higher and lower classes. |
| **customer: fuchsia** | **3 issues  with this label** |
|   | [flutter/flutter#30458](https://github.com/flutter/flutter/pull/30458) [fuchsia] Fix isolate filter |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
|   | [flutter/flutter#29454](https://github.com/flutter/flutter/pull/29454) Update another SDK constraint |
| **easy fix** | **3 issues  with this label** |
|   | [flutter/flutter#21136](https://github.com/flutter/flutter/issues/21136) Request sample code for popular API docs |
|   | [flutter/flutter#7738](https://github.com/flutter/flutter/issues/7738) Allow for more flexible unselected label color parameter in TabBar |
|   | [flutter/engine#8472](https://github.com/flutter/engine/pull/8472) [Docs] Correcting link to contributing guide. |
| **engine** | **3 issues  with this label** |
|   | [flutter/flutter#14704](https://github.com/flutter/flutter/issues/14704) Unpack cached code on Android to the getCodeCacheDir() directory, rather than the user data directory |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
| **severe: regression** | **3 issues  with this label** |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
| **t: flutter driver** | **3 issues  with this label** |
|   | [flutter/flutter#30747](https://github.com/flutter/flutter/pull/30747) Print warning if flutter drive is run in debug |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
|   | [flutter/flutter#27709](https://github.com/flutter/flutter/pull/27709) Increase our build budget to 16ms |
| **cla: no** | **2 issues  with this label** |
|   | [flutter/flutter#27205](https://github.com/flutter/flutter/pull/27205) Fix TextField height issues |
|   | [flutter/flutter#28159](https://github.com/flutter/flutter/pull/28159) [Material] Expand BottomNavigationBar API (reprise) |
| **f: scrolling** | **2 issues  with this label** |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
|   | [flutter/flutter#27433](https://github.com/flutter/flutter/pull/27433) Fix issue where SliverPersistentHeader that is both floating and pinned would scroll down when scrolling past the beginning of the ScrollView |
| **t: hot reload** | **2 issues  with this label** |
|   | [flutter/flutter#29693](https://github.com/flutter/flutter/pull/29693) Use source list from the compiler to track invalidated files for hot reload. |
|   | [flutter/flutter#28152](https://github.com/flutter/flutter/pull/28152) Improve hot reload performance |
| **⌘‬ platform-mac** | **2 issues  with this label** |
|   | [flutter/flutter#31218](https://github.com/flutter/flutter/pull/31218) Add run capability for macOS target |
|   | [flutter/flutter#27908](https://github.com/flutter/flutter/pull/27908) Reland automatic discovery of observatory port for iOS |
| **⚠ TODAY** | **2 issues  with this label** |
|   | [flutter/flutter#8150](https://github.com/flutter/flutter/issues/8150) complex_layout_srcroll_perf__memory diff_total_kb regression |
|   | [flutter/flutter#28438](https://github.com/flutter/flutter/issues/28438) PC keyboard not works on text fields in emulator when on master branch. |
| **Work in progress (WIP)** | **1 issue  with this label** |
|   | [flutter/engine#8274](https://github.com/flutter/engine/pull/8274) [ui] Add null check in FontWeight.lerp |
| **a: accessibility** | **1 issue  with this label** |
|   | [flutter/flutter#29342](https://github.com/flutter/flutter/pull/29342) Add semantic label finders |
| **a: debugging** | **1 issue  with this label** |
|   | [flutter/flutter#27904](https://github.com/flutter/flutter/pull/27904) Convert PointerEvent's toString to Diagnosticable |
| **a: existing-apps** | **1 issue  with this label** |
|   | [flutter/flutter#27712](https://github.com/flutter/flutter/pull/27712) add2app test |
| **a: first hour** | **1 issue  with this label** |
|   | [flutter/flutter#30562](https://github.com/flutter/flutter/pull/30562) Replace flutter.io with flutter.dev |
| **a: internationalization** | **1 issue  with this label** |
|   | [flutter/flutter#27945](https://github.com/flutter/flutter/pull/27945) Fixes crossAxisSpacing overflow in RTL |
| **a: quality** | **1 issue  with this label** |
|   | [flutter/flutter#30562](https://github.com/flutter/flutter/pull/30562) Replace flutter.io with flutter.dev |
| **affects: dev experience** | **1 issue  with this label** |
|   | [flutter/engine#7908](https://github.com/flutter/engine/pull/7908) Link dart:* sources into engine for debugger source support |
| **affects: engine** | **1 issue  with this label** |
|   | [flutter/engine#7908](https://github.com/flutter/engine/pull/7908) Link dart:* sources into engine for debugger source support |
| **area-analyzer** | **1 issue  with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **customer: dream (g3)** | **1 issue  with this label** |
|   | [flutter/flutter#29532](https://github.com/flutter/flutter/pull/29532) Reland composite physical layers on all platforms |
| **f: routes** | **1 issue  with this label** |
|   | [flutter/flutter#28597](https://github.com/flutter/flutter/pull/28597) Adjust remaining Cupertino route animations to match native |
| **p1-high** | **1 issue  with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **platform-ios** | **1 issue  with this label** |
|   | [flutter/engine#8202](https://github.com/flutter/engine/pull/8202) [platform_view] iOSP platformView composition optimize. |
| **prod: API break** | **1 issue  with this label** |
|   | [flutter/engine#8274](https://github.com/flutter/engine/pull/8274) [ui] Add null check in FontWeight.lerp |
| **severe: crash** | **1 issue  with this label** |
|   | [flutter/flutter#28651](https://github.com/flutter/flutter/issues/28651) Cannot execute operation because FlutterJNI is not attached to native. |
| **severe: new feature** | **1 issue  with this label** |
|   | [flutter/flutter#29390](https://github.com/flutter/flutter/pull/29390) Make expansion panel optionally toggle its state by tapping its header. |
| **t: flutter doctor** | **1 issue  with this label** |
|   | [flutter/flutter#8094](https://github.com/flutter/flutter/issues/8094) Flutter doesn't find xcode 8.2 |
| **t: xcode** | **1 issue  with this label** |
|   | [flutter/flutter#27765](https://github.com/flutter/flutter/pull/27765) Refactor local engine logic |
| **team: flakes** | **1 issue  with this label** |
|   | [flutter/flutter#28480](https://github.com/flutter/flutter/pull/28480) increase timeout |
| **type-bug** | **1 issue  with this label** |
|   | [dart-lang/sdk#35940](https://github.com/dart-lang/sdk/issues/35940) Dartanalyzer: Missing line/column information, Unexpected error for int-to-double cast |
| **waiting for PR to land (fixed)** | **1 issue  with this label** |
|   | [flutter/flutter#7999](https://github.com/flutter/flutter/issues/7999) Consider renaming the Design Principles guide |
| **○ platform-fuchsia** | **1 issue  with this label** |
|   | [flutter/flutter#28168](https://github.com/flutter/flutter/pull/28168) [flutter_tool,fuchsia_tester] Only require a test source dir for coverage |
| **❖ platform-windows** | **1 issue  with this label** |
|   | [flutter/flutter#28356](https://github.com/flutter/flutter/pull/28356) Log pub return code on failure on Windows |



We addressed 1231 issues.

## Changes in `flutter/engine` from `375739` to `ca31a7`
| Author | Commit |
|--------|--------|
|Ben Konyi <<EMAIL>>|Disable auth codes for Observatory test ([#8646](https://github.com/flutter/engine/issues/8646))|
|Matthew Dempsky <<EMAIL>>|Remove unnecessary DartIO::EntropySource wrapper ([#8635](https://github.com/flutter/engine/issues/8635))|
|Chinmay Garde <<EMAIL>>|Revert "Remove DartSnapshotBuffer and dry up snapshot resolution logic. ([#8640](https://github.com/flutter/engine/issues/8640))" ([#8644](https://github.com/flutter/engine/issues/8644))  This reverts commit 905ddb6ca9f9dcb6b1a7cea67d502da094867623.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart 7b9ab9f353..cf4444b803 (44 commits) ([#8639](https://github.com/flutter/engine/issues/8639))  cf4444b803 Revert "[ VM / Service ] Temporarily disable VM service auth codes by default" f8491950fe Create List<TypeParameterElement>, not Impl. fa7485f28e Build types for not simply bounded elements using dynamic type arguments. 9d376de55a [vm] Remove code collection. 0c0e49cd11 update keyword list to include "late" modifier 3cf3e4edde [dart/vm] Reduce trip count of osr test 503e20946c [vm] Start logging if shutdown takes too long. 2b9f968d2f Convert engine.dart to triple-slash comment style 73b917c014 [vm/compiler] Do not generate unnecessary field length guards 20fde9a6a8 Don't overwrite inherited types of fields with types of initializers. e6e2a196a3 Issue 36667. Check for unresolved URI in getLibraryByUri(). 6ac38a926c The synthetic default constructor should have reference. 92729e0cd3 Remove UndefinedTypeImpl. cf77b82f52 [analyzer] handle youtube and animation dartdoc directives 6384f629c0 new conflicting-modifiers error code f3703c1bce change "late" from built-in to keyword af9c01432c Remove unused import 7b6b8f8c43 Restore 'parameters' and 'typeParameters' for ParameterElementImpl. bc58bf598e Add summary serialization support for nullability suffixes. 8093f73424 [vm/dbc] Add UNREACHABLE() stubs to simulator_dbc.cc in 32-bit mode to make 32-build work 336fc9001c Fix for getting type of a DefaultFormalParameter during building types. e93c6b7ce1 Add AST representation of the late keyword 43eebea5a3 [kernel_worker] retry on failure 284a299543 Fix mustCallSuper for mixins; fixes [#36492](https://github.com/flutter/engine/issues/36492) 5a824581b7 Read directive annotations before returning offset. 703ccc974b Support for TypeParameter metadata. 242ed77040 Fix for FormalParameter without name. 92dd37081d Write / read Annotation element. d8a0d68bd6 Rename devcompiler to ddc 6d91643073 [vm, interpreter] Handle closure case of invoke-field-dispatchers as bytecode. b8a9711489 Update AstBuilder to handle "required" modifier ca28e2c8ce forward "required" modifier to parser listeners 250d931ecc When URI cannot be resolved, the library is null. 2491767a33 Use correct CompilationUnitElement for annotations. 4ef18fdb96 Fix nameOffset for Export/ImportElement. 7873dbdc1d Fix for duplicate declarations. 38049f3cb6 [vm, interpreter] Handle implicit static getters as bytecode. 714a082a5d Minimal integration of summary2 into AnalysisDriver. dd3530450d make "late" and "required" builtin keywords 836efd6520 Add a language_2 test for having an invalid type in an on clause 81e554ea43 parse "required" modifier for NNBD 1eab04ed3f Reland "[VM-debugger] resolve column breakpoint tokenposition into a single value" ec27b09e68 [vm, service] Fix deferral of service extension invocation to not be mislead by other OOB messages. 1e30bb90dc [vm/dbc] Support unboxed Int64 on DBC|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 067861e14821..9b67802a4cd0 (6 commits) ([#8641](https://github.com/flutter/engine/issues/8641))   https://skia.googlesource.com/skia.git/%2Blog/067861e14821..9b67802a4cd0  git log 067861e1482118dd7071c931a90127c5ca0c9b21..9b67802a4cd01fbbe5fcbe9f4117ff68e7b8e751 --date=short --no-merges --format=%ad %ae %s 2019-04-19 <EMAIL> Properly retrieve memory block in SkTextUtils 2019-04-18 <EMAIL> Roll recipe dependencies (trivial). 2019-04-18 <EMAIL> SkPDF: move type1 font code into single file 2019-04-18 <EMAIL> Swap front and back stencil sides when rendering bottom-left 2019-04-18 <EMAIL> Swap x/y radii for EllipticalRRectOp depending on matrix. 2019-04-18 <EMAIL> SkQP: consolidate cut_release scripts  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Chinmay Garde <<EMAIL>>|Remove DartSnapshotBuffer and dry up snapshot resolution logic. ([#8640](https://github.com/flutter/engine/issues/8640))  Fixes https://github.com/flutter/flutter/issues/26782.|
|Ben Konyi <<EMAIL>>|Enable VM service authentication codes by default ([#8594](https://github.com/flutter/engine/issues/8594))|
|liyuqian <<EMAIL>>|Generate layer unique id for raster cache key ([#8637](https://github.com/flutter/engine/issues/8637))  The raw pointer isn't a reliable id as the allocator can reuse an address that's just been released for another layer.  This will fix Fuchsia bug FL-216.  This problem was not affecting non-Fuchsia Flutter probably because non-Fuchsia Flutter purges the raster cache key much more frequently so we won't see a key collision. In Fuchsia, as the key has to wait for the Vulkan surface to render asynchronously, this suddenly becomes an issue.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 2c2240f66805..067861e14821 (6 commits) ([#8636](https://github.com/flutter/engine/issues/8636))   https://skia.googlesource.com/skia.git/%2Blog/2c2240f66805..067861e14821  git log 2c2240f66805ac852507f7eea010c153b42485e3..067861e1482118dd7071c931a90127c5ca0c9b21 --date=short --no-merges --format=%ad %ae %s 2019-04-18 <EMAIL> Roll recipe dependencies (trivial). 2019-04-18 <EMAIL> Roll recipe dependencies (trivial). 2019-04-18 <EMAIL> remove experimental from some APIs (that are no longer experiments) 2019-04-18 <EMAIL> Fix quad type detection from SkPoint array 2019-04-18 <EMAIL> Remove the axis-aligned vector special case to convexity checker 2019-04-18 <EMAIL> fixed assertion failure in skslc SPIR-V output  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Chinmay Garde <<EMAIL>>|Merge runtime lifecycle unittests into the base test target. ([#8634](https://github.com/flutter/engine/issues/8634))  `//flutter/runtime: runtime_lifecycle_unittests` was added because the these assumed that there was no VM already running in the process. Running other tests in the base target would mess up that assumption. Now that all test targets have been updated to make sure the VM instance does not leak, the tests in this target can be merged.  LUCI bots don’t need to be patched as these tests were only ever run on the trybots.|
|Ben Konyi <<EMAIL>>|Add support for authentication codes via MDNS on iOS ([#8625](https://github.com/flutter/engine/issues/8625))|
|Chinmay Garde <<EMAIL>>|Avoid leaking the VM in the shell unittests and assert VM state in existing tests. ([#8628](https://github.com/flutter/engine/issues/8628))|
|liyuqian <<EMAIL>>|Reland elevation test ([#8633](https://github.com/flutter/engine/issues/8633))  Reland "Add a unit test for PhysicalShapeLayer ([#8616](https://github.com/flutter/engine/issues/8616))"  This reverts commit 91b71070f55ffc38c432babc7d9bb68ac2720d8d.  The only change is the namespace.|
|Jason Simmons <<EMAIL>>|Add an adjustment to the line width check in LineBreaker::addWordBreak ([#8623](https://github.com/flutter/engine/issues/8623))  currentLineWidth is the width passed into Paragraph layout, which comes from the maxIntrinsicWidth returned by a previous call to Paragraph layout. That width is calculated by Layout::measureText.  postBreak is calculated from the character widths in the LineBreaker.  A slight mismatch between these two widths may unnecessarily cause the insertion of desperate breaks in addWordBreak.  Adding some slack to currentLineWidth works around this.  Fixes https://github.com/flutter/flutter/issues/30347|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia a94670bd08cd..2c2240f66805 (2 commits) ([#8632](https://github.com/flutter/engine/issues/8632))   https://skia.googlesource.com/skia.git/%2Blog/a94670bd08cd..2c2240f66805  git log a94670bd08cd52a19bf72223d63e2cc808b6b057..2c2240f66805ac852507f7eea010c153b42485e3 --date=short --no-merges --format=%ad %ae %s 2019-04-17 <EMAIL> Make more SkTypeface virtuals pure. 2019-04-18 <EMAIL> [infra] Fixes for chrome_release_branch  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Justin McCandless <<EMAIL>>|Tight Paragraph Width ([#8530](https://github.com/flutter/engine/issues/8530))  Calculate and expose paragraph tightWidth to the framework to allow drawing Text based on this.|
|Justin McCandless <<EMAIL>>|Glitchiness with Tab Characters ([#8591](https://github.com/flutter/engine/issues/8591))  Fix a bug that caused jumping and flashing when entering tab characters into a TextField.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 847d55be4e62..a94670bd08cd (1 commits) ([#8631](https://github.com/flutter/engine/issues/8631))   https://skia.googlesource.com/skia.git/%2Blog/847d55be4e62..a94670bd08cd  git log 847d55be4e6273fc3cd9c0b30c7bfc8a2d6575b7..a94670bd08cd52a19bf72223d63e2cc808b6b057 --date=short --no-merges --format=%ad %ae %s 2019-04-18 <EMAIL> Update Skia milestone to 76  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia ced2b79d00f9..847d55be4e62 (3 commits) ([#8630](https://github.com/flutter/engine/issues/8630))   https://skia.googlesource.com/skia.git/%2Blog/ced2b79d00f9..847d55be4e62  git log ced2b79d00f9370cbd874d1462757b554655648f..847d55be4e6273fc3cd9c0b30c7bfc8a2d6575b7 --date=short --no-merges --format=%ad %ae %s 2019-04-18 <EMAIL> Update go_deps asset 2019-04-18 <EMAIL> Roll third_party/externals/angle2 c832cdd75f75..82bcffa78747 (7 commits) 2019-04-18 <EMAIL> Roll third_party/externals/swiftshader 84c3a94e1450..abaecd5f2ba7 (6 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 14c90c1ca218..ced2b79d00f9 (1 commits) ([#8629](https://github.com/flutter/engine/issues/8629))   https://skia.googlesource.com/skia.git/%2Blog/14c90c1ca218..ced2b79d00f9  git log 14c90c1ca218428418b82be5684bb71f13e81f60..ced2b79d00f9370cbd874d1462757b554655648f --date=short --no-merges --format=%ad %ae %s 2019-04-18 <EMAIL> Roll ../src a3585f70a100..f968f64cdf27 (436 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Joseph Richey <<EMAIL>>|Allow building without python2 ([#8605](https://github.com/flutter/engine/issues/8605))  Almost all of the python build files in the flutter project work if `python` is `python2` or `python3`. This is the only area where print is incorrectly used (for `python3`).  Related issue: https://fuchsia-review.googlesource.com/c/fuchsia/+/272925|
|Chinmay Garde <<EMAIL>>|Test saving compilation traces. ([#8618](https://github.com/flutter/engine/issues/8618))|
|Chinmay Garde <<EMAIL>>|Avoid leaking the VM in runtime_unittests and update failing tests. ([#8626](https://github.com/flutter/engine/issues/8626))  The failing tests were depending on the old assumption that the VM would never shutdown.|
|Chinmay Garde <<EMAIL>>|Revert "Add a unit test for PhysicalShapeLayer ([#8616](https://github.com/flutter/engine/issues/8616))" ([#8627](https://github.com/flutter/engine/issues/8627))  This reverts commit 8b667b0178f135411ad72d7e5b3304cc46ff7dcf.|
|liyuqian <<EMAIL>>|Add a unit test for PhysicalShapeLayer ([#8616](https://github.com/flutter/engine/issues/8616))  An unnecessary PrerollContext copy is also removed. The added unit test will catch the error if we forget to subtract the elevation after the copy removal.  This change has been tested with the framework (`flutter test --local-engine=host_debug_unopt`).|
|Chinmay Garde <<EMAIL>>|Assert that all VM launches in the process have the same opinion on whether the VM should be leaked in the process. ([#8622](https://github.com/flutter/engine/issues/8622))  There is a possibility for a VM launch to not have a requisite shutdown if a previous launch asked to leak the VM instance in the process. Embedders have to ensure (via their Embedding APIs) that all instances of VM launch use consistent settings.|
|liyuqian <<EMAIL>>|Revert "Roll src/third_party/dart 7b9ab9f353..714a082a5d (7 commits)" ([#8624](https://github.com/flutter/engine/issues/8624))  This reverts commit 80e934e3241fa8b177beea5a6af5f06cc2939923.  Reason for the revert: breaks the post-submit Mac iOS build bot.|
|Chinmay Garde <<EMAIL>>|Avoid manually shutting down engine managed isolates. ([#8621](https://github.com/flutter/engine/issues/8621))  These are now shutdown by the VM and cleanup waits for their shutdown.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart 7b9ab9f353..714a082a5d (7 commits)  714a082a5d Minimal integration of summary2 into AnalysisDriver. dd3530450d make "late" and "required" builtin keywords 836efd6520 Add a language_2 test for having an invalid type in an on clause 81e554ea43 parse "required" modifier for NNBD 1eab04ed3f Reland "[VM-debugger] resolve column breakpoint tokenposition into a single value" ec27b09e68 [vm, service] Fix deferral of service extension invocation to not be mislead by other OOB messages. 1e30bb90dc [vm/dbc] Support unboxed Int64 on DBC|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 6f9047027be3..14c90c1ca218 (5 commits) ([#8619](https://github.com/flutter/engine/issues/8619))   https://skia.googlesource.com/skia.git/%2Blog/6f9047027be3..14c90c1ca218  git log 6f9047027be329719bb9fe79e6f892724e6a0fa2..14c90c1ca218428418b82be5684bb71f13e81f60 --date=short --no-merges --format=%ad %ae %s 2019-04-16 <EMAIL> Reland &[#34](https://github.com/flutter/engine/issues/34);Remove includes from SkFontTypes.h&[#34](https://github.com/flutter/engine/issues/34); 2019-04-17 <EMAIL> Add __init__.py to page_sets 2019-04-17 <EMAIL> Non-substantive changes 2019-04-17 <EMAIL> SkQP: fix model colorspace (16-bit gold images) 2019-04-17 <EMAIL> Add struct to wrap all the gpu flush options.  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Shi-Hao Hong <<EMAIL>>|Fix typo in comment ([#8617](https://github.com/flutter/engine/issues/8617))|
|liyuqian <<EMAIL>>|Rename flow namespace to flutter ([#8615](https://github.com/flutter/engine/issues/8615))  This follows our namespace change from shell to flutter: https://github.com/flutter/engine/pull/8520.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart bc99f1ca28..7b9ab9f353 (5 commits)  7b9ab9f353 Add error ranges to more diagnostic tests 3782197e95 Sort summary_common.dart 37285467db Improvements to null_assertion_ambiguous_test b0c98d57fa Add the new manifest error codes to error codes list ebdefa3f5f add new "late" and "required" modifiers|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 5155e09d1466..6f9047027be3 (5 commits) ([#8613](https://github.com/flutter/engine/issues/8613))   https://skia.googlesource.com/skia.git/%2Blog/5155e09d1466..6f9047027be3  git log 5155e09d146665be078494247092fa990d5ae4a7..6f9047027be329719bb9fe79e6f892724e6a0fa2 --date=short --no-merges --format=%ad %ae %s 2019-04-17 <EMAIL> Roll recipe dependencies (trivial). 2019-04-16 <EMAIL> Reland &[#34](https://github.com/flutter/engine/issues/34);Simplify SkTypeface::charsToGlyphs API to require UTF32 input&[#34](https://github.com/flutter/engine/issues/34); 2019-04-16 <EMAIL> Add flag to map buffer flags in GrCaps to indicate whether mapping a GrGpuBuffer for reading is synchronous or not. 2019-04-17 <EMAIL> [infra] More work on chrome_release_branch 2019-04-17 <EMAIL> Add clang-cl arm64 builds  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|bungeman <<EMAIL>>|Remove call to SkFont::setLinearMetrics ([#8612](https://github.com/flutter/engine/issues/8612))  This particular call doesn't do anything since the default is false anyway. In addition Skia is looking to remove this flag since setting it to true is now synonymous with setting the hinting to none.|
|stuartmorgan <<EMAIL>>|Add FLEPluginRegistry for macOS ([#8611](https://github.com/flutter/engine/issues/8611))  Creates a minimal FLEPluginRegistry protocol, which is a subset of the FlutterPluginRegistry.  This is a small step toward eventually merging the APIs, but allows changing the example project structure to better reflect what a future template will look like.|
|Zachary Anderson <<EMAIL>>|[fuchsia] Fix SceneUpdateContext for new PaintContext field ([#8608](https://github.com/flutter/engine/issues/8608))|
|Michael Klimushyn <<EMAIL>>|Pipe Z bounds from ViewportMetrics to Flow ([#8583](https://github.com/flutter/engine/issues/8583))  This is the first step in making Flutter aware of and responsive to Z bound overflow. On its own this patch shouldn't result in any changes in behavior. This will need to be followed up with a patch in Fuchsia's Flutter runner to set the Z bounds after this lands, and another patch in the engine actually implementing the desired overflow behavior.  This Z bound info is routed through the engine itself to make sure the bounds in flow are truly consistent from the Fuchsia runner. However these values should eventually be exposed to the framework as well.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart f0da03ca36..bc99f1ca28 (1 commits)  bc99f1ca28 Test a few more uses of the nullable type decorator|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 33906ad1e26f..5155e09d1466 (4 commits) ([#8609](https://github.com/flutter/engine/issues/8609))   https://skia.googlesource.com/skia.git/%2Blog/33906ad1e26f..5155e09d1466  git log 33906ad1e26fd36430a70712382b3edc3970213c..5155e09d146665be078494247092fa990d5ae4a7 --date=short --no-merges --format=%ad %ae %s 2019-04-17 <EMAIL> Revert &[#34](https://github.com/flutter/engine/issues/34);added more SPIR-V RelaxedPrecision decorations&[#34](https://github.com/flutter/engine/issues/34); 2019-04-17 <EMAIL> Make Metal render loop more stable 2019-04-17 <EMAIL> roll clang_win 2019-04-17 <EMAIL> Roll recipe dependencies (trivial).  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart d9c40462c2..f0da03ca36 (1 commits)  f0da03ca36 [vm/compiler] Do not include FPU registers on ARM if VFP is not supported|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia e88d4382e1cf..33906ad1e26f (1 commits) ([#8604](https://github.com/flutter/engine/issues/8604))   https://skia.googlesource.com/skia.git/%2Blog/e88d4382e1cf..33906ad1e26f  git log e88d4382e1cf0041ecc1f148e05fbf3b7d0fb7b1..33906ad1e26fd36430a70712382b3edc3970213c --date=short --no-merges --format=%ad %ae %s 2019-04-17 <EMAIL> Reland &[#34](https://github.com/flutter/engine/issues/34);Revert &[#34](https://github.com/flutter/engine/issues/34);Simplify SkTypeface::charsToGlyphs API to require UTF32 input&[#34](https://github.com/flutter/engine/issues/34);&[#34](https://github.com/flutter/engine/issues/34);  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 652b007a4cbb..e88d4382e1cf (4 commits) ([#8603](https://github.com/flutter/engine/issues/8603))   https://skia.googlesource.com/skia.git/%2Blog/652b007a4cbb..e88d4382e1cf  git log 652b007a4cbb0b8c716510949456243573129ff4..e88d4382e1cf0041ecc1f148e05fbf3b7d0fb7b1 --date=short --no-merges --format=%ad %ae %s 2019-04-17 <EMAIL> Update go_deps asset 2019-04-17 <EMAIL> Roll third_party/externals/angle2 e7da32f1c73e..c832cdd75f75 (7 commits) 2019-04-17 <EMAIL> Roll third_party/externals/swiftshader 25be5bb97518..84c3a94e1450 (6 commits) 2019-04-17 <EMAIL> Roll ../src 95629c4c410f..a3585f70a100 (459 commits)  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autoroll/README.md  If the roll is causing failures, please contact the current sheriff (<EMAIL>), and stop the roller if necessary.|
|stuartmorgan <<EMAIL>>|Add desktop shell unittests to test script ([#8600](https://github.com/flutter/engine/issues/8600))  Builds the unit test on all platforms, and adds them to the aggregate test script.|
|Ben Konyi <<EMAIL>>|Roll src/third_party/dart 8e73b3c006..d9c40462c2 (4 commits)  d9c40462c2 [vm] Rename kImplicitStaticFinalGetter to kImplicitStaticGetter. 9a5a720413 Remove dead/outdated lints from pkg/dev_compiler/README.md 9d15138c91 [tests] Revert the previous deletion of UI-as-Code const tests 523666a97a Fix messages_test.|
|skia-flutter-autoroll <<EMAIL>>|Roll src/third_party/skia 70ed05e53ad2..652b007a4cbb (1 commits) ([#8601](https://github.com/flutter/engine/issues/8601))   https://skia.googlesource.com/skia.git/%2Blog/70ed05e53ad2..652b007a4cbb  git log 70ed05e53ad244b3234415b447e5fcd53fb40e3b..652b007a4cbb0b8c716510949456243573129ff4 --date=short --no-merges --format=%ad %ae %s 2019-04-16 <EMAIL> Revert &[#34](https://github.com/flutter/engine/issues/34);Revert &[#34](https://github.com/flutter/engine/issues/34);Simplify SkTypeface::charsToGlyphs API to require UTF32 input&[#34](https://github.com/flutter/engine/issues/34);&[#34](https://github.com/flutter/engine/issues/34);  The AutoRoll server is located here: https://autoroll.skia.org/r/skia-flutter-autoroll  Documentation for the AutoRoller is here: https://skia.googlesource.com/buildbot/+/main/autor