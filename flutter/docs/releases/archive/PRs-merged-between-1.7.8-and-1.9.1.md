

# PRs closed in this release of flutter/flutter

From Fri Jun 21 22:31:55 2019 -0400 to Sun Aug 18 12:22:00 2019 -0700


[28090](https://github.com/flutter/flutter/pull/28090) Ensure that cache dirs and files have appropriate permissions (cla: yes, tool)

[29489](https://github.com/flutter/flutter/pull/29489) Made a few grammatical changes (cla: yes, team)

[32511](https://github.com/flutter/flutter/pull/32511) Rendering errors with root causes in the widget layer should have a reference to the widget (cla: yes, customer: countless, customer: headline, framework)

[32770](https://github.com/flutter/flutter/pull/32770) Dismiss modal with any button press (a: desktop, cla: yes, framework)

[32816](https://github.com/flutter/flutter/pull/32816) Add initial implementation of flutter assemble (cla: yes, tool)

[33140](https://github.com/flutter/flutter/pull/33140) flutter/tests support (cla: yes, team)

[33281](https://github.com/flutter/flutter/pull/33281) Update TextStyle and StrutStyle height docs (a: typography, cla: yes, d: api docs, framework, severe: API break)

[33688](https://github.com/flutter/flutter/pull/33688) Part 1: Skia Gold Testing (a: tests, cla: yes, framework)

[33936](https://github.com/flutter/flutter/pull/33936) New parameter for RawGestureDetector to customize semantics mapping (cla: yes, f: gestures, framework)

[34019](https://github.com/flutter/flutter/pull/34019) Selectable Text (a: text input, cla: yes, customer: amplify, customer: fuchsia, framework, severe: API break)

[34202](https://github.com/flutter/flutter/pull/34202) Remove `_debugWillReattachChildren` assertions from `_TableElement` (cla: yes, customer: payouts, framework)

[34252](https://github.com/flutter/flutter/pull/34252) Integrate dwds into flutter tool for web support (cla: yes, tool, ☸ platform-web)

[34298](https://github.com/flutter/flutter/pull/34298) Preserving SafeArea : Part 2 (cla: yes, customer: solaris, framework, severe: customer critical, waiting for tree to go green)

[34301](https://github.com/flutter/flutter/pull/34301) Make it possible to override the FLUTTER_TEST env variable (a: tests, cla: yes, customer: mulligan (g3), team, tool)

[34515](https://github.com/flutter/flutter/pull/34515) OutlineInputBorder adjusts for borderRadius that is too large (a: text input, cla: yes, f: material design, framework)

[34516](https://github.com/flutter/flutter/pull/34516) [flutter_tool] Fill in Fuchsia version string (cla: yes, tool)

[34573](https://github.com/flutter/flutter/pull/34573) Ensures flutter jar is added to all build types on plugin projects (cla: yes, t: gradle, tool, waiting for tree to go green)

[34597](https://github.com/flutter/flutter/pull/34597) [Material] Update slider gallery demo, including range slider (cla: yes, f: material design, framework)

[34599](https://github.com/flutter/flutter/pull/34599) [Material] ToggleButtons (cla: yes, f: material design, framework, severe: new feature)

[34624](https://github.com/flutter/flutter/pull/34624) Break down flutter doctor validations and results (cla: yes, t: flutter doctor, tool)

[34626](https://github.com/flutter/flutter/pull/34626) AsyncSnapshot.data to throw if error or no data (cla: yes, framework)

[34660](https://github.com/flutter/flutter/pull/34660) Add --target support for Windows and Linux (cla: yes, tool)

[34665](https://github.com/flutter/flutter/pull/34665) Selection handles position is off (a: text input, cla: yes, framework, severe: API break)

[34669](https://github.com/flutter/flutter/pull/34669) Bundle ios dependencies (cla: yes, tool)

[34676](https://github.com/flutter/flutter/pull/34676) Enable selection by default for password text field and expose api to… (a: text input, cla: yes, f: cupertino, f: material design, framework)

[34712](https://github.com/flutter/flutter/pull/34712) Fix FocusTraversalPolicy makes focus lost (a: desktop, cla: yes, framework)

[34723](https://github.com/flutter/flutter/pull/34723) CupertinoTextField vertical alignment (cla: yes, f: cupertino, framework)

[34752](https://github.com/flutter/flutter/pull/34752) [linux] Receives the unmodified characters obtained from GLFW (a: desktop, cla: yes, framework)

[34785](https://github.com/flutter/flutter/pull/34785) Tweak the display name of emulators (cla: yes, tool)

[34794](https://github.com/flutter/flutter/pull/34794) Add `emulatorID` field to devices in daemon (cla: yes, tool)

[34823](https://github.com/flutter/flutter/pull/34823) Introduce image loading performance test. (a: tests, cla: yes, framework)

[34869](https://github.com/flutter/flutter/pull/34869) [Material] Properly call onChangeStart and onChangeEnd in Range Slider (cla: yes, f: material design, framework)

[34870](https://github.com/flutter/flutter/pull/34870) Add test case for Flutter Issue #27677 as a benchmark. (cla: yes, engine, framework, severe: performance)

[34872](https://github.com/flutter/flutter/pull/34872) [Material] Support for hovered, focused, and pressed border color on `OutlineButton`s (cla: yes, f: material design, framework)

[34877](https://github.com/flutter/flutter/pull/34877) More shards (a: tests, cla: yes, team, waiting for tree to go green)

[34885](https://github.com/flutter/flutter/pull/34885) Reland: rename web device (cla: yes, tool)

[34895](https://github.com/flutter/flutter/pull/34895) Remove flutter_tools support for old AOT snapshotting (cla: yes)

[34896](https://github.com/flutter/flutter/pull/34896) Allow multi-root web builds  (cla: yes, tool)

[34906](https://github.com/flutter/flutter/pull/34906) Fix unused [applicationIcon] property on [showLicensePage] (cla: yes, f: material design, framework)

[34907](https://github.com/flutter/flutter/pull/34907) Fixed LicensePage to close page before loaded the License causes an error (cla: yes, f: material design, framework, severe: crash)

[34919](https://github.com/flutter/flutter/pull/34919) Remove duplicate error parts (cla: yes, framework, waiting for tree to go green)

[34932](https://github.com/flutter/flutter/pull/34932) Added onChanged property to TextFormField (cla: yes, f: material design, framework)

[34964](https://github.com/flutter/flutter/pull/34964) CupertinoTextField.onTap (cla: yes, f: cupertino)

[35017](https://github.com/flutter/flutter/pull/35017) sync lint list (cla: yes)

[35046](https://github.com/flutter/flutter/pull/35046) Add generated Icon diagram to api docs (cla: yes, d: api docs, d: examples, framework)

[35055](https://github.com/flutter/flutter/pull/35055) enable lint avoid_bool_literals_in_conditional_expressions (cla: yes)

[35056](https://github.com/flutter/flutter/pull/35056) enable lint use_full_hex_values_for_flutter_colors (cla: yes)

[35059](https://github.com/flutter/flutter/pull/35059) prepare for lint update of prefer_final_fields (cla: yes)

[35063](https://github.com/flutter/flutter/pull/35063) add documentation for conic path not supported (a: platform-views, cla: yes, d: api docs, framework, plugin)

[35066](https://github.com/flutter/flutter/pull/35066) Manual engine roll, Update goldens, improved wavy text decoration 0f9e297ad..185087a65f (a: typography, cla: yes, engine)

[35074](https://github.com/flutter/flutter/pull/35074) Attempt to enable tool coverage redux (a: tests, cla: yes, tool)

[35075](https://github.com/flutter/flutter/pull/35075) Allow for customizing SnackBar's content TextStyle in its theme (cla: yes, f: material design, framework)

[35084](https://github.com/flutter/flutter/pull/35084) Move findTargetDevices to DeviceManager (cla: yes, tool)

[35092](https://github.com/flutter/flutter/pull/35092) Add FlutterProjectFactory so that it can be overridden internally. (cla: yes, tool)

[35110](https://github.com/flutter/flutter/pull/35110) Always test semantics (a: accessibility, a: tests, cla: yes, framework, severe: API break)

[35129](https://github.com/flutter/flutter/pull/35129) [Material] Wrap Flutter Gallery's Expansion Panel Slider in padded Container to make room for Value Indicator. (cla: yes, f: material design, framework, severe: regression)

[35130](https://github.com/flutter/flutter/pull/35130) pass new users for release_smoke_tests (a: tests, cla: yes, team)

[35132](https://github.com/flutter/flutter/pull/35132) Reduce allocations by reusing a matrix for transient transforms in _transformRect (cla: yes)

[35136](https://github.com/flutter/flutter/pull/35136) Update Dark Theme disabledColor to White38 (cla: yes, f: material design, framework, severe: API break)

[35143](https://github.com/flutter/flutter/pull/35143) More HttpClientResponse Uint8List fixes (cla: yes)

[35149](https://github.com/flutter/flutter/pull/35149) More `HttpClientResponse implements Stream<Uint8List>` fixes (cla: yes)

[35150](https://github.com/flutter/flutter/pull/35150) Change didUpdateConfig to didUpdateWidget (cla: yes, d: api docs, framework)

[35157](https://github.com/flutter/flutter/pull/35157) Remove skip clause on tools coverage (cla: yes, team)

[35160](https://github.com/flutter/flutter/pull/35160) Move usage flutter create tests into memory filesystem. (a: tests, cla: yes, tool)

[35164](https://github.com/flutter/flutter/pull/35164) Update reassemble doc (cla: yes, customer: product, d: api docs, framework, severe: customer critical)

[35186](https://github.com/flutter/flutter/pull/35186) Make tool coverage collection resilient to sentinel coverage data (cla: yes, tool)

[35188](https://github.com/flutter/flutter/pull/35188) ensure test isolate is paused before collecting coverage (cla: yes, tool)

[35189](https://github.com/flutter/flutter/pull/35189) enable lints prefer_spread_collections and prefer_inlined_adds (cla: yes)

[35192](https://github.com/flutter/flutter/pull/35192) don't block any presubmit on coverage (cla: yes, tool)

[35197](https://github.com/flutter/flutter/pull/35197) [flutter_tool] Update Fuchsia SDK (cla: yes, tool)

[35206](https://github.com/flutter/flutter/pull/35206) Force-upgrade package deps (cla: yes)

[35207](https://github.com/flutter/flutter/pull/35207) refactor out selection handlers (a: text input, cla: yes, customer: amplify, customer: fuchsia, framework)

[35211](https://github.com/flutter/flutter/pull/35211) `child` param doc update in Ink and Ink.image (cla: yes, d: api docs, f: material design, framework)

[35217](https://github.com/flutter/flutter/pull/35217) Add flutter build aar (a: build, cla: yes, tool, waiting for tree to go green)

[35219](https://github.com/flutter/flutter/pull/35219) Text selection menu show/hide cases (a: text input, cla: yes, f: material design, framework)

[35221](https://github.com/flutter/flutter/pull/35221) Twiggle bit to exclude dev and beta from desktop and web (cla: yes, tool)

[35223](https://github.com/flutter/flutter/pull/35223) Navigator pushAndRemoveUntil Fix (cla: yes, customer: mulligan (g3), f: routes, framework, severe: crash, waiting for tree to go green)

[35225](https://github.com/flutter/flutter/pull/35225) add sample code for AnimatedContainer (a: animation, cla: yes, d: api docs, d: examples, framework)

[35231](https://github.com/flutter/flutter/pull/35231) Fix coverage collection (cla: yes, tool)

[35232](https://github.com/flutter/flutter/pull/35232) New benchmark: Gesture semantics (cla: yes, waiting for tree to go green)

[35233](https://github.com/flutter/flutter/pull/35233) Attempt skipping coverage shard if tools did not change (cla: yes)

[35237](https://github.com/flutter/flutter/pull/35237) Revert "Manual engine roll, Update goldens, improved wavy text decoration 0f9e297ad..185087a65f" (cla: yes)

[35242](https://github.com/flutter/flutter/pull/35242) Reland "Manual engine roll, Update goldens, improved wavy text decoration 0f9e297ad..185087a65f"" (cla: yes)

[35245](https://github.com/flutter/flutter/pull/35245) More preparation for HttpClientResponse implements Uint8List (cla: yes)

[35246](https://github.com/flutter/flutter/pull/35246) attempt to not skip coverage on post commit (cla: yes)

[35263](https://github.com/flutter/flutter/pull/35263) remove unnecessary ..toList() (cla: yes)

[35276](https://github.com/flutter/flutter/pull/35276) Revert "[Material] Support for hovered, focused, and pressed border color on `OutlineButton`s" (cla: yes)

[35278](https://github.com/flutter/flutter/pull/35278) Re-land "[Material] Support for hovered, focused, and pressed border color on `OutlineButton`s" (cla: yes)

[35280](https://github.com/flutter/flutter/pull/35280) benchmarkWidgets.semanticsEnabled default false. (cla: yes)

[35282](https://github.com/flutter/flutter/pull/35282) Add Container fallback to Ink build method (cla: yes, f: material design, framework)

[35288](https://github.com/flutter/flutter/pull/35288) Apply coverage skip math correctly (cla: yes)

[35290](https://github.com/flutter/flutter/pull/35290) tests for about page (cla: yes)

[35297](https://github.com/flutter/flutter/pull/35297) Fix the first frame logic in tracing and driver (cla: yes, engine, framework, severe: performance, team)

[35303](https://github.com/flutter/flutter/pull/35303) fix default artifacts to exclude ios and android (cla: yes, tool)

[35307](https://github.com/flutter/flutter/pull/35307) Clean up host_app_ephemeral Profile build settings (a: existing-apps, cla: yes, t: xcode, tool, ⌺‬ platform-ios)

[35335](https://github.com/flutter/flutter/pull/35335) Using custom exception class for network loading error (a: images, cla: yes, framework)

[35367](https://github.com/flutter/flutter/pull/35367) Add type to StreamChannel in generated test code. (cla: yes, tool)

[35392](https://github.com/flutter/flutter/pull/35392) Add timer checking and Fake http client to testbed (cla: yes, tool)

[35393](https://github.com/flutter/flutter/pull/35393) more ui-as-code (cla: yes, team)

[35406](https://github.com/flutter/flutter/pull/35406) Refactor signal and command line handler from resident runner (cla: yes, team, tool)

[35407](https://github.com/flutter/flutter/pull/35407) Manual engine roll (cla: yes, engine, team)

[35408](https://github.com/flutter/flutter/pull/35408) Remove print (cla: yes)

[35423](https://github.com/flutter/flutter/pull/35423) v1.7.8 hotfixes (cla: yes)

[35424](https://github.com/flutter/flutter/pull/35424) Introduce image_list performance benchmark that runs on jit(debug) build. (a: images, a: tests, cla: yes, framework)

[35464](https://github.com/flutter/flutter/pull/35464) Manual roll of engine 45b66b7...ffba2f6 (cla: yes, team)

[35465](https://github.com/flutter/flutter/pull/35465) Mark update-packages as non-experimental (cla: yes, tool)

[35467](https://github.com/flutter/flutter/pull/35467) Mark update-packages as non-experimental (cla: yes, tool)

[35468](https://github.com/flutter/flutter/pull/35468) Add colorFilterLayer/Widget (a: accessibility, cla: yes, customer: octopod, framework, waiting for tree to go green)

[35477](https://github.com/flutter/flutter/pull/35477) Update macrobenchmarks README and app name (a: tests, cla: yes, team)

[35480](https://github.com/flutter/flutter/pull/35480) Update the help message on precache command for less confusion (cla: yes, tool)

[35481](https://github.com/flutter/flutter/pull/35481) add APK build time benchmarks (cla: yes, tool)

[35482](https://github.com/flutter/flutter/pull/35482) Use the new service protocol message names (cla: yes)

[35487](https://github.com/flutter/flutter/pull/35487) Fix RenderFittedBox when child.size.isEmpty (a: accessibility, cla: yes, framework)

[35491](https://github.com/flutter/flutter/pull/35491) Include tags in SemanticsNode debug properties (cla: yes, framework)

[35492](https://github.com/flutter/flutter/pull/35492) Re-apply 'Add currentSystemFrameTimeStamp to SchedulerBinding' (cla: yes, framework)

[35493](https://github.com/flutter/flutter/pull/35493) Do not use ideographic baseline for RenderPargraph baseline (a: typography, cla: yes, engine, framework)

[35495](https://github.com/flutter/flutter/pull/35495) mark windows and macos chrome dev mode as flaky (cla: yes, team)

[35496](https://github.com/flutter/flutter/pull/35496) [Material] Text scale and wide label fixes for Slider and Range Slider value indicator shape (cla: yes, f: material design)

[35499](https://github.com/flutter/flutter/pull/35499) Added MaterialApp.themeMode to control which theme is used. (cla: yes, f: material design, framework)

[35548](https://github.com/flutter/flutter/pull/35548) Various doc fixes (cla: yes, framework)

[35556](https://github.com/flutter/flutter/pull/35556) ios (iPhone6) and iPhone XS tiles_scroll_perf tests (cla: yes, severe: performance, team, ⌺‬ platform-ios)

[35560](https://github.com/flutter/flutter/pull/35560) Support for elevation based dark theme overlay color in the Material widget (cla: yes, f: material design, framework)

[35573](https://github.com/flutter/flutter/pull/35573) update packages (cla: yes, team)

[35574](https://github.com/flutter/flutter/pull/35574) Fix semantics for floating pinned sliver app bar (a: accessibility, cla: yes, f: scrolling, framework, waiting for tree to go green)

[35646](https://github.com/flutter/flutter/pull/35646) Prepare for Socket implements Stream<Uint8List> (cla: yes)

[35657](https://github.com/flutter/flutter/pull/35657) Remove paused check for tooling tests (cla: yes, tool)

[35681](https://github.com/flutter/flutter/pull/35681) Disable incremental compiler in dartdevc (cla: yes, tool)

[35684](https://github.com/flutter/flutter/pull/35684) Fix typo in main.dart templates (cla: yes, d: api docs, framework)

[35708](https://github.com/flutter/flutter/pull/35708) disable a test case in xcode_backend.sh (cla: yes, tool)

[35709](https://github.com/flutter/flutter/pull/35709) Remove web, fuchsia, and unsupported devices from all (cla: yes, tool)

[35725](https://github.com/flutter/flutter/pull/35725) Update annotated region findAll implementation to use Iterables directly. (cla: yes, framework)

[35728](https://github.com/flutter/flutter/pull/35728) Added demo projects for splash screen support on Android. (a: existing-apps, cla: yes, team)

[35731](https://github.com/flutter/flutter/pull/35731) Keep LLDB connection to iOS device alive while running from CLI. (cla: yes, tool)

[35743](https://github.com/flutter/flutter/pull/35743) Simple Doc Fixes (cla: yes, d: api docs, framework)

[35745](https://github.com/flutter/flutter/pull/35745) enable lint prefer_if_null_operators (cla: yes, team)

[35749](https://github.com/flutter/flutter/pull/35749) add iOS build benchmarks (cla: yes, team, tool)

[35750](https://github.com/flutter/flutter/pull/35750) use sentence case in error message titles (cla: yes, framework)

[35756](https://github.com/flutter/flutter/pull/35756) Remove @objc inference build setting (cla: yes, t: xcode, tool)

[35762](https://github.com/flutter/flutter/pull/35762) Refactor keymapping for resident_runner (cla: yes)

[35763](https://github.com/flutter/flutter/pull/35763) UIApplicationLaunchOptionsKey -> UIApplication.LaunchOptionsKey (cla: yes, t: xcode, tool, ⌺‬ platform-ios)

[35765](https://github.com/flutter/flutter/pull/35765) Use public _registerService RPC in flutter_tools (cla: yes, tool)

[35767](https://github.com/flutter/flutter/pull/35767) set targets of zero percent for tools codecoverage (cla: yes, tool)

[35775](https://github.com/flutter/flutter/pull/35775) Add platform_interaction_test_swift to devicelab (a: tests, cla: yes, framework, p: framework, plugin, ⌺‬ platform-ios)

[35777](https://github.com/flutter/flutter/pull/35777) Fixed logLevel filter bug so that filter now works as expected (cla: yes, team)

[35778](https://github.com/flutter/flutter/pull/35778) Build all example projects in CI build smoke test (a: tests, cla: yes, team)

[35780](https://github.com/flutter/flutter/pull/35780) Remove CoocaPods support from layers example app (a: tests, cla: yes, d: examples, team)

[35785](https://github.com/flutter/flutter/pull/35785) Remove reverseDuration from implicitly animated widgets, since it's ignored. (a: animation, cla: yes, framework, severe: API break)

[35792](https://github.com/flutter/flutter/pull/35792) disable web tests (cla: yes)

[35810](https://github.com/flutter/flutter/pull/35810) SliverFillRemaining accounts for child size when hasScrollBody is false (cla: yes, f: scrolling, framework, waiting for tree to go green)

[35814](https://github.com/flutter/flutter/pull/35814) Roll engine e695a516f..75387dbc1 (8 commits) (cla: yes, team)

[35825](https://github.com/flutter/flutter/pull/35825) Fixed build of example code to use new binary messenger API. (cla: yes, team)

[35828](https://github.com/flutter/flutter/pull/35828) Cleanup widgets/sliver_persistent_header.dart with resolution of dart-lang/sdk#31543 (cla: yes, framework)

[35829](https://github.com/flutter/flutter/pull/35829) iOS 13 scrollbar (cla: yes, f: cupertino, framework)

[35833](https://github.com/flutter/flutter/pull/35833) Disable CocoaPods input and output paths in Xcode build phase for ephemeral add-to-app project (a: existing-apps, cla: yes, tool, ⌺‬ platform-ios)

[35839](https://github.com/flutter/flutter/pull/35839) use pub run for create test and remove [INFO] logs (cla: yes, tool)

[35846](https://github.com/flutter/flutter/pull/35846) move reload and restart handling into terminal (cla: yes, tool)

[35878](https://github.com/flutter/flutter/pull/35878) Add flag to use root navigator for showModalBottomSheet (cla: yes, f: material design, framework)

[35892](https://github.com/flutter/flutter/pull/35892) Doc fixes (cla: yes, d: api docs, d: examples, framework)

[35906](https://github.com/flutter/flutter/pull/35906) Add anchors to samples (cla: yes, team)

[35913](https://github.com/flutter/flutter/pull/35913) Change focus example to be more canonical (and correct) (cla: yes, framework)

[35919](https://github.com/flutter/flutter/pull/35919) Animation API doc improvments (a: animation, cla: yes, framework, waiting for tree to go green)

[35926](https://github.com/flutter/flutter/pull/35926) Add example showing how to move from one field to the next. (cla: yes, d: api docs, d: examples, framework)

[35932](https://github.com/flutter/flutter/pull/35932) Upgraded framework packages with 'flutter update-packages --force-upgrade'. (cla: yes, framework)

[35941](https://github.com/flutter/flutter/pull/35941) SliverLayoutBuilder (cla: yes, f: scrolling, framework)

[35942](https://github.com/flutter/flutter/pull/35942) Use test instead of test_api package in platform_channel_swift example tests (a: tests, cla: yes, d: examples, team)

[35971](https://github.com/flutter/flutter/pull/35971) [ImgBot] Optimize images (cla: yes, team)

[35979](https://github.com/flutter/flutter/pull/35979) Optimizes gesture recognizer fixes #35658 (cla: yes, f: gestures, framework)

[35991](https://github.com/flutter/flutter/pull/35991) Enable widget load assets in its own package in test (a: tests, cla: yes, tool)

[35996](https://github.com/flutter/flutter/pull/35996) Revert "Keep LLDB connection to iOS device alive while running from CLI." (cla: yes)

[35999](https://github.com/flutter/flutter/pull/35999) Deflake ImageProvider.evict test (a: images, a: tests, cla: yes, team: flakes)

[36006](https://github.com/flutter/flutter/pull/36006) fix linesplitter (cla: yes, team)

[36017](https://github.com/flutter/flutter/pull/36017) Move reporting files to reporting/ (cla: yes, tool)

[36026](https://github.com/flutter/flutter/pull/36026) add the transformPoint and transformRect benchmarks (cla: yes, team)

[36028](https://github.com/flutter/flutter/pull/36028) Fix slider preferred height (cla: yes, f: material design, framework)

[36030](https://github.com/flutter/flutter/pull/36030) [Material] Implement TooltipTheme and Tooltip.textStyle, fix Tooltip debugLabel, update Tooltip defaults (cla: yes, f: material design, framework, severe: API break, severe: new feature)

[36071](https://github.com/flutter/flutter/pull/36071) Revert "Bundle ios dependencies" (cla: yes, team, tool)

[36082](https://github.com/flutter/flutter/pull/36082) Add better handling of JSON-RPC exception (cla: yes, tool)

[36084](https://github.com/flutter/flutter/pull/36084) handle google3 version of pb (cla: yes, tool)

[36087](https://github.com/flutter/flutter/pull/36087) Update visual style of CupertinoSwitch to match iOS 13 (cla: yes, f: cupertino, framework)

[36088](https://github.com/flutter/flutter/pull/36088) Add PopupMenuTheme to enable theming color, shape, elevation, text style of Menu (cla: yes, f: material design, framework)

[36089](https://github.com/flutter/flutter/pull/36089) Fix flaky peer connection (a: tests, cla: yes, framework)

[36090](https://github.com/flutter/flutter/pull/36090) don't require diffs to have a percentage coverage greater (cla: yes, team)

[36093](https://github.com/flutter/flutter/pull/36093) Reland bundle ios deps (cla: yes, team, tool)

[36094](https://github.com/flutter/flutter/pull/36094) Revert "Part 1: Skia Gold Testing" (cla: yes, f: cupertino, f: material design, framework, team)

[36096](https://github.com/flutter/flutter/pull/36096) Revert "Merge branches 'master' and 'master' of github.com:flutter/fl… (cla: yes, tool)

[36097](https://github.com/flutter/flutter/pull/36097) Fix nested scroll view can rebuild without layout (cla: yes, f: scrolling, framework, severe: crash)

[36098](https://github.com/flutter/flutter/pull/36098) Be clearer about errors in customer testing script (cla: yes, team)

[36102](https://github.com/flutter/flutter/pull/36102) Move buildable module test to a module test (cla: yes, team)

[36103](https://github.com/flutter/flutter/pull/36103) Re-land "Part 1: Skia Gold Testing" (a: tests, cla: yes, framework, waiting for tree to go green)

[36105](https://github.com/flutter/flutter/pull/36105) [flutter_tool] Catch a yaml parse failure during project creation (cla: yes, team, tool)

[36106](https://github.com/flutter/flutter/pull/36106) Updated ColorScheme.dark() colors to match the Material Dark theme specification (cla: yes, f: material design, framework, severe: API break)

[36108](https://github.com/flutter/flutter/pull/36108) Move tools tests into a general.shard directory in preparation to changing how we shard tools tests (cla: yes, tool)

[36109](https://github.com/flutter/flutter/pull/36109) Catch exceptions thrown by runChecked* when possible (cla: yes, tool, waiting for tree to go green)

[36122](https://github.com/flutter/flutter/pull/36122) Make sure add-to-app build bundle from outer xcodebuild/gradlew sends analytics (cla: yes, team, tool)

[36123](https://github.com/flutter/flutter/pull/36123) Attempt to re-enable integration_tests-macos (a: tests, cla: yes, team, team: flakes)

[36135](https://github.com/flutter/flutter/pull/36135) add a kIsWeb constant to foundation (cla: yes, framework)

[36138](https://github.com/flutter/flutter/pull/36138) Implement feature flag system for flutter tools (cla: yes, tool)

[36174](https://github.com/flutter/flutter/pull/36174) [cupertino_icons] Add glyph refs for brightness #16102 (cla: yes, f: cupertino, framework)

[36194](https://github.com/flutter/flutter/pull/36194) Keep LLDB connection to iOS device alive while running from CLI.  (cla: yes, tool)

[36197](https://github.com/flutter/flutter/pull/36197) Fix windows, exclude widgets from others (cla: yes, team)

[36199](https://github.com/flutter/flutter/pull/36199) Don't try to flutterExit if isolate is still paused (cla: yes, tool)

[36200](https://github.com/flutter/flutter/pull/36200) Refactoring the Android_views tests app to prepare for adding the iOS platform view tests (a: platform-views, a: tests, cla: yes, team)

[36202](https://github.com/flutter/flutter/pull/36202) Add clarifying docs on MaterialButton.colorBrightness (cla: yes, d: api docs, f: material design, framework)

[36208](https://github.com/flutter/flutter/pull/36208) [flutter_tool] Allow analytics without a terminal attached (cla: yes, tool)

[36213](https://github.com/flutter/flutter/pull/36213) Use DeviceManager instead of device to determine if device supports project. (cla: yes, tool)

[36217](https://github.com/flutter/flutter/pull/36217) Split Mouse from Listener (a: desktop, cla: yes, framework, severe: API break, waiting for tree to go green)

[36218](https://github.com/flutter/flutter/pull/36218) release lock in flutter pub context (cla: yes, tool)

[36237](https://github.com/flutter/flutter/pull/36237) Recommend to use the final version of CDN support for the trunk specs repo. (cla: yes, tool)

[36240](https://github.com/flutter/flutter/pull/36240) Rearrange flutter assemble implementation (cla: yes, tool)

[36243](https://github.com/flutter/flutter/pull/36243) Allow semantics labels to be shorter or longer than raw text (a: accessibility, cla: yes, customer: money (g3), framework, waiting for tree to go green)

[36262](https://github.com/flutter/flutter/pull/36262) Prevents infinite loop in Table._computeColumnWidths (cla: yes, framework)

[36270](https://github.com/flutter/flutter/pull/36270) Change Future.done to Future.whenComplete (a: tests, cla: yes, framework, team, waiting for tree to go green)

[36288](https://github.com/flutter/flutter/pull/36288) Throw exception if instantiating IOSDevice on non-mac os platform (cla: yes, tool)

[36289](https://github.com/flutter/flutter/pull/36289) FakeHttpClientResponse improvements (cla: yes, tool)

[36293](https://github.com/flutter/flutter/pull/36293) Revert "Keep LLDB connection to iOS device alive while running from CLI. " (cla: yes, tool)

[36297](https://github.com/flutter/flutter/pull/36297) Add multi-line flag to semantics (a: accessibility, cla: yes, framework, ☸ platform-web)

[36302](https://github.com/flutter/flutter/pull/36302) Issues/30526 gc (cla: yes, framework)

[36303](https://github.com/flutter/flutter/pull/36303) Add sync star benchmark cases (a: accessibility, cla: yes, team)

[36317](https://github.com/flutter/flutter/pull/36317) Disable flaky tests on Windows (cla: yes, f: material design, framework)

[36318](https://github.com/flutter/flutter/pull/36318) Include flutter_runner in precache artifacts. (cla: yes, tool)

[36319](https://github.com/flutter/flutter/pull/36319) Revert "Fix semantics for floating pinned sliver app bar" (cla: yes, framework)

[36327](https://github.com/flutter/flutter/pull/36327) Fix invocations of ideviceinstaller not passing DYLD_LIBRARY_PATH (cla: yes, tool)

[36331](https://github.com/flutter/flutter/pull/36331) Minor fixes to precache help text (attempt #2) (cla: yes, tool)

[36333](https://github.com/flutter/flutter/pull/36333) fix sliver fixed pinned appbar (cla: yes, framework, waiting for tree to go green)

[36334](https://github.com/flutter/flutter/pull/36334) Added a Driver API that waits until frame sync (a: tests, cla: yes, framework)

[36379](https://github.com/flutter/flutter/pull/36379) Add missing test case for Usage (cla: yes, tool)

[36384](https://github.com/flutter/flutter/pull/36384) rename the test app android_views to platform_views  (cla: yes, team)

[36391](https://github.com/flutter/flutter/pull/36391) Adds doc example for Listview and pageview (cla: yes, d: api docs, framework)

[36392](https://github.com/flutter/flutter/pull/36392) Increase pattern that matches operation duration in log_test (a: tests, cla: yes, tool, waiting for tree to go green)

[36394](https://github.com/flutter/flutter/pull/36394) Add missing protobuf dependency (cla: yes, tool)

[36396](https://github.com/flutter/flutter/pull/36396) Optimize the transformRect and transformPoint methods in matrix_utils. (cla: yes, framework)

[36399](https://github.com/flutter/flutter/pull/36399) Added ThemeMode support to the Flutter Gallery (cla: yes, d: examples, team, team: gallery)

[36402](https://github.com/flutter/flutter/pull/36402) Teach render objects to reuse engine layers (cla: yes, framework, severe: API break, severe: performance, ☸ platform-web)

[36404](https://github.com/flutter/flutter/pull/36404) Make test back button label deterministic (cla: yes, team)

[36409](https://github.com/flutter/flutter/pull/36409) Add searchFieldLabel to SearchDelegate in order to show a custom hint (cla: yes, f: material design, framework)

[36410](https://github.com/flutter/flutter/pull/36410) Add plumbing for hello world startup test in devicelab (cla: yes, team)

[36411](https://github.com/flutter/flutter/pull/36411) Implement InputDecorationTheme copyWith, ==, hashCode (cla: yes, f: material design, framework, severe: new feature)

[36413](https://github.com/flutter/flutter/pull/36413) Revert "Roll engine f3482700474a..1af19ae67dd1 (4 commits)" (cla: yes, engine)

[36418](https://github.com/flutter/flutter/pull/36418) Add testing to screenshot and printDetails method (cla: yes, tool)

[36421](https://github.com/flutter/flutter/pull/36421) doc : ReorderableListView - added youtube video from WidgetOfTheWeek … (cla: yes, f: material design, framework)

[36428](https://github.com/flutter/flutter/pull/36428) Bump engine version (cla: yes, engine, team)

[36431](https://github.com/flutter/flutter/pull/36431) Re-enable `flutter test` expression evaluation tests (a: tests, cla: yes, tool)

[36434](https://github.com/flutter/flutter/pull/36434) Clean up flutter driver device detection. (cla: yes, tool)

[36460](https://github.com/flutter/flutter/pull/36460) Add images and update examples for top widgets: (cla: yes, d: api docs, d: examples, f: material design, framework)

[36465](https://github.com/flutter/flutter/pull/36465) Use FlutterFeatures to configure web and desktop devices (cla: yes, tool)

[36468](https://github.com/flutter/flutter/pull/36468) Fix test_widgets-windows not running tests (a: tests, cla: yes, framework, team, waiting for tree to go green)

[36471](https://github.com/flutter/flutter/pull/36471) Enable bitcode compilation for AOT (cla: yes)

[36481](https://github.com/flutter/flutter/pull/36481) Remove untested code (cla: yes, tool)

[36482](https://github.com/flutter/flutter/pull/36482) Sped up shader warmup by only drawing on a 100x100 surface (cla: yes, framework)

[36485](https://github.com/flutter/flutter/pull/36485) Add text border docs (a: typography, cla: yes, d: api docs, framework)

[36490](https://github.com/flutter/flutter/pull/36490) [flutter_tool] Send analytics command before the command runs (cla: yes, tool)

[36492](https://github.com/flutter/flutter/pull/36492) Add GitHub CODEOWNERS file to auto-add reviewers by path (cla: yes, team)

[36493](https://github.com/flutter/flutter/pull/36493) Fixes sliver list does not layout firstchild when child reordered (cla: yes, framework)

[36498](https://github.com/flutter/flutter/pull/36498) Clean up host_app_ephemeral_cocoapods Profile build settings (a: existing-apps, cla: yes, t: xcode, tool, ⌺‬ platform-ios)

[36503](https://github.com/flutter/flutter/pull/36503) Disabling Firebase Test Lab smoke test to unblock autoroller (cla: yes)

[36507](https://github.com/flutter/flutter/pull/36507) Bump engine version (cla: yes, engine, team, tool)

[36510](https://github.com/flutter/flutter/pull/36510) flutter update-packages --force-upgrade (cla: yes, team)

[36512](https://github.com/flutter/flutter/pull/36512) Renamed the Driver API waitUntilFrameSync to waitUntilNoPendingFrame (a: tests, cla: yes, framework)

[36513](https://github.com/flutter/flutter/pull/36513) Fix `flutter pub -v` (cla: yes, tool)

[36545](https://github.com/flutter/flutter/pull/36545) [flutter_tool] Send the local time to analytics with screens and events (cla: yes, tool, work in progress; do not review)

[36546](https://github.com/flutter/flutter/pull/36546) Unskip date_picker_test on Windows as underlying issue 19696 was fixed. (cla: yes, f: material design, framework)

[36548](https://github.com/flutter/flutter/pull/36548) Fix the web builds by reverting version bump of build_modules (cla: yes, tool)

[36549](https://github.com/flutter/flutter/pull/36549) fix number encoding in message codecs for the Web (cla: yes, framework)

[36553](https://github.com/flutter/flutter/pull/36553) Load assets during test from file system instead of manifest. (a: tests, cla: yes, framework)

[36556](https://github.com/flutter/flutter/pull/36556) Fix usage test to use local usage (cla: yes, tool)

[36560](https://github.com/flutter/flutter/pull/36560) [flutter_tools] Add some useful commands to the README.md (cla: yes, tool)

[36564](https://github.com/flutter/flutter/pull/36564) Make sure fx flutter attach can find devices (cla: yes, tool)

[36569](https://github.com/flutter/flutter/pull/36569) Some minor cleanup for flutter_tools (cla: yes, tool)

[36570](https://github.com/flutter/flutter/pull/36570) Some minor fixes to the tool_coverage tool (cla: yes, tool)

[36571](https://github.com/flutter/flutter/pull/36571) Some minor cleanup in devicelab (cla: yes, team)

[36579](https://github.com/flutter/flutter/pull/36579) Add gradient text docs (a: typography, cla: yes, framework)

[36585](https://github.com/flutter/flutter/pull/36585) Place build outputs under dart tool (cla: yes, tool)

[36589](https://github.com/flutter/flutter/pull/36589) Update Localizations: added 24 new locales (reprise) (a: internationalization, cla: yes, f: cupertino, f: material design)

[36595](https://github.com/flutter/flutter/pull/36595) More resident runner tests (cla: yes, tool)

[36598](https://github.com/flutter/flutter/pull/36598) Expose functionality to compile dart to kernel for the VM (cla: yes, tool)

[36618](https://github.com/flutter/flutter/pull/36618) Revert "AsyncSnapshot.data to throw if error or no data" (cla: yes, framework)

[36654](https://github.com/flutter/flutter/pull/36654) Revert "Use FlutterFeatures to configure web and desktop devices" (cla: yes, team, tool)

[36679](https://github.com/flutter/flutter/pull/36679) add line-length to `flutter format` command line (cla: yes, tool)

[36690](https://github.com/flutter/flutter/pull/36690) Updating cirrus fingerprint script to include goldens version (a: tests, cla: yes, framework, waiting for tree to go green)

[36695](https://github.com/flutter/flutter/pull/36695) Android visible password input type support (cla: yes, framework)

[36698](https://github.com/flutter/flutter/pull/36698) fixes iphone force press keyboard select crashes (cla: yes, framework)

[36699](https://github.com/flutter/flutter/pull/36699) Reland: use flutter features for web and desktop (cla: yes, team, tool)

[36717](https://github.com/flutter/flutter/pull/36717) Fix devicelab tests that did not enable web config. (cla: yes, team)

[36722](https://github.com/flutter/flutter/pull/36722) Skip flaky test windows (cla: yes, tool)

[36727](https://github.com/flutter/flutter/pull/36727) Add missing config to create (cla: yes, team, tool)

[36731](https://github.com/flutter/flutter/pull/36731) Revert "Add flutter build aar" (cla: yes, team, tool)

[36732](https://github.com/flutter/flutter/pull/36732) Flutter build aar (a: build, cla: yes, team, tool, waiting for tree to go green)

[36768](https://github.com/flutter/flutter/pull/36768) add an error count field to the Flutter.Error event (cla: yes, framework)

[36773](https://github.com/flutter/flutter/pull/36773) Expose build-dir config option (cla: yes, tool)

[36774](https://github.com/flutter/flutter/pull/36774) Parameterize CoverageCollector with a library name predicate (cla: yes, tool)

[36784](https://github.com/flutter/flutter/pull/36784) [flutter_tool] Improve Windows flutter clean error message (cla: yes, tool)

[36785](https://github.com/flutter/flutter/pull/36785) [flutter_tool] Clean up usage events and custom dimensions (cla: yes, tool)

[36787](https://github.com/flutter/flutter/pull/36787) Check for directory instead of path separator (cla: yes, tool)

[36793](https://github.com/flutter/flutter/pull/36793) Vend Flutter module App.framework as a local CocoaPod pod to be installed by a host app (a: existing-apps, cla: yes, t: xcode, team, tool, ⌺‬ platform-ios)

[36805](https://github.com/flutter/flutter/pull/36805) Allow flavors and custom build types in host app (a: build, a: existing-apps, cla: yes, t: gradle, team, tool, waiting for tree to go green)

[36832](https://github.com/flutter/flutter/pull/36832) Remove flaky check for analyzer message. (cla: yes, tool)

[36845](https://github.com/flutter/flutter/pull/36845) Improve Windows build failure message (cla: yes, tool, waiting for tree to go green)

[36851](https://github.com/flutter/flutter/pull/36851) Revert "[Material] Implement TooltipTheme and Tooltip.textStyle, fix Tooltip debugLabel, update Tooltip defaults" (cla: yes, f: material design, framework)

[36856](https://github.com/flutter/flutter/pull/36856) [Material] Implement TooltipTheme and Tooltip.textStyle, update Tooltip defaults (cla: yes, f: material design, framework, severe: API break, severe: new feature)

[36857](https://github.com/flutter/flutter/pull/36857) Ensure user-thrown errors have ErrorSummary nodes (cla: yes, framework)

[36860](https://github.com/flutter/flutter/pull/36860) Remove Chain terse parsing (cla: yes, tool)

[36866](https://github.com/flutter/flutter/pull/36866) Tests for `flutter test [some_directory]` (cla: yes, team, tool)

[36867](https://github.com/flutter/flutter/pull/36867) Add reference to StrutStyle from TextStyle (cla: yes, framework)

[36874](https://github.com/flutter/flutter/pull/36874) Adjust phrasing of features (cla: yes, tool)

[36877](https://github.com/flutter/flutter/pull/36877) Revert "Dismiss modal with any button press" (cla: yes, framework)

[36880](https://github.com/flutter/flutter/pull/36880) [Material] Create material Banner component (cla: yes, f: material design, framework)

[36884](https://github.com/flutter/flutter/pull/36884) Unbreak build_runner (cla: yes, team, tool)

[36885](https://github.com/flutter/flutter/pull/36885) Manual roll of flutter/engine@ef99738...72341ed (cla: yes, engine, tool)

[36886](https://github.com/flutter/flutter/pull/36886) Add annotation dependency to plugins (cla: yes, team, tool, waiting for tree to go green)

[36887](https://github.com/flutter/flutter/pull/36887) Fix thumb size calculation (cla: yes, f: cupertino, framework)

[36893](https://github.com/flutter/flutter/pull/36893) Fix minor typos (cla: yes, framework)

[36895](https://github.com/flutter/flutter/pull/36895) Mark splash test flaky until proved stable. (cla: yes, team)

[36901](https://github.com/flutter/flutter/pull/36901) Run Gradle tests on Windows (a: build, cla: yes, team, tool)

[36949](https://github.com/flutter/flutter/pull/36949) Remove stdout related to settings_aar.gradle (a: tests, cla: yes, t: flutter driver, team)

[36955](https://github.com/flutter/flutter/pull/36955) Extract common PlatformView functionality: Painting and Semantics (a: platform-views, cla: yes, framework)

[36956](https://github.com/flutter/flutter/pull/36956) Redo: Modal dismissed by any button (cla: yes, framework, waiting for tree to go green)

[36963](https://github.com/flutter/flutter/pull/36963) Add margins to tooltips (cla: yes, f: material design, framework, severe: new feature)

[36964](https://github.com/flutter/flutter/pull/36964) Interactive size const (cla: yes, f: cupertino, f: material design, framework, severe: API break)

[36966](https://github.com/flutter/flutter/pull/36966) Roll back the AAR build experiment (cla: yes, tool)

[36969](https://github.com/flutter/flutter/pull/36969) devicelab: replace the FLUTTER_ENGINE environment variable with the new local engine flags (cla: yes, team)

[36970](https://github.com/flutter/flutter/pull/36970) Clarify showDuration and waitDuration API docs and test behavior (cla: yes, d: api docs, f: material design, framework)

[36974](https://github.com/flutter/flutter/pull/36974) Multiline Selection Menu Position Bug (a: text input, cla: yes, f: material design, framework)

[36987](https://github.com/flutter/flutter/pull/36987) Flutter assemble for macos take 2! (cla: yes, tool, ⌘‬ platform-mac)

[36997](https://github.com/flutter/flutter/pull/36997) Added missing png's to demo splash screen project (was caused by global gitignore). (cla: yes, team)

[37026](https://github.com/flutter/flutter/pull/37026) Add support for the Kannada (kn) locale (a: internationalization, cla: yes, f: cupertino, f: material design, team)

[37027](https://github.com/flutter/flutter/pull/37027) Revert "Fix the first frame logic in tracing and driver" (a: tests, cla: yes, d: examples, framework, team, team: gallery, tool)

[37030](https://github.com/flutter/flutter/pull/37030) Mark backdrop_filter_perf test nonflaky (cla: yes, team)

[37033](https://github.com/flutter/flutter/pull/37033) fix debug paint crash when axis direction inverted (cla: yes, framework, severe: crash)

[37036](https://github.com/flutter/flutter/pull/37036) Build number (part after +) is documented as optional, use entire app version if not present (cla: yes, tool)

[37037](https://github.com/flutter/flutter/pull/37037) [flutter_tool] Update Fuchsia SDK (cla: yes, tool)

[37038](https://github.com/flutter/flutter/pull/37038) Update SnackBar to the latest Material specs. (cla: yes, f: material design, framework)

[37042](https://github.com/flutter/flutter/pull/37042) Fix selection menu not showing after clear (a: text input, cla: yes, framework)

[37043](https://github.com/flutter/flutter/pull/37043) Tests for Engine ensuring debug-mode apps are attached on iOS. (cla: yes, team, tool)

[37044](https://github.com/flutter/flutter/pull/37044) [flutter_tool] Make a couple file operations synchronous (cla: yes, tool)

[37048](https://github.com/flutter/flutter/pull/37048) use SizedBox instead of Container for building collapsed selection (a: text input, cla: yes, f: cupertino, framework)

[37049](https://github.com/flutter/flutter/pull/37049) Revert Optimize transformRect (cla: yes, framework, waiting for tree to go green)

[37055](https://github.com/flutter/flutter/pull/37055) Revert "Enable selection by default for password text field and expos… (cla: yes, f: cupertino, f: material design, framework)

[37158](https://github.com/flutter/flutter/pull/37158) Fix Textfields in Semantics Debugger (a: accessibility, cla: yes, framework, waiting for tree to go green)

[37183](https://github.com/flutter/flutter/pull/37183) reland Enable selection by default for password text field and expose… (a: text input, cla: yes, customer: fun (g3), f: cupertino, f: material design)

[37186](https://github.com/flutter/flutter/pull/37186) [flutter_tool] Usage refactor cleanup (cla: yes, tool)

[37187](https://github.com/flutter/flutter/pull/37187) use FlutterError in MultiChildRenderObjectWidget (cla: yes, framework)

[37192](https://github.com/flutter/flutter/pull/37192) Reland "Fix the first frame logic in tracing and driver (#35297)" (cla: yes, engine, framework)

[37194](https://github.com/flutter/flutter/pull/37194) [flutter_tool] More gracefully handle Android sdkmanager failure (cla: yes, tool)

[37196](https://github.com/flutter/flutter/pull/37196) [flutter_tool] Catch ProcessException from 'adb devices' (cla: yes, tool)

[37198](https://github.com/flutter/flutter/pull/37198) [flutter_tool] Re-try sending the first crash report (cla: yes, tool)

[37205](https://github.com/flutter/flutter/pull/37205) add cmx for complex_layout (cla: yes, team)

[37206](https://github.com/flutter/flutter/pull/37206) Test that modules built as AAR contain the right assets and artifacts (a: build, cla: yes, team, waiting for tree to go green, ▣ platform-android)

[37207](https://github.com/flutter/flutter/pull/37207) Update complex_layout.cmx (cla: yes, team)

[37208](https://github.com/flutter/flutter/pull/37208) Roll flutter/engine@09f8bffb9...67319c00b (cla: yes)

[37210](https://github.com/flutter/flutter/pull/37210) do not strip symbols when building profile (cla: yes, team, tool)

[37211](https://github.com/flutter/flutter/pull/37211) Don't enable scroll wheel when scrolling is off (a: desktop, cla: yes, customer: octopod, framework)

[37217](https://github.com/flutter/flutter/pull/37217) hide symbols from spotlight for App.framework (cla: yes, tool, waiting for tree to go green)

[37250](https://github.com/flutter/flutter/pull/37250) Removing Leftover Golden Test Skips (a: tests, cla: yes, framework)

[37254](https://github.com/flutter/flutter/pull/37254) Clamp Scaffold's max body height when extendBody is true (cla: yes, f: material design, framework, severe: crash)

[37259](https://github.com/flutter/flutter/pull/37259) [Material] Add support for hovered, pressed, focused, and selected text color on Chips. (cla: yes, f: material design, framework)

[37266](https://github.com/flutter/flutter/pull/37266) Change the value of kMaxUnsignedSMI for the Web (cla: yes, framework, severe: new feature, waiting for tree to go green, ☸ platform-web)

[37269](https://github.com/flutter/flutter/pull/37269) [Material] FAB refactor - remove unnecessary IconTheme  (cla: yes, f: material design, framework)

[37275](https://github.com/flutter/flutter/pull/37275) Optimize the transformRect and transformPoint methods in matrix_utils… (cla: yes, framework)

[37276](https://github.com/flutter/flutter/pull/37276) Make podhelper.rb a template to avoid passing in the module name (a: existing-apps, cla: yes, t: xcode, team, tool, ⌺‬ platform-ios)

[37295](https://github.com/flutter/flutter/pull/37295) Revert "reland Enable selection by default for password text field and expose…" (a: accessibility, cla: yes, f: cupertino, f: material design, framework, team)

[37314](https://github.com/flutter/flutter/pull/37314) Update dartdoc to 28.4 (cla: yes, team)

[37319](https://github.com/flutter/flutter/pull/37319) resizeToAvoidBottomInset Cupertino without NavBar (cla: yes, f: cupertino, framework)

[37322](https://github.com/flutter/flutter/pull/37322) Add comments to an Android Platform view gesture test case (a: platform-views, cla: yes, framework, severe: crash, waiting for tree to go green)

[37324](https://github.com/flutter/flutter/pull/37324) reland Enable selection by default for password text field and expose… (a: accessibility, cla: yes, f: cupertino, f: material design, framework, team)

[37328](https://github.com/flutter/flutter/pull/37328) Fix some tests now that the isMultiline flag is added to values (a: accessibility, cla: yes, framework, waiting for tree to go green, ☸ platform-web)

[37331](https://github.com/flutter/flutter/pull/37331) [flutter_tool] Add missing toString() (cla: yes, tool)

[37338](https://github.com/flutter/flutter/pull/37338) Update constructor APIs TooltipTheme, ToggleButtonsTheme, PopupMenuTheme (cla: yes, f: material design, framework, severe: API break)

[37341](https://github.com/flutter/flutter/pull/37341) hiding original hero after hero transition (a: animation, cla: yes, framework, severe: API break, severe: new feature)

[37342](https://github.com/flutter/flutter/pull/37342) Fix mouse region crash when using closures (a: desktop, cla: yes, framework, waiting for tree to go green)

[37344](https://github.com/flutter/flutter/pull/37344) Fix mouse region double render (a: desktop, cla: yes, framework, waiting for tree to go green)

[37345](https://github.com/flutter/flutter/pull/37345) [flutter_tool] Include the local timezone in analytics timestamp (cla: yes, tool)

[37351](https://github.com/flutter/flutter/pull/37351) fix errors caught by roll of macOS assemble (cla: yes, tool)

[37355](https://github.com/flutter/flutter/pull/37355) Added ThemeData.from() method to construct a Theme from a ColorScheme (cla: yes, f: material design, framework)

[37365](https://github.com/flutter/flutter/pull/37365) only build macOS kernel in debug mode (cla: yes, tool)

[37378](https://github.com/flutter/flutter/pull/37378) Disable xcode indexing in CI via COMPILER_INDEX_STORE_ENABLE=NO argument (cla: yes, tool, waiting for customer response)

[37403](https://github.com/flutter/flutter/pull/37403) add ontap to textformfield (cla: yes, f: material design, framework)

[37405](https://github.com/flutter/flutter/pull/37405) Add .android/Flutter/flutter.iml to module template. (cla: yes, tool)

[37407](https://github.com/flutter/flutter/pull/37407) Remove multi-arch check in iOS builds (cla: yes, tool)

[37413](https://github.com/flutter/flutter/pull/37413) Revert "Remove multi-arch check in iOS builds" (cla: yes, engine, tool)

[37417](https://github.com/flutter/flutter/pull/37417) Nosuchmethod window (a: tests, cla: yes, framework)

[37422](https://github.com/flutter/flutter/pull/37422) [flutter_tool] Additional flutter manifest yaml validation (cla: yes, tool)

[37425](https://github.com/flutter/flutter/pull/37425) Support for macOS release mode (1 of 3) (cla: yes, tool)

[37436](https://github.com/flutter/flutter/pull/37436) Hide text selection handle after entering text (cla: yes, f: material design, framework)

[37440](https://github.com/flutter/flutter/pull/37440) Print message when HttpException is thrown after running `flutter run` (cla: yes, tool, waiting for tree to go green)

[37442](https://github.com/flutter/flutter/pull/37442) Disable flaky test (a: tests, cla: yes, team)

[37445](https://github.com/flutter/flutter/pull/37445) Switch iOS gen_snapshot from multi-arch binary to multiple binaries (cla: yes, engine, tool)

[37449](https://github.com/flutter/flutter/pull/37449) If xcode_backend.sh script fails or substitute variables are missing, fail the host Xcode build (a: existing-apps, cla: yes, t: xcode, team, tool)

[37457](https://github.com/flutter/flutter/pull/37457) Find the app bundle when the flavor contains underscores (a: build, cla: yes, team, tool, waiting for tree to go green)

[37479](https://github.com/flutter/flutter/pull/37479) Remove bogus code in ContainerParentDataMixin.detach (cla: yes, framework, waiting for tree to go green)

[37489](https://github.com/flutter/flutter/pull/37489) Moved the default BinaryMessenger instance to ServicesBinding (a: tests, cla: yes, customer: espresso, d: examples, framework, severe: API break, t: flutter driver, team)

[37492](https://github.com/flutter/flutter/pull/37492) Drawer edge drag width improvements (cla: yes, f: material design, framework, severe: new feature)

[37497](https://github.com/flutter/flutter/pull/37497) Extract common PlatformView functionality: Gesture and PointerEvent (cla: yes, framework)

[37500](https://github.com/flutter/flutter/pull/37500) Avoid killing Flutter tool process (#37471) (cla: yes, tool)

[37503](https://github.com/flutter/flutter/pull/37503) Quickly fix start up tests (cla: yes, framework)

[37509](https://github.com/flutter/flutter/pull/37509) Use macOS ephemeral directory for Pod env script (cla: yes, tool)

[37512](https://github.com/flutter/flutter/pull/37512) Enable track widget creation on debug builds (cla: yes, tool)

[37514](https://github.com/flutter/flutter/pull/37514) [flutter_tool] Remove unintended analytics screen send (cla: yes, tool)

[37515](https://github.com/flutter/flutter/pull/37515) Upstream web support for IterableProperty<double> (cla: yes, framework, ☸ platform-web)

[37516](https://github.com/flutter/flutter/pull/37516) Kill stale TODO (a: tests, cla: yes, framework, team)

[37521](https://github.com/flutter/flutter/pull/37521) have xcodeSelectPath also catch ArgumentError (cla: yes, tool)

[37524](https://github.com/flutter/flutter/pull/37524) Ensure that tests remove pointers by using addTearDown (a: tests, cla: yes, framework, waiting for tree to go green)

[37556](https://github.com/flutter/flutter/pull/37556) [Material] Make RawChip.selected non-nullable.  (cla: yes, f: material design, framework)

[37595](https://github.com/flutter/flutter/pull/37595) Closes #37593 Add flutter_export_environment.sh to gitignore (cla: yes, tool, waiting for tree to go green)

[37624](https://github.com/flutter/flutter/pull/37624) Diagrams for API docs rank 10-20 in most views (cla: yes, d: api docs, d: examples, f: material design, framework, from: study, waiting for tree to go green)

[37631](https://github.com/flutter/flutter/pull/37631) [Material] Create demo for material banner (cla: yes, d: examples, f: material design, team, team: gallery)

[37634](https://github.com/flutter/flutter/pull/37634) [web][upstream] Update diagnostics to support web platform tests (cla: yes, f: material design, framework)

[37636](https://github.com/flutter/flutter/pull/37636) Add CheckboxListTile checkColor (cla: yes, f: material design, framework)

[37637](https://github.com/flutter/flutter/pull/37637) don't call Platform.operatingSystem in RenderView diagnostics (cla: yes, framework, ☸ platform-web)

[37638](https://github.com/flutter/flutter/pull/37638) [web][upstream] Fix debugPrintStack for web platform (cla: yes, framework, ☸ platform-web)

[37647](https://github.com/flutter/flutter/pull/37647) Change priority of gen_snapshot search paths (cla: yes, tool)

[37649](https://github.com/flutter/flutter/pull/37649) Revert "Integrate dwds into flutter tool for web support" (a: accessibility, cla: yes, d: examples, team, team: gallery)

[37650](https://github.com/flutter/flutter/pull/37650) Reland Integrate dwds into flutter tool for web support (a: accessibility, cla: yes, d: examples, team, team: gallery)

[37652](https://github.com/flutter/flutter/pull/37652) Change RenderObject.getTransformTo to include ancestor. (cla: yes, framework, severe: API break)

[37654](https://github.com/flutter/flutter/pull/37654) Add missing library to flutter tools BUILD.gn (cla: yes, tool)

[37658](https://github.com/flutter/flutter/pull/37658) fix windows path for dwds/web builds (cla: yes, tool)

[37661](https://github.com/flutter/flutter/pull/37661) flutter update-packages --force-upgrade  (a: accessibility, a: tests, cla: yes, d: examples, framework, team, team: gallery, tool)

[37664](https://github.com/flutter/flutter/pull/37664) Partial macOS assemble revert (cla: yes, tool)

[37703](https://github.com/flutter/flutter/pull/37703) PlatformViewLink, handling creation of the PlatformViewSurface and dispose PlatformViewController (a: platform-views, cla: yes, framework)

[37712](https://github.com/flutter/flutter/pull/37712) [web][upstream] Optimize InactiveElements deactivation (cla: yes, framework, ☸ platform-web)

[37714](https://github.com/flutter/flutter/pull/37714) remove unused script (cla: yes, team)

[37715](https://github.com/flutter/flutter/pull/37715) Fix markdown link format (cla: yes, f: material design, framework)

[37718](https://github.com/flutter/flutter/pull/37718) Adding physicalDepth to MediaQueryData & TestWindow (cla: yes, customer: fuchsia, framework, severe: customer critical, waiting for tree to go green)

[37724](https://github.com/flutter/flutter/pull/37724) iOS 13 scrollbar vibration (cla: yes, f: cupertino, framework)

[37730](https://github.com/flutter/flutter/pull/37730) Revert "remove unused script" (cla: yes, team)

[37731](https://github.com/flutter/flutter/pull/37731) Add metadata to indicate if the host app contains a Flutter module (cla: yes, team, tool)

[37733](https://github.com/flutter/flutter/pull/37733) Support macOS Catalina-style signing certificate names (cla: yes, tool)

[37735](https://github.com/flutter/flutter/pull/37735) Remove unused no-build flag from the flutter run command (cla: yes, tool)

[37738](https://github.com/flutter/flutter/pull/37738) Use relative paths when installing module pods (a: existing-apps, cla: yes, tool, ⌺‬ platform-ios)

[37740](https://github.com/flutter/flutter/pull/37740) Disable gem documentation generation on Cirrus (cla: yes, team)

[37743](https://github.com/flutter/flutter/pull/37743) Handle thrown maps and rejects from fe server (cla: yes, tool)

[37752](https://github.com/flutter/flutter/pull/37752) Remove dead flag `gradle-dir` in flutter config (cla: yes, tool)

[37760](https://github.com/flutter/flutter/pull/37760) Don't mark system_debug_ios as flaky. (cla: yes, team)

[37790](https://github.com/flutter/flutter/pull/37790) Doc: Image.memory only accepts compressed format (cla: yes, framework, waiting for tree to go green)

[37792](https://github.com/flutter/flutter/pull/37792) Disable the progress bar when downloading the Dart SDK via Invoke-WebRequest (cla: yes, tool)

[37793](https://github.com/flutter/flutter/pull/37793) Add equals and hashCode to Tween (a: animation, cla: yes, framework, waiting for tree to go green)

[37801](https://github.com/flutter/flutter/pull/37801) Fix TextField cursor color documentation (cla: yes, f: material design, framework)

[37806](https://github.com/flutter/flutter/pull/37806) Add COMPILER_INDEX_STORE_ENABLE=NO to macOS build and tests (a: tests, cla: yes, t: xcode, team, tool)

[37809](https://github.com/flutter/flutter/pull/37809) Add autofocus parameter to widgets which use Focus widget internally (cla: yes, f: cupertino, f: material design, framework)

[37811](https://github.com/flutter/flutter/pull/37811) Add flutter_export_environment.sh to the framework tree gitignore (cla: yes, team)

[37812](https://github.com/flutter/flutter/pull/37812) [web][upstream] Don't register exit/saveCompilationTrace for web platform since they are not available (cla: yes, framework, ☸ platform-web)

[37815](https://github.com/flutter/flutter/pull/37815) Restructure resident web runner usage to avoid SDK users that don't support dwds (cla: yes, tool)

[37816](https://github.com/flutter/flutter/pull/37816) update dependencies; add a Web smoke test (a: accessibility, a: tests, cla: yes, d: examples, framework, team, team: gallery)

[37825](https://github.com/flutter/flutter/pull/37825) Automatic focus highlight mode for FocusManager (CQ+1, cla: yes, f: material design, framework)

[37828](https://github.com/flutter/flutter/pull/37828) have android_semantics_testing use adb from ENV provided android sdk (a: accessibility, cla: yes, team)

[37856](https://github.com/flutter/flutter/pull/37856) Remove references to solo flag in flutter test (a: tests, cla: yes, d: api docs, framework)

[37863](https://github.com/flutter/flutter/pull/37863) Expose the timeline event names so they can be used in other systems that do tracing (cla: yes, tool)

[37870](https://github.com/flutter/flutter/pull/37870) remove Header flag from BottomNavigationBar items (cla: yes, f: material design, framework, waiting for tree to go green)

[37871](https://github.com/flutter/flutter/pull/37871) Catch failure to create directory in cache (cla: yes, tool)

[37872](https://github.com/flutter/flutter/pull/37872) Unmark devicelab tests as flaky that are no longer flaky (cla: yes, team)

[37877](https://github.com/flutter/flutter/pull/37877) Adds DefaultTextStyle ancestor to Tooltip Overlay (cla: yes, f: material design, framework)

[37880](https://github.com/flutter/flutter/pull/37880) reduce mac workload (cla: yes)

[37881](https://github.com/flutter/flutter/pull/37881) Remove no-longer-needed scripts (cla: yes, team, waiting for tree to go green)

[37882](https://github.com/flutter/flutter/pull/37882) Add dense property to AboutListTile (cla: yes, f: material design, framework)

[37891](https://github.com/flutter/flutter/pull/37891) Focus debug (a: desktop, cla: yes, framework)

[37895](https://github.com/flutter/flutter/pull/37895) Revert "Add equals and hashCode to Tween" (cla: yes, framework)

[37900](https://github.com/flutter/flutter/pull/37900) Listen to ExtensionEvent instead of TimelineEvent (cla: yes, framework, tool)

[37904](https://github.com/flutter/flutter/pull/37904) test tool scheduling (cla: yes, team)

[37906](https://github.com/flutter/flutter/pull/37906) Always install the ephemeral engine copy instead of fetching from CocoaPods specs (a: existing-apps, cla: yes, team, tool, ⌺‬ platform-ios)

[37938](https://github.com/flutter/flutter/pull/37938) Revert "Adding physicalDepth to MediaQueryData & TestWindow" (a: tests, cla: yes, framework, waiting for tree to go green)

[37940](https://github.com/flutter/flutter/pull/37940) skip docs shard for changes that don't include packages with docs (cla: yes, team, tool)

[37941](https://github.com/flutter/flutter/pull/37941) Skip widget tests on non framework change (cla: yes, team)

[37955](https://github.com/flutter/flutter/pull/37955) Update shader warm-up for recent Skia changes (cla: yes, framework, severe: performance, severe: regression)

[37958](https://github.com/flutter/flutter/pull/37958) Catch FormatException caused by bad simctl output (cla: yes, tool)

[37964](https://github.com/flutter/flutter/pull/37964) Update documentation for bottom sheets to accurately reflect usage (cla: yes, f: material design, framework)

[37966](https://github.com/flutter/flutter/pull/37966) Remove ephemeral directories during flutter clean (a: existing-apps, cla: yes, tool)

[37971](https://github.com/flutter/flutter/pull/37971) Update dependencies (a: accessibility, cla: yes, d: examples, team, team: gallery)

[37981](https://github.com/flutter/flutter/pull/37981) Give `_runFlutterTest` the ability to validate command output (cla: yes, team)

[37983](https://github.com/flutter/flutter/pull/37983) Revert "Moved the default BinaryMessenger instance to ServicesBinding… (a: accessibility, a: tests, cla: yes, f: cupertino, f: material design, framework, team)

[37984](https://github.com/flutter/flutter/pull/37984) Fix some typos in flutter/dev/bots/README.md (cla: yes, team)

[37994](https://github.com/flutter/flutter/pull/37994) Remove no-constant-update-2018, the underlying issue has been resolved. (cla: yes, tool)

[38101](https://github.com/flutter/flutter/pull/38101) Catch filesystem exception from flutter create (cla: yes, tool)

[38102](https://github.com/flutter/flutter/pull/38102) Fix type error hidden by implicit downcasts (cla: yes, tool)

[38296](https://github.com/flutter/flutter/pull/38296) use common emulator/device list (cla: yes, t: flutter driver, tool, waiting for tree to go green)

[38325](https://github.com/flutter/flutter/pull/38325) refactor `flutter upgrade` to be 2 part, with the second part re-entrant (cla: yes, tool)

[38326](https://github.com/flutter/flutter/pull/38326) Re-enabling post-submit gold tests on mac (a: tests, cla: yes, framework)

[38339](https://github.com/flutter/flutter/pull/38339) [flutter_tool] Flip create language defaults to swift and kotlin (cla: yes, tool)

[38342](https://github.com/flutter/flutter/pull/38342) remove bsdiff from  BUILD.gn (cla: yes, tool)

[38348](https://github.com/flutter/flutter/pull/38348) Analyzer fix that wasn't caught in the PR originally (cla: yes, d: examples, f: material design, team, team: gallery)

[38353](https://github.com/flutter/flutter/pull/38353) [flutter_tool] Observatory connection error handling cleanup (cla: yes, tool)

[38441](https://github.com/flutter/flutter/pull/38441) Fix getOffsetToReveal for growthDirection reversed and AxisDirection down or right (cla: yes, framework)

[38462](https://github.com/flutter/flutter/pull/38462) Revert "Roll engine ff49ca1c6e5b..7dfdfc6faeb6 (52 commits)" (cla: yes, engine)

[38463](https://github.com/flutter/flutter/pull/38463) Do not construct arguments to _focusDebug when running in non-debug modes (cla: yes, framework)

[38467](https://github.com/flutter/flutter/pull/38467) [Material] Add splashColor to FAB and FAB ThemeData (cla: yes, f: material design, framework)

[38472](https://github.com/flutter/flutter/pull/38472) [flutter_tool] Fix bug in manifest yaml validation (cla: yes, tool)

[38486](https://github.com/flutter/flutter/pull/38486) Catch errors thrown into the Zone by json_rpc (cla: yes, tool)

[38491](https://github.com/flutter/flutter/pull/38491) Update CONTRIBUTING.md (cla: yes, team)

[38494](https://github.com/flutter/flutter/pull/38494) Navigator change backup (a: tests, cla: yes, framework, ☸ platform-web)

[38495](https://github.com/flutter/flutter/pull/38495) Roll engine ff49ca1c6e5b..be4c8338a6ab (61 commits) (cla: yes, engine)

[38497](https://github.com/flutter/flutter/pull/38497) handle unexpected exit from frontend server (cla: yes, tool)

[38499](https://github.com/flutter/flutter/pull/38499) Update build web compilers and configure libraries (cla: yes, tool)

[38546](https://github.com/flutter/flutter/pull/38546) Re-land 'Adding physicalDepth to MediaQueryData & TestWindow' (a: tests, cla: yes, customer: fuchsia, framework, severe: customer critical)

[38558](https://github.com/flutter/flutter/pull/38558) Fix typos (and a few errors) in the API docs. (cla: yes, framework)

[38564](https://github.com/flutter/flutter/pull/38564) Updating code owners for golden file changes (a: tests, cla: yes, framework, team, will affect goldens)

[38567](https://github.com/flutter/flutter/pull/38567) Add smoke tests to test every commit on a Catalina host. (cla: yes, team)

[38575](https://github.com/flutter/flutter/pull/38575) fix rpc exception for real (cla: yes, tool)

[38579](https://github.com/flutter/flutter/pull/38579) Fix a smoke test. (cla: yes, team)

[38586](https://github.com/flutter/flutter/pull/38586) Don't reload if compilation has errors (cla: yes, tool)

[38587](https://github.com/flutter/flutter/pull/38587) Improve bitcode check (cla: yes, t: xcode, tool, ⌺‬ platform-ios)

[38593](https://github.com/flutter/flutter/pull/38593) Fix text scale factor for non-content components of Cupertino scaffolds (a: fidelity, cla: yes, f: cupertino, f: material design, framework)

[38603](https://github.com/flutter/flutter/pull/38603) Fix up iOS Add to App tests (a: existing-apps, a: tests, cla: yes, team, ⌺‬ platform-ios)

[38621](https://github.com/flutter/flutter/pull/38621) [Material] Create theme for Dividers to enable customization of thickness (cla: yes, f: material design, framework)

[38629](https://github.com/flutter/flutter/pull/38629) Handle case of a connected unpaired iOS device (cla: yes, tool)

[38635](https://github.com/flutter/flutter/pull/38635) Toggle buttons docs (cla: yes, d: api docs, f: material design, framework)

[38636](https://github.com/flutter/flutter/pull/38636) Adds the arrowColor option to UserAccountsDrawerHeader (#38608) (cla: yes, f: material design, framework)

[38637](https://github.com/flutter/flutter/pull/38637) [flutter_tool] Throw tool exit on malformed storage url override (cla: yes, tool)

[38639](https://github.com/flutter/flutter/pull/38639) PlatformViewLink. cached surface should be a Widget type (a: platform-views, cla: yes, framework)

[38645](https://github.com/flutter/flutter/pull/38645) Rename iOS arch for macOS release mode (macOS release mode 2 of 3) (cla: yes, tool)

[38651](https://github.com/flutter/flutter/pull/38651) Update the macOS Podfile template platform version (cla: yes, tool)

[38652](https://github.com/flutter/flutter/pull/38652) Kill dead code (cla: yes, team, tool)

[38658](https://github.com/flutter/flutter/pull/38658) Roll back engine to f8e7453f11067b5801a4484283592977d18be242 (cla: yes, engine)

[38662](https://github.com/flutter/flutter/pull/38662) Change from using `defaults` to `plutil` for Plist parsing (cla: yes, tool)

[38686](https://github.com/flutter/flutter/pull/38686) Rename patent file (cla: yes)

[38704](https://github.com/flutter/flutter/pull/38704) Adds canRequestFocus toggle to FocusNode (cla: yes, framework)

[38708](https://github.com/flutter/flutter/pull/38708) Fix Catalina hot reload test by specifying the platform is iOS. (cla: no, team)

[38710](https://github.com/flutter/flutter/pull/38710) PlatformViewLink: Rename CreatePlatformViewController to CreatePlatformViewCallback (a: platform-views, cla: yes, framework)

[38719](https://github.com/flutter/flutter/pull/38719) Fix stages of some iOS devicelab tests (cla: yes, team)



670 PRs were closed in flutter/flutter.
We omitted 154 PRs in this report because they were
automated PRs, such as autoroller commits.


# PRs closed in this release of flutter/engine

From Fri Jun 21 22:31:55 2019 -0400 to Sun Aug 18 12:22:00 2019 -0700


[9041](https://github.com/flutter/engine/pull/9041) TextStyle.height property as a multiple of font size instead of multiple of ascent+descent+leading. (affects: text input, cla: yes, prod: API break)

[9075](https://github.com/flutter/engine/pull/9075) IOS Platform view transform/clipping (cla: yes)

[9089](https://github.com/flutter/engine/pull/9089) Wire up custom event loop interop for the GLFW embedder. (cla: yes)

[9206](https://github.com/flutter/engine/pull/9206) Android Embedding Refactor PR31: Integrate platform views with the new embedding and the plugin shim. (cla: yes)

[9329](https://github.com/flutter/engine/pull/9329) Fixed memory leak by way of accidental retain on implicit self (cla: yes)

[9341](https://github.com/flutter/engine/pull/9341) some drive-by docs while I was reading the embedding classes (cla: yes)

[9360](https://github.com/flutter/engine/pull/9360) Simplify loading of app bundles on Android (cla: yes)

[9403](https://github.com/flutter/engine/pull/9403) Remove variants of ParagraphBuilder::AddText that are not used within the engine (cla: yes)

[9419](https://github.com/flutter/engine/pull/9419) Has a binary messenger (cla: yes, prod: API break)

[9423](https://github.com/flutter/engine/pull/9423) Don't hang to a platform view's input connection after it's disposed (cla: yes)

[9424](https://github.com/flutter/engine/pull/9424) Send timings of the first frame without batching (cla: yes)

[9428](https://github.com/flutter/engine/pull/9428) Update README.md for consistency with framework (cla: yes)

[9431](https://github.com/flutter/engine/pull/9431) Generate weak pointers only in the platform thread (cla: yes)

[9436](https://github.com/flutter/engine/pull/9436) Add the functionality to merge and unmerge MessageLoopTaskQueues (cla: yes)

[9439](https://github.com/flutter/engine/pull/9439) Eliminate unused import in FlutterView (cla: yes)

[9446](https://github.com/flutter/engine/pull/9446) Revert "Roll fuchsia/sdk/core/mac-amd64 from Cx51F... to e8sS_..." (cla: yes)

[9449](https://github.com/flutter/engine/pull/9449) Revert "Roll fuchsia/sdk/core/linux-amd64 from udf6w... to jQ8aw..." (cla: yes)

[9450](https://github.com/flutter/engine/pull/9450) Revert "Roll fuchsia/sdk/core/mac-amd64 from Cx51F... to w-3t4..." (cla: yes)

[9452](https://github.com/flutter/engine/pull/9452) Convert RRect.scaleRadii to public method (affects: framework, cla: yes)

[9456](https://github.com/flutter/engine/pull/9456) Made sure that the run_tests script returns the right error code. (cla: yes)

[9458](https://github.com/flutter/engine/pull/9458) Test cleanup geometry_test.dart  (affects: tests, cla: yes)

[9459](https://github.com/flutter/engine/pull/9459) Remove unused/unimplemented shell constructor (cla: yes)

[9460](https://github.com/flutter/engine/pull/9460) Fixed logLevel filter bug so that filter now works as expected. (cla: yes)

[9461](https://github.com/flutter/engine/pull/9461) Adds API for retaining intermediate engine layers (cla: yes)

[9462](https://github.com/flutter/engine/pull/9462) Reland Update harfbuzz to 2.5.2 (cla: yes)

[9463](https://github.com/flutter/engine/pull/9463) Removed unused imports in new embedding. (cla: yes)

[9464](https://github.com/flutter/engine/pull/9464) Added shebangs to ios unit test scripts. (cla: yes)

[9466](https://github.com/flutter/engine/pull/9466) Re-enable the Wuffs GIF decoder (cla: yes)

[9467](https://github.com/flutter/engine/pull/9467) ios-unit-tests: Forgot a usage of a variable in our script. (cla: yes)

[9468](https://github.com/flutter/engine/pull/9468) Manually draw remainder curve for wavy decorations (cla: yes)

[9469](https://github.com/flutter/engine/pull/9469) ios-unit-tests: Fixed ocmock system header search paths. (cla: yes)

[9471](https://github.com/flutter/engine/pull/9471) ios-unit-tests: Started using rsync instead of cp -R to copy frameworks. (cla: yes)

[9476](https://github.com/flutter/engine/pull/9476) fix NPE when a touch event is sent to an unknown Android platform view (cla: yes)

[9478](https://github.com/flutter/engine/pull/9478) iOS PlatformView clip path (cla: yes)

[9480](https://github.com/flutter/engine/pull/9480) Revert "IOS Platform view transform/clipping (#9075)" (cla: yes)

[9482](https://github.com/flutter/engine/pull/9482) Re-enable embedder_unittests. (cla: yes)

[9483](https://github.com/flutter/engine/pull/9483) Reland "IOS Platform view transform/clipping (#9075)" and fix the breakage. (cla: yes)

[9485](https://github.com/flutter/engine/pull/9485) Add --observatory-host switch (cla: yes)

[9486](https://github.com/flutter/engine/pull/9486) Rework image & texture management to use concurrent message queues. (cla: yes)

[9489](https://github.com/flutter/engine/pull/9489) Handle ambiguous directionality of final trailing whitespace in mixed bidi text (cla: yes)

[9490](https://github.com/flutter/engine/pull/9490) fix a bug where the platform view's transform is not reset before set frame (cla: yes)

[9491](https://github.com/flutter/engine/pull/9491) Purge caches on low memory on iOS (cla: yes)

[9493](https://github.com/flutter/engine/pull/9493) Run benchmarks on try jobs. (cla: yes)

[9495](https://github.com/flutter/engine/pull/9495) fix build breakage on PlatformViews.mm (cla: yes)

[9501](https://github.com/flutter/engine/pull/9501) [android] External textures must be rescaled to fill the canvas (cla: yes)

[9503](https://github.com/flutter/engine/pull/9503) Improve caching limits for Skia (cla: yes)

[9506](https://github.com/flutter/engine/pull/9506) Synchronize main thread and gpu thread for first render frame (cla: yes)

[9507](https://github.com/flutter/engine/pull/9507) Revert Skia version to d8f79a27b06b5bce7a27f89ce2d43d39f8c058dc (cla: yes)

[9508](https://github.com/flutter/engine/pull/9508) Support image filter on paint (cla: yes)

[9509](https://github.com/flutter/engine/pull/9509) Roll Fuchsia SDK to latest (cla: yes)

[9518](https://github.com/flutter/engine/pull/9518) Bump dart_resource_rev to f8e37558a1c4f54550aa463b88a6a831e3e33cd6 (cla: yes)

[9525](https://github.com/flutter/engine/pull/9525) Android Embedding Refactor PR36: Add splash screen support. (cla: yes)

[9532](https://github.com/flutter/engine/pull/9532) fix FlutterOverlayView doesn't remove from superview in some cases (cla: yes)

[9546](https://github.com/flutter/engine/pull/9546) [all] add fuchsia.{net.NameLookup,posix.socket.Provider} (cla: yes)

[9556](https://github.com/flutter/engine/pull/9556) Minimal integration with the Skia text shaper module (cla: yes)

[9559](https://github.com/flutter/engine/pull/9559)  Roll src/third_party/dart b37aa3b036...1eb113ba27 (cla: yes)

[9561](https://github.com/flutter/engine/pull/9561) libtxt: fix reference counting of SkFontStyleSets held by font asset providers (cla: yes)

[9562](https://github.com/flutter/engine/pull/9562) Switched preprocessor logic for exporting symbols for testing. (cla: yes)

[9581](https://github.com/flutter/engine/pull/9581) Revert "Avoid a full screen overlay within virtual displays" (cla: yes)

[9584](https://github.com/flutter/engine/pull/9584) Revert " Roll src/third_party/dart b37aa3b036...1eb113ba27" (cla: yes)

[9585](https://github.com/flutter/engine/pull/9585) Fix a race in the embedder accessibility unit test (cla: yes)

[9588](https://github.com/flutter/engine/pull/9588) Roll src/third_party/dart b37aa3b036...0abff7b2bb (cla: yes)

[9589](https://github.com/flutter/engine/pull/9589) Fixes a plugin overwrite bug in the plugin shim system. (cla: yes)

[9590](https://github.com/flutter/engine/pull/9590) Apply patches that have landed in topaz since we ported the runners to the engine repo (cla: yes)

[9591](https://github.com/flutter/engine/pull/9591) Document various classes in //flutter/shell/common. (cla: yes)

[9593](https://github.com/flutter/engine/pull/9593) [trace clients] Remove fuchsia.tracelink.Registry (cla: yes)

[9608](https://github.com/flutter/engine/pull/9608) Disable failing Mutators tests (cla: yes)

[9613](https://github.com/flutter/engine/pull/9613) Fix uninitialized variables and put tests in flutter namespace. (cla: yes)

[9632](https://github.com/flutter/engine/pull/9632) Added Doxyfile. (cla: yes)

[9633](https://github.com/flutter/engine/pull/9633) Cherry-pick fix for flutter/flutter#35291 (cla: yes)

[9634](https://github.com/flutter/engine/pull/9634) Roll Dart to 67ab3be10d35d994641da167cc806f20a7ffa679 (cla: yes)

[9636](https://github.com/flutter/engine/pull/9636) Added shebangs to ios unit test scripts. (#9464) (cla: yes)

[9637](https://github.com/flutter/engine/pull/9637) Revert "Roll Dart to 67ab3be10d35d994641da167cc806f20a7ffa679 (#9634)" (cla: yes)

[9638](https://github.com/flutter/engine/pull/9638) Reland: Roll Dart to 67ab3be10d35d994641da167cc806f20a7ffa679 (cla: yes)

[9640](https://github.com/flutter/engine/pull/9640) make EmbeddedViewParams a unique ptr (cla: yes)

[9641](https://github.com/flutter/engine/pull/9641) Let pushColorFilter accept all types of ColorFilters (cla: yes)

[9642](https://github.com/flutter/engine/pull/9642) Fix warning about settings unavailable GN arg build_glfw_shell (cla: yes)

[9649](https://github.com/flutter/engine/pull/9649) Roll buildroot to c5a493b25. (cla: yes)

[9651](https://github.com/flutter/engine/pull/9651) Move the mutators stack handling to preroll (cla: yes)

[9652](https://github.com/flutter/engine/pull/9652) Pipeline allows continuations that can produce to front (cla: yes)

[9653](https://github.com/flutter/engine/pull/9653) External view embedder can tell if embedded views have mutated (cla: yes)

[9654](https://github.com/flutter/engine/pull/9654) Begin separating macOS engine from view controller (cla: yes)

[9655](https://github.com/flutter/engine/pull/9655) Allow embedders to add callbacks for responses to platform messages from the framework. (cla: yes)

[9660](https://github.com/flutter/engine/pull/9660) ExternalViewEmbedder can CancelFrame after pre-roll (cla: yes)

[9661](https://github.com/flutter/engine/pull/9661) Raster now returns an enum rather than boolean (cla: yes)

[9663](https://github.com/flutter/engine/pull/9663) Mutators Stack refactoring (cla: yes)

[9667](https://github.com/flutter/engine/pull/9667) iOS platform view opacity (cla: yes)

[9668](https://github.com/flutter/engine/pull/9668) Refactor ColorFilter to have a native wrapper (cla: yes)

[9669](https://github.com/flutter/engine/pull/9669) Improve window documentation (cla: yes)

[9670](https://github.com/flutter/engine/pull/9670)  Roll src/third_party/dart 67ab3be10d...43891316ca (cla: yes)

[9672](https://github.com/flutter/engine/pull/9672) Add FLEDartProject for macOS embedding (cla: yes)

[9673](https://github.com/flutter/engine/pull/9673) Revert " Roll src/third_party/dart 67ab3be10d...43891316ca" (cla: yes)

[9675](https://github.com/flutter/engine/pull/9675)  Roll src/third_party/dart 67ab3be10d...b5aeaa6796 (cla: yes)

[9685](https://github.com/flutter/engine/pull/9685) fix Picture.toImage return type check and api conform test. (cla: yes)

[9698](https://github.com/flutter/engine/pull/9698) Ensure that platform messages without response handles can be dispatched. (cla: yes)

[9707](https://github.com/flutter/engine/pull/9707) Revert "Revert "Use track-widget-creation transformer included in the… (cla: yes)

[9708](https://github.com/flutter/engine/pull/9708) Roll src/third_party/dart b5aeaa6796...966038ef58 (cla: yes)

[9711](https://github.com/flutter/engine/pull/9711) Revert "Roll src/third_party/dart b5aeaa6796...966038ef58" (cla: yes)

[9713](https://github.com/flutter/engine/pull/9713) Explain why OpacityLayer has an offset field (cla: yes)

[9716](https://github.com/flutter/engine/pull/9716) Roll src/third_party/dart b5aeaa6796..06c3d7ad3a (44 commits) (cla: yes)

[9717](https://github.com/flutter/engine/pull/9717) Fixed logLevel filter bug so that filter now works as expected. (#9460) (cla: yes)

[9721](https://github.com/flutter/engine/pull/9721) Add comments to differentiate two cache paths (cla: yes)

[9722](https://github.com/flutter/engine/pull/9722) Forwards iOS dark mode trait to the Flutter framework (#34441). (cla: yes)

[9723](https://github.com/flutter/engine/pull/9723) Roll src/third_party/dart 06c3d7ad3a..7acecda2cc (12 commits) (cla: yes)

[9724](https://github.com/flutter/engine/pull/9724) Revert "Roll src/third_party/dart 06c3d7ad3a..7acecda2cc (12 commits)")

[9725](https://github.com/flutter/engine/pull/9725) Make the license script compatible with recently changed Dart I/O stream APIs (cla: yes)

[9727](https://github.com/flutter/engine/pull/9727) Add hooks for InputConnection lock and unlocking (cla: yes)

[9728](https://github.com/flutter/engine/pull/9728) Roll src/third_party/dart 06c3d7ad3a...09fc76bc51 (cla: yes)

[9730](https://github.com/flutter/engine/pull/9730) Fix Fuchsia build. (cla: yes)

[9734](https://github.com/flutter/engine/pull/9734) Fix backspace crash on Chinese devices (cla: yes)

[9736](https://github.com/flutter/engine/pull/9736) Build Fuchsia as part of CI presumit (cla: yes)

[9737](https://github.com/flutter/engine/pull/9737) Use libc++ variant of string view and remove the FML variant. (cla: yes)

[9740](https://github.com/flutter/engine/pull/9740) Revert "Improve caching limits for Skia" (cla: yes)

[9741](https://github.com/flutter/engine/pull/9741) Make FLEViewController's view an internal detail (cla: yes)

[9745](https://github.com/flutter/engine/pull/9745) Fix windows test by not attempting to open a directory as a file. (cla: yes)

[9746](https://github.com/flutter/engine/pull/9746) Make all shell unit tests use the OpenGL rasterizer. (cla: yes)

[9747](https://github.com/flutter/engine/pull/9747) Remove get engine (cla: yes)

[9750](https://github.com/flutter/engine/pull/9750) FLEViewController/Engine API changes (cla: yes)

[9758](https://github.com/flutter/engine/pull/9758) Include SkParagraph headers only when the enable-skshaper flag is on (cla: yes)

[9762](https://github.com/flutter/engine/pull/9762) Fall back to a fully qualified path to libapp.so if the library can not be loaded by name (cla: yes)

[9767](https://github.com/flutter/engine/pull/9767) Un-deprecated FlutterViewController's binaryMessenger. (cla: yes)

[9769](https://github.com/flutter/engine/pull/9769) Document //flutter/shell/common/engine. (cla: yes)

[9772](https://github.com/flutter/engine/pull/9772) fix objcdoc generation (cla: yes)

[9781](https://github.com/flutter/engine/pull/9781) SendPlatformMessage allow null message value (cla: yes)

[9787](https://github.com/flutter/engine/pull/9787) Roll src/third_party/dart 09fc76bc51..24725a8559 (43 commits) (cla: yes)

[9789](https://github.com/flutter/engine/pull/9789) fix ColorFilter.matrix constness (cla: yes)

[9791](https://github.com/flutter/engine/pull/9791) Roll Wuffs and buildroot (cla: yes)

[9792](https://github.com/flutter/engine/pull/9792) Update flutter_web to latest (cla: yes)

[9793](https://github.com/flutter/engine/pull/9793) Fix typo in PlaceholderAlignment docs (cla: yes)

[9797](https://github.com/flutter/engine/pull/9797) Remove breaking asserts (cla: yes)

[9799](https://github.com/flutter/engine/pull/9799) Update buildroot to c4df4a7b to pull in MSVC 2017 Update 9 on Windows. (cla: yes)

[9808](https://github.com/flutter/engine/pull/9808) Document FontFeature class (cla: yes)

[9809](https://github.com/flutter/engine/pull/9809) Document //flutter/shell/common/rasterizer (cla: yes)

[9812](https://github.com/flutter/engine/pull/9812) Roll src/third_party/dart 24725a8559..28f95fcd24 (32 commits) (cla: yes)

[9813](https://github.com/flutter/engine/pull/9813) Made Picture::toImage happen on the IO thread with no need for an onscreen surface. (cla: yes)

[9815](https://github.com/flutter/engine/pull/9815) Made the persistent cache's directory a const pointer. (cla: yes)

[9816](https://github.com/flutter/engine/pull/9816) Only release the image data in the unit-test once Skia has accepted ownership of it. (cla: yes)

[9817](https://github.com/flutter/engine/pull/9817) Revert "Roll src/third_party/dart 24725a8559..28f95fcd24 (32 commits)" (cla: yes)

[9818](https://github.com/flutter/engine/pull/9818) Convert run_tests to python, allow running on Mac/Windows and allow filters for tests. (cla: yes)

[9819](https://github.com/flutter/engine/pull/9819) Allow for dynamic thread merging on IOS for embedded view mutations (cla: yes)

[9823](https://github.com/flutter/engine/pull/9823) Roll buildroot to support bitcode enabled builds for iOS (cla: yes)

[9825](https://github.com/flutter/engine/pull/9825) In a single frame codec, release the encoded image buffer after giving it to the decoder (cla: yes)

[9826](https://github.com/flutter/engine/pull/9826) Roll src/third_party/dart 24725a8559..cbaf890f88 (33 commits) (cla: yes)

[9828](https://github.com/flutter/engine/pull/9828) Make the virtual display's window translucent (cla: yes)

[9829](https://github.com/flutter/engine/pull/9829) Revert "Roll src/third_party/dart 24725a8559..cbaf890f88 (33 commits)" (cla: yes)

[9835](https://github.com/flutter/engine/pull/9835) [Windows] Alternative Windows shell platform implementation (affects: desktop, cla: yes, waiting for tree to go green)

[9847](https://github.com/flutter/engine/pull/9847)  Started adding the engine hash to frameworks' Info.plist. (cla: yes)

[9849](https://github.com/flutter/engine/pull/9849) Preserve the alpha for VD content by setting a transparent background. (cla: yes)

[9850](https://github.com/flutter/engine/pull/9850) Add multi-line flag to semantics (cla: yes)

[9851](https://github.com/flutter/engine/pull/9851) Add a macro for prefixing embedder.h symbols (cla: yes)

[9852](https://github.com/flutter/engine/pull/9852) Selectively enable tests that work on Windows and file issues for ones that don't. (cla: yes)

[9855](https://github.com/flutter/engine/pull/9855) Fix missing assignment to _allowHeadlessExecution (cla: yes)

[9856](https://github.com/flutter/engine/pull/9856) Disable Fuchsia Debug & Release presubmits and only attempt the Profile unopt variant. (cla: yes)

[9857](https://github.com/flutter/engine/pull/9857) Fix fuchsia license detection (cla: yes)

[9859](https://github.com/flutter/engine/pull/9859) Fix justify for RTL paragraphs. (cla: yes, waiting for tree to go green)

[9866](https://github.com/flutter/engine/pull/9866) Update buildroot to pick up Fuchsia artifact roller. (cla: yes)

[9867](https://github.com/flutter/engine/pull/9867) Fixed error in generated xml Info.plist. (cla: yes)

[9873](https://github.com/flutter/engine/pull/9873) Add clang version to Info.plist (cla: yes)

[9875](https://github.com/flutter/engine/pull/9875) Simplify buildtools (cla: yes)

[9883](https://github.com/flutter/engine/pull/9883) Roll src/third_party/dart 24725a8559..2b3336b51e (108 commits) (cla: yes)

[9890](https://github.com/flutter/engine/pull/9890) Log dlopen errors only in debug mode (cla: yes)

[9893](https://github.com/flutter/engine/pull/9893) Removed logic from FlutterAppDelegate into FlutterPluginAppLifeCycleDelegate (cla: yes)

[9894](https://github.com/flutter/engine/pull/9894) Add the isMultiline semantics flag to values (cla: yes)

[9895](https://github.com/flutter/engine/pull/9895) Android Embedding PR37: Separated FlutterActivity and FlutterFragment via FlutterActivityAndFragmentDelegate (cla: yes)

[9896](https://github.com/flutter/engine/pull/9896) Capture stderr for ninja command (cla: yes)

[9898](https://github.com/flutter/engine/pull/9898) v1.7.8 hotfixes (cla: yes)

[9901](https://github.com/flutter/engine/pull/9901) Handle decompressed images in InstantiateImageCodec (cla: yes)

[9903](https://github.com/flutter/engine/pull/9903) Revert to using fml::StringView instead of std::string_view (cla: yes)

[9905](https://github.com/flutter/engine/pull/9905) Respect EXIF information while decompressing images. (cla: yes)

[9906](https://github.com/flutter/engine/pull/9906) Update libcxx & libcxxabi to HEAD in prep for compiler upgrade. (cla: yes)

[9909](https://github.com/flutter/engine/pull/9909) Roll src/third_party/dart 6bf1f8e280..63120303a7 (4 commits) (cla: yes)

[9919](https://github.com/flutter/engine/pull/9919) Removed unused method. (cla: yes)

[9920](https://github.com/flutter/engine/pull/9920) Fix caching of Locale.toString (cla: yes)

[9922](https://github.com/flutter/engine/pull/9922) Split out lifecycle protocol (cla: yes)

[9923](https://github.com/flutter/engine/pull/9923) Fix failure of the onReportTimings window hook test (cla: yes)

[9924](https://github.com/flutter/engine/pull/9924) Don't try to use unset assets_dir setting (cla: yes)

[9925](https://github.com/flutter/engine/pull/9925) Fix the geometry test to reflect that OffsetBase comparison operators are a partial ordering (cla: yes)

[9927](https://github.com/flutter/engine/pull/9927) Update Buildroot Version (cla: yes)

[9929](https://github.com/flutter/engine/pull/9929) Update the exception thrown for invalid data in the codec test (cla: yes)

[9931](https://github.com/flutter/engine/pull/9931) Fix reentrancy handling in SingleFrameCodec (cla: yes)

[9932](https://github.com/flutter/engine/pull/9932) Exit flutter_tester with an error code on an unhandled exception (cla: yes)

[9933](https://github.com/flutter/engine/pull/9933) Build fuchsia artifacts from the engine (cla: yes)

[9934](https://github.com/flutter/engine/pull/9934) Updates to the engine test runner script (cla: yes)

[9935](https://github.com/flutter/engine/pull/9935) Fix backspace crash on Chinese devices (#9734) (cla: yes)

[9936](https://github.com/flutter/engine/pull/9936) Move development.key from buildroot (cla: yes)

[9937](https://github.com/flutter/engine/pull/9937) [platform view] do not make clipping view and interceptor view clipToBounds (cla: yes)

[9938](https://github.com/flutter/engine/pull/9938) Removed PlatformViewsController if-statements from TextInputPlugin (#34286). (cla: yes)

[9939](https://github.com/flutter/engine/pull/9939) Added hasRenderedFirstFrame() to old FlutterView for Espresso (#36211). (cla: yes)

[9948](https://github.com/flutter/engine/pull/9948) [glfw] Enables replies on binary messenger in glfw embedder (cla: yes)

[9951](https://github.com/flutter/engine/pull/9951) Roll src/third_party/dart 63120303a7...a089199b93 (cla: yes)

[9952](https://github.com/flutter/engine/pull/9952) ios: Fixed the callback for the first frame so that it isn't predicated on having a splash screen. (cla: yes)

[9953](https://github.com/flutter/engine/pull/9953) [macos] Add reply to binary messenger (cla: yes)

[9954](https://github.com/flutter/engine/pull/9954) Add working Robolectric tests (cla: yes)

[9958](https://github.com/flutter/engine/pull/9958) Clean up cirrus.yml file a little (cla: yes)

[9959](https://github.com/flutter/engine/pull/9959) Update Dart engine tests to check for assertion failures only when running in debug mode (cla: yes)

[9961](https://github.com/flutter/engine/pull/9961) Fix return type of assert function in gradient_test (cla: yes)

[9977](https://github.com/flutter/engine/pull/9977) Fix flutter/flutter #34791 (cla: yes, platform-android)

[9987](https://github.com/flutter/engine/pull/9987) Update GN to git_revision:152c5144ceed9592c20f0c8fd55769646077569b (cla: yes)

[9998](https://github.com/flutter/engine/pull/9998) [luci] Reference the right fuchsia CIPD and upload only once (cla: yes)

[9999](https://github.com/flutter/engine/pull/9999) Add support for Android's visible password input type (affects: text input, cla: yes)

[10001](https://github.com/flutter/engine/pull/10001) Roll src/third_party/dart a089199b93..fedd74669a (8 commits) (cla: yes)

[10003](https://github.com/flutter/engine/pull/10003) Declare a copy of the enable_bitcode flag within the Flutter build scripts for use in Fuchsia builds (cla: yes)

[10004](https://github.com/flutter/engine/pull/10004) [fuchsia] Use GatherArtifacts to create the requisite dir structure (cla: yes)

[10007](https://github.com/flutter/engine/pull/10007) Embedding testing app (cla: yes)

[10009](https://github.com/flutter/engine/pull/10009) [macos] Revert check on FlutterCodecs and refactor message function] (cla: yes)

[10010](https://github.com/flutter/engine/pull/10010) Use simarm_x64 when targeting arm (cla: yes)

[10012](https://github.com/flutter/engine/pull/10012) Undelete used method (cla: yes)

[10021](https://github.com/flutter/engine/pull/10021) Added a DartExecutor API for querying # of pending channel callbacks (cla: yes)

[10056](https://github.com/flutter/engine/pull/10056) Update .cirrus.yml (cla: yes)

[10063](https://github.com/flutter/engine/pull/10063) Track clusters and return cluster boundaries in getGlyphPositionForCoordinates (emoji fix) (affects: text input, cla: yes, crash)

[10064](https://github.com/flutter/engine/pull/10064) Disable DartLifecycleTest::ShuttingDownTheVMShutsDownAllIsolates in runtime_unittests. (cla: yes)

[10065](https://github.com/flutter/engine/pull/10065) test scenario_app on CI (cla: yes)

[10066](https://github.com/flutter/engine/pull/10066) Roll src/third_party/dart fedd74669a..9c148623c5 (70 commits) (cla: yes)

[10068](https://github.com/flutter/engine/pull/10068) Fixed memory leak with engine registrars. (cla: yes)

[10069](https://github.com/flutter/engine/pull/10069) Enable consts from environment in DDK for flutter_web (cla: yes)

[10073](https://github.com/flutter/engine/pull/10073) Basic structure for flutter_jit_runner far (cla: yes)

[10074](https://github.com/flutter/engine/pull/10074) Change ParagraphBuilder to replace the parent style's font families with the child style's font families (cla: yes)

[10075](https://github.com/flutter/engine/pull/10075) Change flutter runner target for LUCI (cla: yes)

[10078](https://github.com/flutter/engine/pull/10078) One more luci fix (cla: yes)

[10081](https://github.com/flutter/engine/pull/10081) [fuchsia] Add support for libs in packages (cla: yes)

[10082](https://github.com/flutter/engine/pull/10082) [fuchsia] Add sysroot and clang libs to package (cla: yes)

[10085](https://github.com/flutter/engine/pull/10085) [fuchsia] Use the new far package model (cla: yes)

[10087](https://github.com/flutter/engine/pull/10087) [fuchsia] copy over the cmx file (cla: yes)

[10096](https://github.com/flutter/engine/pull/10096) Roll src/third_party/skia 3ae30cc2e6e0..1cd1ed8976c4 (1 commits) (autoroller: dryrun, cla: yes)

[10097](https://github.com/flutter/engine/pull/10097) Roll src/third_party/skia 1cd1ed8976c4..f564f1515bde (1 commits) (autoroller: dryrun, cla: yes)

[10098](https://github.com/flutter/engine/pull/10098) Roll src/third_party/dart 9c148623c5..82f657d7cb (25 commits) (cla: yes)

[10100](https://github.com/flutter/engine/pull/10100) Roll src/third_party/skia f564f1515bde..fdf4bfe6d389 (1 commits) (autoroller: dryrun, cla: yes)

[10101](https://github.com/flutter/engine/pull/10101) Roll src/third_party/skia fdf4bfe6d389..b3956dc6ba6a (1 commits) (autoroller: dryrun, cla: yes)

[10102](https://github.com/flutter/engine/pull/10102) [fuchsia] Use manifest file to better replicate the existing build (cla: yes)

[10109](https://github.com/flutter/engine/pull/10109) Cache font family lookups that fail to obtain a font collection (cla: yes)

[10114](https://github.com/flutter/engine/pull/10114) Roll src/third_party/dart 82f657d7cb..0c97c31b6e (7 commits) (cla: yes)

[10122](https://github.com/flutter/engine/pull/10122) [fuchsia] Use the patched sdk to generate the flutter jit runner far (cla: yes)

[10127](https://github.com/flutter/engine/pull/10127) Track detailed LibTxt metrics (cla: yes)

[10128](https://github.com/flutter/engine/pull/10128) Started linking the test targets against Flutter. (cla: yes)

[10139](https://github.com/flutter/engine/pull/10139) Roll src/third_party/dart 0c97c31b6e..a2aec5eb06 (22 commits) (cla: yes)

[10140](https://github.com/flutter/engine/pull/10140) Revert "[fuchsia] Use the patched sdk to generate the flutter jit run… (cla: yes)

[10141](https://github.com/flutter/engine/pull/10141) Revert "[macos] Revert check on FlutterCodecs and refactor message fu… (cla: yes)

[10143](https://github.com/flutter/engine/pull/10143) Disable windows tests (cla: yes)

[10144](https://github.com/flutter/engine/pull/10144) [fuchsia] Push CMX to fars and add product mode support (cla: yes)

[10145](https://github.com/flutter/engine/pull/10145) Added integration test that tests that the first frame callback is called (cla: yes)

[10146](https://github.com/flutter/engine/pull/10146) Revert "Disable windows tests" (cla: yes)

[10150](https://github.com/flutter/engine/pull/10150) [fuchsia] Remove extraneous ShapeNodes (cla: yes)

[10151](https://github.com/flutter/engine/pull/10151) [fucshia] fix name to reflect the cmx file (cla: yes)

[10153](https://github.com/flutter/engine/pull/10153) Add gclient_gn_args_file to DEPS (cla: yes)

[10155](https://github.com/flutter/engine/pull/10155) src/third_party/dart a2aec5eb06...86dba81dec (cla: yes)

[10160](https://github.com/flutter/engine/pull/10160) Roll src/third_party/dart 86dba81dec..0ca1582afd (2 commits) (cla: yes)

[10171](https://github.com/flutter/engine/pull/10171) [fuchsia] Add support for aot mode in flutter runner (cla: yes)

[10172](https://github.com/flutter/engine/pull/10172) [dart_runner] Rename dart to dart runner (cla: yes)

[10176](https://github.com/flutter/engine/pull/10176) Add suggested Java changes from flutter roll (cla: yes, platform-android)

[10178](https://github.com/flutter/engine/pull/10178) Removed unnecessary call to find the App.framework. (cla: yes)

[10179](https://github.com/flutter/engine/pull/10179) [dart_runner] dart jit runner and dart jit product runner (cla: yes)

[10183](https://github.com/flutter/engine/pull/10183) [fuchsia] Uncomment publish to CIPD (cla: yes)

[10185](https://github.com/flutter/engine/pull/10185) Add better CIPD docs. (cla: yes)

[10186](https://github.com/flutter/engine/pull/10186) Ensure debug-mode apps are always attached on iOS. (cla: yes)

[10188](https://github.com/flutter/engine/pull/10188) [fuchsia] Artifacts now contain gen_snapshot and gen_snapshot_product (cla: yes)

[10189](https://github.com/flutter/engine/pull/10189)  [macos] Reland function refactor (cla: yes)

[10195](https://github.com/flutter/engine/pull/10195) Allow embedder controlled composition of Flutter layers. (cla: yes)

[10226](https://github.com/flutter/engine/pull/10226) Roll src/third_party/dart 0ca1582afd..1e43d65d4a (50 commits) (cla: yes)

[10235](https://github.com/flutter/engine/pull/10235) Deprecate FlutterView#enableTransparentBackground (cla: yes)

[10240](https://github.com/flutter/engine/pull/10240) [fuchsia] Update buildroot to support arm64 (cla: yes)

[10242](https://github.com/flutter/engine/pull/10242) Remove Dead Scenic Clipping Code Path. (cla: yes)

[10246](https://github.com/flutter/engine/pull/10246) [fuchsia] Start building dart_patched_sdk (cla: yes)

[10250](https://github.com/flutter/engine/pull/10250) Android Embedding Refactor 38: Removed AssetManager from DartEntrypoint. (cla: yes)

[10260](https://github.com/flutter/engine/pull/10260) [fuchsia] Add arm64 builds for flutter and dart runner (cla: yes)

[10261](https://github.com/flutter/engine/pull/10261) [fuchsia] Bundle architecture specific gen_snapshots (cla: yes)

[10265](https://github.com/flutter/engine/pull/10265) [dart-roll] Roll dart sdk to 80c4954d4d1d2a257005793d83b601f3ff2997a2 (cla: yes)

[10268](https://github.com/flutter/engine/pull/10268) [fuchsia] Make cirrus build fuchsia artifacts (cla: yes)

[10273](https://github.com/flutter/engine/pull/10273) Remove one last final call to AddPart() (cla: yes)

[10282](https://github.com/flutter/engine/pull/10282) Export FFI from sky_engine. (cla: yes)

[10293](https://github.com/flutter/engine/pull/10293) Add fuchsia.stamp for roller (cla: yes)

[10294](https://github.com/flutter/engine/pull/10294) Roll src/third_party/dart 80c4954d4d..bd049f5b53 (37 commits) (cla: yes)

[10295](https://github.com/flutter/engine/pull/10295) Fix memory overrun in minikin patch (cla: yes, crash)

[10296](https://github.com/flutter/engine/pull/10296) fix CI (cla: yes)

[10297](https://github.com/flutter/engine/pull/10297) Ensure that the SingleFrameCodec stays alive until the ImageDecoder invokes its callback (cla: yes)

[10298](https://github.com/flutter/engine/pull/10298) Fix red build again (cla: yes)

[10303](https://github.com/flutter/engine/pull/10303) Make tree green for real this time, I promise. (cla: yes)

[10309](https://github.com/flutter/engine/pull/10309) [fuchsia] Kernel compiler is now ready (cla: yes)

[10381](https://github.com/flutter/engine/pull/10381) Fix empty composing range on iOS (cla: yes)

[10386](https://github.com/flutter/engine/pull/10386) Don't use DBC for hot-reload on iOS. (cla: yes)

[10403](https://github.com/flutter/engine/pull/10403) [fuchsia] Add kernel compiler target (cla: yes)

[10413](https://github.com/flutter/engine/pull/10413) Pass Android Q insets.systemGestureInsets to Window (cla: yes, platform-android)

[10414](https://github.com/flutter/engine/pull/10414) expose max depth on Window (cla: yes)

[10419](https://github.com/flutter/engine/pull/10419) Make kernel compiler use host toolchain (cla: yes)

[10423](https://github.com/flutter/engine/pull/10423) Fix mac gen_snapshot uploader (cla: yes)

[10424](https://github.com/flutter/engine/pull/10424) Fix deprecation warnings in the Android embedding (cla: yes)

[10430](https://github.com/flutter/engine/pull/10430) Add copy_gen_snapshots.py tool (cla: yes)

[10434](https://github.com/flutter/engine/pull/10434) Reland Skia Caching improvements (cla: yes)

[10437](https://github.com/flutter/engine/pull/10437) Roll src/third_party/dart bd049f5b53...622ec5099f (cla: yes)

[10440](https://github.com/flutter/engine/pull/10440) Revert "Remove one last final call to AddPart()" (cla: yes)

[10475](https://github.com/flutter/engine/pull/10475) Roll src/third_party/dart 622ec5099f...9bb446aae1 (14 commits) (cla: yes)

[10477](https://github.com/flutter/engine/pull/10477) Add #else, #endif condition comments (cla: yes)

[10478](https://github.com/flutter/engine/pull/10478) Migrate Fuchsia runners to SDK tracing API (cla: yes)

[10479](https://github.com/flutter/engine/pull/10479) Delete unused create_macos_gen_snapshot.py script (cla: yes)

[10481](https://github.com/flutter/engine/pull/10481) Android embedding refactor pr40 add static engine cache (cla: yes)

[10484](https://github.com/flutter/engine/pull/10484) Roll src/third_party/dart 9bb446aae1...4bebfebdbc (7 commits). (cla: yes)

[10485](https://github.com/flutter/engine/pull/10485) Remove semi-redundant try-jobs. (cla: yes)

[10629](https://github.com/flutter/engine/pull/10629) Fix engine platformviewscontroller leak (cla: yes)

[10633](https://github.com/flutter/engine/pull/10633) skip flaky tests (cla: yes)

[10634](https://github.com/flutter/engine/pull/10634) Use Fuchsia trace macros when targeting Fuchsia SDK (cla: yes)

[10635](https://github.com/flutter/engine/pull/10635) [fuchsia] CloneChannelFromFD fix for system.cc (cla: yes)

[10636](https://github.com/flutter/engine/pull/10636) Fix threading and re-enable resource cache shell unit-tests. (cla: yes)

[10637](https://github.com/flutter/engine/pull/10637) Document the thread test fixture. (cla: yes)

[10642](https://github.com/flutter/engine/pull/10642) Roll src/third_party/dart 4bebfebdbc..8cd01287b4 (30 commits) (cla: yes)

[10644](https://github.com/flutter/engine/pull/10644) [flutter_runner] Port: Add connectToService, wrapping fdio_ns_connect. (cla: yes)

[10645](https://github.com/flutter/engine/pull/10645) Don't use DBC for hot-reload on iOS.  (cla: yes)

[10652](https://github.com/flutter/engine/pull/10652) Allow embedders to control Dart VM lifecycle on engine shutdown. (cla: yes)

[10656](https://github.com/flutter/engine/pull/10656) fix iOS keyboard crash : -[__NSCFString substringWithRange:], range o… (cla: yes)

[10662](https://github.com/flutter/engine/pull/10662) bump local podspec's ios deployment target version from 7.0 to 8.0 (cla: yes)

[10663](https://github.com/flutter/engine/pull/10663) Roll src/third_party/dart 8cd01287b4..574c4a51c6 (35 commits) (cla: yes)

[10667](https://github.com/flutter/engine/pull/10667) Roll buildroot for ANGLE support (cla: yes)

[10671](https://github.com/flutter/engine/pull/10671) Roll src/third_party/dart 574c4a51c6..c262cbd414 (11 commits) (cla: yes)

[10674](https://github.com/flutter/engine/pull/10674) When setting up AOT snapshots from symbol references, make buffer sizes optional. (cla: yes)

[10675](https://github.com/flutter/engine/pull/10675) Improvements to the flutter GDB script (cla: yes)

[10679](https://github.com/flutter/engine/pull/10679) Roll buildroot to pick up EGL library name fix (cla: yes)

[10681](https://github.com/flutter/engine/pull/10681) Roll buildroot back to an earlier version (cla: yes)

[10682](https://github.com/flutter/engine/pull/10682) Roll src/third_party/dart c262cbd414..8740bb5c68 (18 commits) (cla: yes)

[10687](https://github.com/flutter/engine/pull/10687) Roll src/third_party/dart 8740bb5c68..f3139f57b4 (7 commits) (cla: yes)

[10692](https://github.com/flutter/engine/pull/10692) Rolls engine to Android SDK 29 and its corresponding tools (cla: yes)

[10693](https://github.com/flutter/engine/pull/10693) Roll src/third_party/dart f3139f57b4..f29f41f1a5 (3 commits) (cla: yes)

[10694](https://github.com/flutter/engine/pull/10694) Roll buildroot (cla: yes)

[10699](https://github.com/flutter/engine/pull/10699) Roll swiftshader (cla: yes)

[10700](https://github.com/flutter/engine/pull/10700) [fuchsia] Migrate from custom FuchsiaFontManager to SkFontMgr_fuchsia (cla: yes)

[10703](https://github.com/flutter/engine/pull/10703) Test perf overlay gold on Linux (cla: yes)

[10705](https://github.com/flutter/engine/pull/10705) Revert "Remove semi-redundant try-jobs. (#10485)" (cla: yes)

[10717](https://github.com/flutter/engine/pull/10717) Specify which android variant for tests (cla: yes, waiting for tree to go green)

[10719](https://github.com/flutter/engine/pull/10719) Include Maven dependency in files.json (cla: yes)

[10771](https://github.com/flutter/engine/pull/10771) Don't use gradle daemon for building (cla: yes, waiting for tree to go green)

[10773](https://github.com/flutter/engine/pull/10773) Remove use of the deprecated AccessibilityNodeInfo boundsInParent API (cla: yes)

[10774](https://github.com/flutter/engine/pull/10774) Manual roll of Fuchsia clang/linux-amd64 toolchain (cla: yes)

[10776](https://github.com/flutter/engine/pull/10776) rename stub_ui to web_ui (cla: yes)

[10777](https://github.com/flutter/engine/pull/10777) Manually roll Skia to pull in iOS armv7 build failure fix. (cla: yes)

[10778](https://github.com/flutter/engine/pull/10778) Build JARs containing the Android embedding sources and the engine native library (cla: yes)

[10780](https://github.com/flutter/engine/pull/10780) [flutter_runner] Improve frame scheduling (cla: yes)

[10781](https://github.com/flutter/engine/pull/10781) [flutter] Create the compositor context on the GPU task runner. (cla: yes)

[10782](https://github.com/flutter/engine/pull/10782) Update license script to handle ANGLE (cla: yes)

[10783](https://github.com/flutter/engine/pull/10783) Make firebase test more LUCI friendly (cla: yes)

[10784](https://github.com/flutter/engine/pull/10784) Roll buildroot for ANGLE support (cla: yes)

[10786](https://github.com/flutter/engine/pull/10786) Remove 3 semi-redundant try-jobs (cla: yes)

[10787](https://github.com/flutter/engine/pull/10787) Change call to |AddPart| to |AddChild| (cla: yes)

[10788](https://github.com/flutter/engine/pull/10788) Wire up a concurrent message loop backed SkExecutor for Skia. (cla: yes)

[10789](https://github.com/flutter/engine/pull/10789) Revert "Forwards iOS dark mode trait to the Flutter framework.". (cla: yes)

[10791](https://github.com/flutter/engine/pull/10791) Re-lands platform brightness support on iOS (cla: yes)

[10797](https://github.com/flutter/engine/pull/10797) Rename artifacts so they match the Maven convention (cla: yes)

[10799](https://github.com/flutter/engine/pull/10799) Add a test for creating images from bytes. (cla: yes)

[10802](https://github.com/flutter/engine/pull/10802) Roll src/third_party/dart f29f41f1a5..3d9a356f6e (65 commits) (cla: yes)

[10805](https://github.com/flutter/engine/pull/10805) Roll src/third_party/dart 3d9a356f6e..78ce916d82 (7 commits) (cla: yes)

[10808](https://github.com/flutter/engine/pull/10808) Remove flutter_kernel_sdk dart script (cla: yes)

[10809](https://github.com/flutter/engine/pull/10809) [dart:zircon] Porting Cache re-usable handle wait objects (cla: yes)

[10810](https://github.com/flutter/engine/pull/10810) Roll Dart SDK 78ce916d82..15a3bf82cb (cla: yes)

[10811](https://github.com/flutter/engine/pull/10811) Revert "Remove flutter_kernel_sdk dart script" (cla: yes)

[10815](https://github.com/flutter/engine/pull/10815) Return an empty mapping for an empty file asset (cla: yes)

[10816](https://github.com/flutter/engine/pull/10816) Add firstFrameDidRender to FlutterViewController (cla: yes)

[10817](https://github.com/flutter/engine/pull/10817) Roll src/third_party/dart 15a3bf82cb..ffefa124a7 (11 commits) (cla: yes)

[10820](https://github.com/flutter/engine/pull/10820) iOS JIT support and enhancements for scenarios app (cla: yes)

[10821](https://github.com/flutter/engine/pull/10821) Roll src/third_party/dart ffefa124a7..e29d6d0ecb (4 commits) (cla: yes)

[10823](https://github.com/flutter/engine/pull/10823) Expose isolateId for engine (cla: yes)

[10905](https://github.com/flutter/engine/pull/10905) Roll src/third_party/dart e29d6d0ecb..261fd6266b (2 commits) (cla: yes)

[10925](https://github.com/flutter/engine/pull/10925) Roll src/third_party/dart 261fd6266b..9adf3c119e (2 commits) (cla: yes)

[10934](https://github.com/flutter/engine/pull/10934) Roll src/third_party/dart 9adf3c119e..32b70ce2a5 (3 commits) (cla: yes)

[10941](https://github.com/flutter/engine/pull/10941) Report test failures in run_tests.py (cla: yes)

[10946](https://github.com/flutter/engine/pull/10946) Roll src/third_party/dart 32b70ce2a5..896c053803 (1 commits) (cla: yes)

[10949](https://github.com/flutter/engine/pull/10949) Fix iOS references to PostPrerollResult (cla: yes)

[10952](https://github.com/flutter/engine/pull/10952) Change SemanticsNode#children lists to be non-null (cla: yes)

[10955](https://github.com/flutter/engine/pull/10955) Fix format (cla: yes)

[10956](https://github.com/flutter/engine/pull/10956) Increase the license block scan from 5k to 6k (cla: yes)

[10962](https://github.com/flutter/engine/pull/10962) Roll src/third_party/dart 896c053803..b31df28d72 (10 commits) (cla: yes)

[10966](https://github.com/flutter/engine/pull/10966) Roll src/third_party/dart b31df28d72..baebba06af (5 commits) (cla: yes)

[10968](https://github.com/flutter/engine/pull/10968) include zx::clock from new location to fix Fuchsia autoroll. (cla: yes)

[10973](https://github.com/flutter/engine/pull/10973) Roll src/third_party/dart baebba06af..06509e333d (7 commits) (cla: yes)

[10975](https://github.com/flutter/engine/pull/10975) Roll src/third_party/dart 06509e333d..9aea1f3489 (8 commits) (cla: yes)

[10977](https://github.com/flutter/engine/pull/10977) Roll src/third_party/dart 9aea1f3489..b9217efc77 (7 commits) (cla: yes)

[10981](https://github.com/flutter/engine/pull/10981) Roll src/third_party/dart b9217efc77..20407e28db (6 commits) (cla: yes)

[10982](https://github.com/flutter/engine/pull/10982) Revert "Track detailed LibTxt metrics" (cla: yes)

[10983](https://github.com/flutter/engine/pull/10983) Roll src/third_party/dart 20407e28db..45f892df68 (2 commits) (cla: yes)

[10987](https://github.com/flutter/engine/pull/10987) Roll src/third_party/dart 45f892df68..88c43bbcc4 (7 commits) (cla: yes)

[10990](https://github.com/flutter/engine/pull/10990) Roll src/third_party/dart 88c43bbcc4..b173229baa (14 commits) (cla: yes)

[10993](https://github.com/flutter/engine/pull/10993) Roll src/third_party/dart b173229baa..76c99bcd01 (5 commits) (cla: yes)

[10997](https://github.com/flutter/engine/pull/10997) Roll src/third_party/dart 76c99bcd01..c4727fddf4 (10 commits) (cla: yes)

[10999](https://github.com/flutter/engine/pull/10999) Add script for running ios Tests on simulator (cla: yes)

[11001](https://github.com/flutter/engine/pull/11001) Avoid dynamic lookups of the engine library's symbols on Android (cla: yes)

[11002](https://github.com/flutter/engine/pull/11002) Remove a tracing macro with a dangling pointer (cla: yes)

[11003](https://github.com/flutter/engine/pull/11003) Roll src/third_party/dart c4727fddf4..e35e8833ee (1 commits) (cla: yes)

[11004](https://github.com/flutter/engine/pull/11004) Trace RasterCacheResult::Draw (cla: yes)

[11005](https://github.com/flutter/engine/pull/11005) Drop firebase test from Cirrus (cla: yes)

[11006](https://github.com/flutter/engine/pull/11006) On iOS report the preferred frames per second to tools via service protocol. (cla: yes)

[11007](https://github.com/flutter/engine/pull/11007) Update README.md (cla: yes)

[11009](https://github.com/flutter/engine/pull/11009) Revert "Update README.md" (cla: yes)

[11010](https://github.com/flutter/engine/pull/11010) Rename macOS FLE* classes to Flutter* (affects: desktop, cla: yes, platform-macos, waiting for tree to go green)

[11011](https://github.com/flutter/engine/pull/11011) Initialize the engine in the running state to match the animator's default state (cla: yes)

[11012](https://github.com/flutter/engine/pull/11012) Remove the ParagraphImpl class from the text API (cla: yes)

[11013](https://github.com/flutter/engine/pull/11013) Remove ability to override mac_sdk_path in flutter/tools/gn (cla: yes)

[11015](https://github.com/flutter/engine/pull/11015) Remove the output directory prefix from the Android engine JAR filename (cla: yes)

[11016](https://github.com/flutter/engine/pull/11016) Fix gn breakage on Fuchsia macOS host builds (cla: yes)

[11017](https://github.com/flutter/engine/pull/11017) Roll src/third_party/dart e35e8833ee..e35e8833ee (0 commits) (cla: yes)

[11019](https://github.com/flutter/engine/pull/11019) Fix gn breakage on non-Fuchsia macOS host builds (cla: yes)

[11023](https://github.com/flutter/engine/pull/11023) Roll src/third_party/dart e35e8833ee..cae08c6813 (28 commits) (cla: yes)

[11024](https://github.com/flutter/engine/pull/11024) Add _glfw versions of the GLFW desktop libraries (cla: yes)

[11026](https://github.com/flutter/engine/pull/11026) Roll src/third_party/dart cae08c6813..9552646dc4 (3 commits) (cla: yes)

[11027](https://github.com/flutter/engine/pull/11027) Fix first frame logic (cla: yes)

[11029](https://github.com/flutter/engine/pull/11029) Disable a deprecation warning for use of a TaskDescription constructor for older platforms (cla: yes)

[11030](https://github.com/flutter/engine/pull/11030) Roll src/third_party/dart 9552646dc4..cd16fba718 (5 commits) (cla: yes)

[11033](https://github.com/flutter/engine/pull/11033) remove OS version (cla: yes)

[11036](https://github.com/flutter/engine/pull/11036) [fuchsia] Add required trace so files for fuchsia fars (cla: yes)

[11037](https://github.com/flutter/engine/pull/11037) Roll buildroot to pick up recent macOS changes (cla: yes)

[11038](https://github.com/flutter/engine/pull/11038) Make JIT work on iPhone armv7 (cla: yes)

[11039](https://github.com/flutter/engine/pull/11039) Roll src/third_party/dart cd16fba718..306f8e04bb (10 commits) (cla: yes)

[11040](https://github.com/flutter/engine/pull/11040) Hide verbose dart snapshot during run_test.py (cla: yes)

[11043](https://github.com/flutter/engine/pull/11043) Roll Dart back to e35e8833 (cla: yes)

[11044](https://github.com/flutter/engine/pull/11044) Roll src/third_party/dart 306f8e04bb..fecc4c8f2d (4 commits) (cla: yes)

[11046](https://github.com/flutter/engine/pull/11046) Add ccls config files to .gitignore (cla: yes)

[11048](https://github.com/flutter/engine/pull/11048) Roll src/third_party/dart e35e8833ee..2023f09b56 (67 commits) (cla: yes)

[11052](https://github.com/flutter/engine/pull/11052) Remove unused dstColorSpace argument to MakeCrossContextFromPixmap (cla: yes)

[11055](https://github.com/flutter/engine/pull/11055) Roll src/third_party/dart 2023f09b56..a3b579d5c3 (8 commits) (cla: yes)

[11056](https://github.com/flutter/engine/pull/11056) Sort the Skia typefaces in a font style set into a consistent order (cla: yes)

[11060](https://github.com/flutter/engine/pull/11060) Roll src/third_party/dart a3b579d5c3..2a3b844b41 (5 commits) (cla: yes)

[11061](https://github.com/flutter/engine/pull/11061) Roll buildroot to 5a33d6ab to pickup changes to toolchain version tracking. (cla: yes)

[11066](https://github.com/flutter/engine/pull/11066) Roll src/third_party/dart 2a3b844b41..8ab978b6d4 (7 commits) (cla: yes)

[11067](https://github.com/flutter/engine/pull/11067) Minor update to the Robolectric test harness (cla: yes)

[11068](https://github.com/flutter/engine/pull/11068) More updates to the Robolectric test harness (cla: yes)

[11071](https://github.com/flutter/engine/pull/11071) Roll src/third_party/dart 8ab978b6d4..beee442625 (17 commits) (cla: yes)

[11072](https://github.com/flutter/engine/pull/11072) Roll src/third_party/dart beee442625..79e6c74337 (8 commits) (cla: yes)

[11075](https://github.com/flutter/engine/pull/11075) [dynamic_thread_merging] Resubmit only on the frame where the merge (cla: yes)



858 PRs were closed in flutter/engine.
We omitted 436 PRs in this report because they were
automated PRs, such as autoroller commits.
