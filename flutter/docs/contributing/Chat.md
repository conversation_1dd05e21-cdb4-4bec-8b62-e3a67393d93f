# Chatting on Discord

<img src="https://github.com/flutter/flutter/assets/551196/1381071b-e482-4e07-b096-60e9219d3ff7" width=300 align=right alt="">

The Flutter team uses a [Discord server](https://discord.com/channels/608014603317936148). [This is the invite link for Flutter's Discord server](https://discord.gg/ht477J5PyH). Please do not share either link directly, instead share links to this page.

The server is open to the public, though some channels are intended only for people who are actively contributing. **See the #welcome channel for instructions on posting to the server (you won't be able to see the channels until you acknowledge the rules there).**

We recommend you use the same display name on Discord and GitHub.

(Our Flutter Discord server is unrelated to the [r/FlutterDev Discord server](https://www.reddit.com/r/FlutterDev/), which is where the r/FlutterDev community shares their apps, discusses Flutter, and so on. When in doubt, remember: our server has Dash as an icon!)

## Existing channels

We have different channels for different purposes (this list is not exhaustive):

| Channel | Description | Participants |
| - | - | - |
| #welcome | Welcome message. | Only admins can post.<br>Anyone can read.
| #announcements | Flutter announcements (e.g. breaking changes). | Only team members can post (but not frequently).<br>Anyone can read.
| #server-support | Forum for asking for your role to be changed, for code of conduct violations to be raised, and for other administrative issues. | Anyone.
| #general | Chat about anything related to Flutter. | Anyone.
| #help | People asking for our help. | Anyone.
| #package-authors | For people who develop Flutter packages. | People writing packages for [pub.dev](https://pub.dev/).
| #dart | Questions about the Dart language. | Anyone.
| #tree-status | Announcements about whether the trees are open or closed. | Anyone, but discussion should happen elsewhere.
| #hackers | Chat about anything related to Flutter. | Flutter contributors.<br>Anyone can watch.
| #hackers-* | Chat specifically about foo, where foo is something to do with Flutter development, for example "engine", "framework", "desktop", "devtools", etc. | Flutter contributors.<br>Anyone can watch.
| #hackers-dart | Questions about the Dart language. | Flutter contributors.<br>Anyone can watch.
| #hackers-new | New people to the team and people who want to help them. | Flutter contributors.<br>Anyone can watch.
| #hackers-triage | For use while triaging bugs. | Flutter contributors.<br>Anyone can watch.
| #hidden-chat | Chat about anything related to Flutter. | Flutter contributors only.<br>Not public.
| general (voice) | Talk (audio) about anything related to Flutter. | Anyone.
| team (voice) | Talk (audio) that is only open to team members. | Flutter contributors.

The #hidden-chat and #team channels are not publicly-readable, you have to be a member of the "team member" role to see them.

## New channels

If you need a new channel, first just use #general or #hackers, or a thread in one of those channels; if the conversation lasts more than a day, ask for a channel in #server-support.

If you find your team's channel is hard to follow due to having too many topics discussed at once, ask for a new channel, or use threads.

If you would like a channel for subcommunities, e.g. #women or #china, that should be fine. (Subcommunities that are already overrepresented in the main channels, e.g. #english-speakers, are less likely to be good candidates for dedicated channels.)

For practical reasons, we do not use Discord for chat groups limited to specific customers. For example, if your company wants a private discussion channel with the Flutter team, we would not use Discord.

Each channel describes its topic in the channel description. Please read the channel description before posting in a channel, to make sure you're using the appropriate one.

## Policies

Our [code of conduct](https://github.com/flutter/flutter/blob/main/CODE_OF_CONDUCT.md) applies to the Discord server, as it does to any communications involving Flutter.

The #hackers-* channels are visible to anyone, but please only post in those channels if you are actively contributing. If you want help with your app, ask in #help instead. If you want to learn how to contribute, have a look at our [contributing guide](https://github.com/flutter/flutter/blob/main/CONTRIBUTING.md).

See the [contributor access](Contributor-access.md) wiki page for details on becoming a member of the "team" role.

Please don't direct-message people unless they are comfortable with it (ask publicly first).
You can disable direct messages on this server by changing your Privacy settings for the server, or on a global basis by changing your Privacy & Safety user settings.

## Discord features

### Threading

Discord has both actual threads (temporary new channels) and a threadingish feature called [Replies](https://support.discord.com/hc/en-us/articles/************-Replies-FAQ) that lets you tie messages to earlier messages and notify the original commenter.

### Status

You can [change your status](https://support.discord.com/hc/en-us/articles/************-Custom-Status) (online, away, custom messages) by clicking on your avatar in Discord.


# Design documents

This page used to discuss how to create design docs, but that content is now on its own page: [Design documents](Design-Documents.md).