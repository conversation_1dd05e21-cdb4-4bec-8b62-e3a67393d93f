The process to update the framework's Material fonts (currently, <PERSON><PERSON>) and [icons](https://github.com/flutter/flutter/blob/main/packages/flutter/lib/src/material/icons.dart) has been largely simplified and automated. See [go/effortless-flutter-font-updates](http://go/effortless-flutter-font-updates) (Google-only, sorry). The steps used prior to automation are available in this page's history.

If you notice an issue with fonts or icons (e.g. a missing icon), please file an [issue](https://github.com/flutter/flutter/issues/new/choose). Consider starting the issue title with `[<PERSON>ont<PERSON>]` or `[Icons]` to make it stand out.

_(This page is referenced by comments in the Flutter codebase.)_