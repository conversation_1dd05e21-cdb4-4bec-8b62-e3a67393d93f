name: My app has some non-speed performance issues
description: |
  You are writing an application but have discovered that it uses too much memory,
  too much energy (e.g., CPU/GPU usage is high), or its app size is too large.
labels: 'from: performance template'
body:
  - type: markdown
    attributes:
      value: |
        Thank you for using Flutter!

        If you are looking for support, please check out our documentation
        or consider asking a question on Stack Overflow:

          - https://flutter.dev/
          - https://api.flutter.dev/
          - https://stackoverflow.com/questions/tagged/flutter?sort=frequent
  - type: textarea
    attributes:
      label: Steps to reproduce
      description: Please tell us exactly how to reproduce the problem you are running into, and how you measured the performance.
      placeholder: |
        1. ...
        2. ...
        3. ...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Code sample
      description: |
        Please create a minimal reproducible sample that shows the problem and attach it below between the lines with the backticks.

        Try to reproduce the problem in a test app. Either run `flutter create janktest` and recreate the situation you are experiencing in that app, or clone your app and delete code until you have the jank reproducing with a single `.dart` file.

        If you need more than just a `.dart` file (for example, assets are needed to reproduce the issue, or plugins/packages are needed to reproduce the issue) then create a GitHub repository and upload code there.

        Without this we will unlikely be able to progress on the issue, and because of that we regretfully will have to close it.

        Note: Please do not upload screenshots of text. Instead, use code blocks or the above mentioned ways to upload your code sample.
      value: |
        <details open><summary>Code sample</summary>

        ```dart
        [Paste your code here]
        ```

        </details>
    validations:
      required: true
  - type: dropdown
    id: target_platforms
    attributes:
      label: What target platforms are you seeing this bug on?
      multiple: true
      options:
        - Android
        - iOS
        - Web
        - macOS
        - Linux
        - Windows
    validations:
      required: true
  - type: textarea
    attributes:
      label: OS/Browser name and version | Device information
      description: |
        Which target OS version is the test system running? For Web, please provide browser version.
        Please also include the device information (model, CPU architecture, etc).
    validations:
      required: true
  - type: dropdown
    id: device-kind
    attributes:
      label: Does the problem occur on emulator/simulator as well as on physical devices?
      options:
        - "Unknown"
        - "Yes"
        - "No"
    validations:
      required: true
  - type: textarea
    attributes:
      label: Logs
      description: |
        Include the full logs of the commands you are running between the lines with the backticks below. If you are running any `flutter` commands, please include the output of running them with `--verbose`; for example, the output of running `flutter --verbose create foo`.

        If the logs are too large to be uploaded to GitHub, you may upload them as a `txt` file or use online tools like https://pastebin.com to share it.

        Note: Please do not upload screenshots of text. Instead, use code blocks or the above mentioned ways to upload logs.
      value: |
        <details open><summary>Logs</summary>

        ```console
        [Paste your logs here]
        ```

        </details>
  - type: textarea
    attributes:
      label: Flutter Doctor output
      description: |
        Finally, paste the output of running `flutter doctor -v` here, with your device plugged in.
      value: |
        <details open><summary>Doctor output</summary>

        ```console
        [Paste your output here]
        ```

        </details>
    validations:
      required: true
