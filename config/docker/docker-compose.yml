﻿# The docker-compose configuration for the CloudToLocalLLM services

services:
# The tunnel service is commented out because there is no package.json in the build context.
# Add a valid Node.js project with package.json to enable this service.
# tunnel:
#   build:
#     context: .
#     dockerfile: Dockerfile.tunnel
#   networks:
#     - llm-network

  cloud:
    image: node:20
    working_dir: /app
    # depends_on:
    #   tunnel:
    #     condition: service_started
    networks:
      - cloudllm-network
    volumes:
      - ./cloud:/app
      - ./setup_cloud.sh:/app/setup_cloud.sh
    command: /bin/bash /app/setup_cloud.sh
    ports:
      - "3456:3456"
    user: "1000:1000" # Run as non-root user for security and compatibility

  webapp:
    container_name: cloudtolocalllm-webapp
    image: cloudtolocalllm-webapp:latest
    build:
      context: ../..
      dockerfile: config/docker/Dockerfile.web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ../../ssl:/etc/nginx/ssl:ro
      - ../../.env:/app/.env:ro
      - ../../static_portal_files:/usr/share/nginx/html/portal_files:ro
    networks:
      - cloudllm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  cloudllm-network:
    driver: bridge
