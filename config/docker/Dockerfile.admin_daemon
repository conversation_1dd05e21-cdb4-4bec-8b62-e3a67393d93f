# Dockerfile for the Admin Control Daemon
FROM ghcr.io/cirruslabs/flutter:latest AS build

WORKDIR /app

# Copy pubspec files
COPY admin_control_daemon/pubspec.yaml ./
COPY admin_control_daemon/pubspec.lock ./

# Get dependencies
RUN dart pub get

# Copy the rest of the code
COPY admin_control_daemon .

# Build the admin daemon
RUN dart compile exe bin/server.dart -o /app/daemon

# Final stage: Use the same Flutter image for runtime
FROM ghcr.io/cirruslabs/flutter:latest

RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Certbot and its Nginx plugin
RUN apt-get update && apt-get install -y --no-install-recommends \
    certbot \
    python3-certbot-nginx \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI (latest)
RUN curl -fsSL https://download.docker.com/linux/static/stable/x86_64/docker-24.0.9.tgz | tar xz -C /usr/local/bin --strip-components=1 docker/docker
# Install Docker Compose plugin (latest)
RUN mkdir -p /usr/local/lib/docker/cli-plugins && \
    curl -SL https://github.com/docker/compose/releases/download/v2.29.2/docker-compose-linux-x86_64 -o /usr/local/lib/docker/cli-plugins/docker-compose && \
    chmod +x /usr/local/lib/docker/cli-plugins/docker-compose

COPY --from=build /app/daemon /app/daemon

WORKDIR /opt/cloudtolocalllm

EXPOSE 9001

ENTRYPOINT ["/app/daemon"] 