# Stage 1: Build the Flutter web app
FROM ghcr.io/cirruslabs/flutter:latest AS build

WORKDIR /app

# Create a non-root user and group
RUN groupadd -r fluttergroup && useradd -r -g fluttergroup -m -s /sbin/nologin flutteruser

# Cache-busting echo
RUN echo "Build version: $(date +%s)"

# Change ownership of /app and /sdks/flutter to flutteruser before running flutter commands
# This is done as root, as USER flutteruser is set later
RUN chown -R flutteruser:fluttergroup /app && \
    chown -R flutteruser:fluttergroup /sdks/flutter

# Copy pubspec files first to leverage Docker cache for dependencies
# Ensure these are copied with correct ownership from the start
COPY --chown=flutteruser:fluttergroup pubspec.yaml ./
COPY --chown=flutteruser:fluttergroup pubspec.lock ./

# Switch to non-root user for flutter commands
USER flutteruser

# Add Flutter SDK directory as a safe directory for git
RUN git config --global --add safe.directory /sdks/flutter

# Get dependencies
RUN flutter pub get

# Copy the rest of the application source code (as flutteruser)
# Ensure correct ownership using --chown with COPY
COPY --chown=flutteruser:fluttergroup . .

# Build the Flutter web application
# The output will be in /app/build/web
RUN flutter build web --release

# Stage 2: Serve the app with Nginx
FROM nginx:alpine

# Install curl for healthcheck (keep only curl, remove openssl)
USER root
RUN apk add --no-cache curl openssl

# Copy the built web app from the build stage
COPY --from=build /app/build/web /usr/share/nginx/html

# Set permissions for nginx user (as root)
RUN chown -R nginx:nginx /usr/share/nginx/html && chmod -R 755 /usr/share/nginx/html

# Create and set permissions for Nginx cache and temp directories
RUN mkdir -p /var/cache/nginx/client_temp /var/cache/nginx/proxy_temp /var/cache/nginx/fastcgi_temp /var/cache/nginx/uwsgi_temp /var/cache/nginx/scgi_temp && \
    chown -R nginx:nginx /var/cache/nginx && \
    chmod -R 700 /var/cache/nginx

# Create and set permissions for Nginx PID directory
RUN mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx

# Generate self-signed certs for initial Nginx startup if they don't exist
# This allows Nginx to start and serve ACME challenges before real certs are available
RUN mkdir -p /etc/nginx/certs && \
    if [ ! -f /etc/nginx/certs/default.pem ] || [ ! -f /etc/nginx/certs/default.key ]; then \
      openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
      -keyout /etc/nginx/certs/default.key -out /etc/nginx/certs/default.pem \
      -subj "/CN=localhost"; \
    fi && \
    chown -R nginx:nginx /etc/nginx/certs && \
    chmod -R 600 /etc/nginx/certs

EXPOSE 80
# USER nginx is already specified in nginx:alpine base image and Nginx will run as nginx user by default.
# Explicitly setting it here for clarity.
CMD ["nginx", "-g", "daemon off;"] 