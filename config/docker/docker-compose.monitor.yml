# Docker Compose for CloudToLocalLLM monitoring service

services:
  netdata:
    image: netdata/netdata:latest
    container_name: cloudtolocalllm_monitor
    hostname: cloudtolocalllm
    restart: unless-stopped
    ports:
      - "19999:19999"
    cap_add:
      - SYS_PTRACE
      - SYS_ADMIN
    security_opt:
      - apparmor:unconfined
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - netdata-config:/etc/netdata
      - netdata-lib:/var/lib/netdata
      - netdata-cache:/var/cache/netdata
    environment:
      - NETDATA_CLAIM_TOKEN=${NETDATA_CLAIM_TOKEN:-}
      - NETDATA_CLAIM_URL=https://app.netdata.cloud
      - NETDATA_CLAIM_ROOMS=${NETDATA_CLAIM_ROOMS:-}
    user: "1000:1000" # Run as non-root user for security and compatibility
    networks:
      - webnet

volumes:
  netdata-config:
  netdata-lib:
  netdata-cache:

networks:
  webnet:
    external: true 