user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Add WebAssembly MIME type
    types {
        application/wasm wasm;
    }
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;
    sendfile on;
    keepalive_timeout 65;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # DNS resolution settings
    resolver 127.0.0.11 valid=30s;
    resolver_timeout 10s;

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name cloudtolocalllm.online www.cloudtolocalllm.online;

        # Handle Let's Encrypt ACME challenge
        location ^~ /.well-known/acme-challenge/ {
            root /var/www/certbot;
            default_type "text/plain";
            break;
        }

        # Redirect all other HTTP traffic to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    server {
        # listen 443 ssl http2; # Deprecated
        listen 443 ssl; # Updated
        http2 on; # Added
        server_name cloudtolocalllm.online www.cloudtolocalllm.online;

        # SSL configuration
        ssl_certificate /etc/letsencrypt/live/cloudtolocalllm.online/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/cloudtolocalllm.online/privkey.pem;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # root /usr/share/nginx/html; # Removed - content served by webapp
        # index index.html; # Removed - content served by webapp

        # Proxy pass requests to FusionAuth (running on port 9011)
        location /auth/ {
            # Using service name requires containers to be on the same network
            proxy_pass http://cloudtolocalllm-fusionauth-app:9011/;
            
            proxy_set_header Host $host; # Forward original host
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme; # Forward original scheme (https)
            proxy_set_header X-Forwarded-Host $host; # Sometimes needed
            proxy_set_header X-Forwarded-Port $server_port; # Sometimes needed

            # WebSocket support (often needed by modern web apps)
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            proxy_read_timeout 300s; # Increase timeout if needed
            proxy_connect_timeout 75s;
        }

        # Health check endpoint
        location = /health {
            return 200 'OK';
            add_header Content-Type text/plain;
        }

        # Handle SPA routing by proxying to the webapp service
        location / {
            proxy_pass http://cloudtolocalllm-webapp;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade"; # Required for WebSocket support
            # add_header Cache-Control "no-cache, no-store, must-revalidate"; # This should be handled by webapp
        }

        # Static files caching - Removed, handled by webapp
        # location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        #     expires 30d;
        #     add_header Cache-Control "public, no-transform";
        # }
    }
}
