# log_format main '$remote_addr - $remote_user [$time_local] "$request" '$status $body_bytes_sent "$http_referer" '$http_user_agent" "$http_x_forwarded_for"';
access_log /var/log/nginx/access.log main;

# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval'" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always; # Ensure HSTS is appropriate for your setup

# DNS resolution for proxy_pass with variables or service discovery
resolver 127.0.0.11 valid=30s; # Dock<PERSON>'s embedded DNS server
resolver_timeout 10s;

# Enable gzip compression
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Explicitly set .wasm MIME type only once
# (nginx default mime.types may already include this, but we ensure no duplicate)
types {
    application/wasm wasm;
}

# Main domain - static homepage
server {
    listen 80;
    server_name cloudtolocalllm.online www.cloudtolocalllm.online;
    root /usr/share/nginx/landing;
    location / {
        try_files $uri $uri/ =404;
    }
}

# App subdomain - Flutter app
server {
    listen 80;
    server_name app.cloudtolocalllm.online;
    root /usr/share/nginx/html;
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# Main domain - static homepage (SSL)
server {
    listen 443 ssl;
    server_name cloudtolocalllm.online www.cloudtolocalllm.online;
    root /usr/share/nginx/landing;
    ssl_certificate /etc/letsencrypt/live/cloudtolocalllm.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/cloudtolocalllm.online/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    location / {
        try_files $uri $uri/ =404;
    }
}

# App subdomain - Flutter app (SSL)
server {
    listen 443 ssl;
    server_name app.cloudtolocalllm.online;
    root /usr/share/nginx/html;
    ssl_certificate /etc/letsencrypt/live/cloudtolocalllm.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/cloudtolocalllm.online/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    location / {
        try_files $uri $uri/ /index.html;
    }
} 