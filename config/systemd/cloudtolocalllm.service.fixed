[Unit]
Description=CloudToLocalLLM Service
After=network.target docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/cloudtolocalllm/portal
User=root
Group=root

# Setup environment
ExecStartPre=/bin/bash -c 'mkdir -p /opt/cloudtolocalllm/logs'

# Check docker service
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet docker || systemctl start docker'

# Start all services
ExecStart=/usr/bin/docker-compose -f docker-compose.auth.yml -f docker-compose.web.yml up -d

# Stop all services
ExecStop=/usr/bin/docker-compose -f docker-compose.auth.yml -f docker-compose.web.yml down

# Restart policy
Restart=on-failure
RestartSec=10s

[Install]
WantedBy=multi-user.target 