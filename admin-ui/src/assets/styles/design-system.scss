/* CloudToLocalLLM Design System 
   This file defines core design variables used across the admin UI
*/

// Colors
:root {
  // Primary colors
  --color-primary: #a777e3;
  --color-secondary: #6e8efb;
  --color-accent: #00c58e;
  
  // Backgrounds
  --bg-main: #181a20;
  --bg-card: #23243a;
  --bg-light: #f5f5f5;
  
  // Text colors
  --text-color: #f1f1f1;
  --text-color-light: #b0b0b0;
  --text-color-dark: #2c3e50;
  
  // Status colors
  --color-success: #4caf50;
  --color-warning: #ffa726;
  --color-danger: #ff5252;
  --color-info: #2196f3;
  
  // Spacing & Layout
  --border-radius: 16px;
  --border-radius-sm: 4px;
  --box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.4);
  --box-shadow-sm: 0 2px 12px rgba(0, 0, 0, 0.2);
  
  // Gradients
  --gradient-header: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  --gradient-button: linear-gradient(90deg, #6e8efb 0%, #a777e3 100%);
}

// Typography
body {
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--bg-main);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

// Reusable Components
.card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 24px;
  margin-bottom: 24px;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: var(--border-radius-sm);
  border: none;
  font-weight: bold;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(1px);
  }
}

.btn-primary {
  background: var(--gradient-button);
  color: white;
}

.btn-secondary {
  background-color: var(--bg-card);
  color: var(--text-color);
  border: 1px solid var(--color-secondary);
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.header {
  background: var(--gradient-header);
  padding: 24px 0;
  color: white;
  
  .title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
  }
}

// Layout utilities
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Text utilities
.text-muted {
  color: var(--text-color-light);
}

.text-success {
  color: var(--color-success);
}

.text-danger {
  color: var(--color-danger);
}

.text-warning {
  color: var(--color-warning);
}

.text-info {
  color: var(--color-info);
}

.card-title {
  color: var(--color-primary);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.7rem;
  letter-spacing: 0.5px;
}

.card-desc {
  color: var(--text-muted);
  font-size: 1.05rem;
  margin-bottom: 1.2rem;
}

.button, .login-btn {
  font-size: 1.15rem;
  font-weight: 700;
  color: #fff;
  background: var(--gradient-button);
  border: none;
  border-radius: 32px;
  box-shadow: 0 2px 12px #6e8efb33;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  outline: none;
  text-decoration: none;
  letter-spacing: 0.5px;
  padding: 0.9rem 2.5rem;
}

.button:hover, .login-btn:hover, .button:focus, .login-btn:focus {
  background: linear-gradient(90deg, #a777e3 0%, #6e8efb 100%);
  box-shadow: 0 4px 24px #a777e366;
  transform: translateY(-2px) scale(1.03);
}

// Utility classes
.mt-4 { margin-top: 16px; }
.mb-4 { margin-bottom: 16px; }
.mx-auto { margin-left: auto; margin-right: auto; }
.p-4 { padding: 16px; } 