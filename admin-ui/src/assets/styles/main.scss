@import './design-system.scss';

// Base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// Layout
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.flex-grow {
  flex-grow: 1;
}

.grid {
  display: grid;
  grid-gap: 20px;
}

// Spacing
.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.p-4 {
  padding: 16px;
}

// Dashboard
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 16px;
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  
  h2 {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  p {
    color: var(--text-color-light);
  }
}

// App layout
.app-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  min-height: 100vh;
}

.sidebar {
  background-color: var(--color-secondary);
  color: white;
  padding: 20px;
  
  .logo {
    margin-bottom: 32px;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .nav-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      text-decoration: none;
    }
    
    &.active {
      background-color: var(--color-primary);
      color: white;
    }
    
    i {
      margin-right: 12px;
    }
  }
}

.main-content {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h1 {
    font-size: 1.8rem;
    font-weight: 500;
  }
}

// Responsive
@media (max-width: 768px) {
  .app-layout {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    display: none;
  }
} 