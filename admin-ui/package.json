{"name": "admin-ui", "version": "1.0.0", "description": "Admin UI for CloudToLocalLLM", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "ESLINT_USE_FLAT_CONFIG=false eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix"}, "dependencies": {"@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.9.0", "pinia": "^3.0.2", "primevue": "^4.3.4", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-standard": "^9.0.1", "eslint": "^9.26.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-vue": "^10.1.0", "terser": "^5.39.0", "vite": "^6.3.5"}}