# CloudToLocalLLM Windows App v1.1.0

## Overview
CloudToLocalLLM bridges the gap between cloud-based applications and local large language models. This Windows application provides a seamless interface to interact with locally installed LLMs while offering optional secure remote access.

## Key Features
- **Native Windows Experience** with system tray integration
- **Multiple LLM Support** for Ollama and LM Studio
- **Model Management** to download and use different LLM models
- **User-friendly Chat Interface** for natural interaction
- **Optional Cloud Connectivity** for secure remote access
- **Dark/Light Theme** options for personalized experience

## Requirements
- Windows 10/11
- Ollama or LM Studio installed locally

## Installation
1. Download and extract the ZIP file
2. Ensure Ollama or LM Studio is installed
3. Launch CloudToLocalLLM.exe
4. Follow the initial setup guide

Experience the power of local LLMs with the convenience of a modern, user-friendly interface. CloudToLocalLLM gives you complete control over your AI interactions while maintaining privacy and flexibility.
