:root {
  --color-primary: #a777e3;
  --color-secondary: #6e8efb;
  --color-accent: #00c58e;
  
  /* Backgrounds */
  --bg-main: #181a20;
  --bg-card: #23243a;
  --bg-light: #f5f5f5;
  
  /* Text colors */
  --text-color: #f1f1f1;
  --text-color-light: #b0b0b0;
  --text-color-dark: #2c3e50;
  
  /* Status colors */
  --color-success: #4caf50;
  --color-warning: #ffa726;
  --color-danger: #ff5252;
  --color-info: #2196f3;
  
  /* Spacing & Layout */
  --border-radius: 16px;
  --border-radius-sm: 4px;
  --box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.4);
  --box-shadow-sm: 0 2px 12px rgba(0, 0, 0, 0.2);
  
  /* Gradients */
  --gradient-header: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  --gradient-button: linear-gradient(90deg, #6e8efb 0%, #a777e3 100%);
}

html, body { height: 100%; margin: 0; padding: 0; }

body {
  background: var(--bg-main);
  color: var(--text-color);
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: var(--gradient-header);
  color: #fff;
  padding: 2.5rem 1rem 1.5rem 1rem;
  text-align: center;
  box-shadow: var(--box-shadow-sm);
  position: relative;
}

.logo {
  width: 70px;
  height: 70px;
  background: var(--color-secondary);
  border-radius: 50%;
  margin: 0 auto 1.2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--color-primary);
  font-weight: bold;
  box-shadow: var(--box-shadow-sm);
  user-select: none;
  border: 3px solid var(--color-primary);
}

h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 1px;
  color: #fff;
  text-shadow: 0 2px 8px rgba(110, 142, 251, 0.27);
}

h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 500;
  color: #e0d7ff;
}

.main {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem 1rem 3rem 1rem;
}

.card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2.2rem 2rem 2rem 2rem;
  max-width: 480px;
  width: 100%;
  margin: 2rem 0 0 0;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: box-shadow 0.2s;
  border: 1.5px solid rgba(110, 142, 251, 0.27);
}

.card:hover {
  box-shadow: 0 8px 32px 0 rgba(167, 119, 227, 0.4);
  border-color: var(--color-primary);
}

.card-title {
  color: var(--color-primary);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.7rem;
  letter-spacing: 0.5px;
}

.card-desc {
  color: var(--text-color-light);
  font-size: 1.05rem;
  margin-bottom: 1.2rem;
}

ul {
  margin: 0 0 0 1.2rem;
  padding: 0;
  color: var(--text-color);
  font-size: 1.05rem;
}

li {
  margin-bottom: 0.4rem;
}

.footer {
  margin-top: 3rem;
  padding: 1rem 0;
  text-align: center;
}

.version-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* Banner for under construction */
.banner {
  display: none; /* Hidden per design system requirements */
  background-color: var(--color-warning);
  color: #000;
  text-align: center;
  padding: 10px;
  font-weight: bold;
}

@media (max-width: 600px) {
  .header { font-size: 1.1rem; padding: 1.5rem 0.5rem 1rem 0.5rem; }
  .main { padding: 1rem 0.2rem 2rem 0.2rem; }
  .card { padding: 1.2rem 0.7rem 1.2rem 0.7rem; }
  h1 { font-size: 1.5rem; }
} 