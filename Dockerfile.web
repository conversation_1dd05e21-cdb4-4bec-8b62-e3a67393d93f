# Use Debian Bullseye slim as the base image for Flutter build
FROM debian:bullseye-slim AS build

# Install required dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user for Flutter
RUN groupadd -r fluttergroup && useradd -r -g fluttergroup -m -s /sbin/nologin flutteruser

# Download and install Flutter
RUN curl -O https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.5-stable.tar.xz \
    && tar xf flutter_linux_3.24.5-stable.tar.xz -C / \
    && rm flutter_linux_3.24.5-stable.tar.xz

# Set Flutter directory permissions
RUN chown -R flutteruser:fluttergroup /flutter

# Set working directory and ensure proper ownership
WORKDIR /app
RUN chown -R flutteruser:fluttergroup /app

# Configure git for Flutter (as root first)
RUN git config --global --add safe.directory /flutter

# Switch to non-root user for Flutter operations
USER flutteruser

# Configure git for the non-root user
RUN git config --global --add safe.directory /flutter

# Upgrade Flutter and verify installation
RUN /flutter/bin/flutter channel master \
    && /flutter/bin/flutter upgrade \
    && /flutter/bin/flutter doctor

# Add build version for cache busting
RUN echo "Build version: $(date +%s)"

# Copy pubspec files
COPY --chown=flutteruser:fluttergroup pubspec.lock ./
COPY --chown=flutteruser:fluttergroup pubspec.yaml ./

# Get Flutter dependencies
RUN /flutter/bin/flutter pub get

# Copy the rest of the application
COPY --chown=flutteruser:fluttergroup . .

# Build the Flutter web app as non-root user
RUN /flutter/bin/flutter build web --release --no-tree-shake-icons

# Switch back to root for the final stage setup
USER root

# Use Nginx Alpine as the base image for the final stage
FROM nginx:alpine

# Create nginx user with specific UID/GID
RUN adduser -D -H -u 101 -s /sbin/nologin nginx || true

# Install curl for healthcheck
RUN apk add --no-cache curl

# Copy the built Flutter web app
COPY --from=build /app/build/web /usr/share/nginx/html

# Set proper permissions for web root
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Create necessary directories for Nginx
RUN mkdir -p /var/cache/nginx/client_temp /var/cache/nginx/proxy_temp /var/cache/nginx/fastcgi_temp /var/cache/nginx/uwsgi_temp /var/cache/nginx/scgi_temp && \
    chown -R nginx:nginx /var/cache/nginx && \
    chmod -R 700 /var/cache/nginx && \
    mkdir -p /tmp/nginx && \
    chown -R nginx:nginx /tmp/nginx && \
    chmod -R 755 /tmp/nginx

# Copy Nginx configuration
COPY config/nginx/nginx-webapp-internal.conf /etc/nginx/conf.d/default.conf

# Make nginx configuration writable
RUN chown -R nginx:nginx /etc/nginx/conf.d && \
    chmod -R 755 /etc/nginx/conf.d

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]